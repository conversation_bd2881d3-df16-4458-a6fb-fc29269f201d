(()=>{var e={};e.id=5096,e.ids=[5096],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},44012:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{PUT:()=>d});var n=s(42706),i=s(28203),o=s(45994),u=s(39187),a=s(62545),p=s(45369);async function d(e){let r=e.url.split("/").pop();if(!r)return u.NextResponse.json({error:"User ID not found in URL"},{status:400});try{let s=await (0,p.B$)(e);if(s)return s;let{password:t}=await e.json();if(!t)return u.NextResponse.json({error:"Password is required"},{status:400});try{await a.A.query("UPDATE admin_users SET password = ? WHERE admin_id = ?",[t,r])}catch(e){return console.error("Database error:",e),u.NextResponse.json({error:"Database error: "+e.message},{status:500})}return u.NextResponse.json({message:"Password updated successfully"})}catch(e){return console.error("Update password error:",e),u.NextResponse.json({error:"An error occurred while updating password"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/[id]/password/route",pathname:"/api/admin/users/[id]/password",filename:"route",bundlePath:"app/api/admin/users/[id]/password/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\admin\\users\\[id]\\password\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:m}=c;function v(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},45369:(e,r,s)=>{"use strict";s.d(r,{B$:()=>v,Er:()=>c,HU:()=>l,dD:()=>f,nG:()=>m,nr:()=>x});var t=s(5486),n=s.n(t),i=s(43008),o=s.n(i),u=s(44512),a=s(39187),p=s(62545);let d=process.env.JWT_SECRET||"your-secret-key-change-this-in-production";async function c(e){return n().hash(e,10)}function l(e){return o().sign({id:e.admin_id,email:e.email,is_super_admin:e.is_super_admin},d,{expiresIn:"24h"})}function x(e){try{return o().verify(e,d)}catch{return null}}async function m(){let e=(0,u.UL)(),r=(await e).get("admin_token")?.value;if(!r)return null;let s=x(r);if(!s)return null;try{let[e]=await p.A.query("SELECT admin_id, email, full_name, is_super_admin FROM admin_users WHERE admin_id = ?",[s.id]);if(0===e.length)return null;return e[0]}catch(e){return console.error("Error fetching current admin:",e),null}}async function v(e){let r=e.cookies.get("admin_token")?.value;return r?x(r)?null:a.NextResponse.json({error:"Invalid token"},{status:401}):a.NextResponse.json({error:"Unauthorized"},{status:401})}async function f(e){let r=e.cookies.get("admin_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let s=x(r);return s&&s.is_super_admin?null:a.NextResponse.json({error:"Forbidden: Requires super admin privileges"},{status:403})}},62545:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60820),n=s(29021),i=s.n(n),o=s(33873),u=s.n(o);let a=t.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(u().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,5452,820,8935],()=>s(44012));module.exports=t})();