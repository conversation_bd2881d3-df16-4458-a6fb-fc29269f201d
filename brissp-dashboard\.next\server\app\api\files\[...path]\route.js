(()=>{var e={};e.id=4037,e.ids=[4037],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},79748:e=>{"use strict";e.exports=require("fs/promises")},33873:e=>{"use strict";e.exports=require("path")},54833:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d,OPTIONS:()=>u});var i=r(42706),n=r(28203),o=r(45994),a=r(39187),l=r(79748),p=r(33873),c=r(77922);async function d(e,{params:t}){try{let r=(await t).path.join("/"),s=(0,p.join)(process.cwd(),"public/uploads",r);if(!await c.oB.fileExists(s))return a.NextResponse.json({error:"File not found"},{status:404});let i=await (0,l.stat)(s),n=await (0,l.readFile)(s),o={".jpg":"image/jpeg",".jpeg":"image/jpeg",".png":"image/png",".gif":"image/gif",".webp":"image/webp",".pdf":"application/pdf",".doc":"application/msword",".docx":"application/vnd.openxmlformats-officedocument.wordprocessingml.document",".xls":"application/vnd.ms-excel",".xlsx":"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",".txt":"text/plain",".csv":"text/csv"}[r.substring(r.lastIndexOf(".")).toLowerCase()]||"application/octet-stream",d=new a.NextResponse(n);if(d.headers.set("Content-Type",o),d.headers.set("Content-Length",i.size.toString()),d.headers.set("Cache-Control","public, max-age=31536000, immutable"),d.headers.set("Access-Control-Allow-Origin","*"),d.headers.set("Access-Control-Allow-Methods","GET, HEAD, OPTIONS"),d.headers.set("Access-Control-Allow-Headers","Content-Type"),"true"===e.nextUrl.searchParams.get("download")){let e=r.substring(r.lastIndexOf("/")+1);d.headers.set("Content-Disposition",`attachment; filename="${e}"`)}return d}catch(e){return console.error("Error serving file:",e),a.NextResponse.json({error:"Failed to serve file"},{status:500})}}async function u(){return new a.NextResponse(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, HEAD, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}let m=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/files/[...path]/route",pathname:"/api/files/[...path]",filename:"route",bundlePath:"app/api/files/[...path]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\files\\[...path]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:g,serverHooks:x}=m;function h(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{},77922:(e,t,r)=>{"use strict";r.d(t,{oB:()=>f,cy:()=>x});var s=r(79748),i=r(33873),n=r(55511),o=r.n(n);let a=new Uint8Array(256),l=a.length,p=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,c=[];for(let e=0;e<256;++e)c.push((e+256).toString(16).substr(1));let d=function(e,t=0){let r=(c[e[t+0]]+c[e[t+1]]+c[e[t+2]]+c[e[t+3]]+"-"+c[e[t+4]]+c[e[t+5]]+"-"+c[e[t+6]]+c[e[t+7]]+"-"+c[e[t+8]]+c[e[t+9]]+"-"+c[e[t+10]]+c[e[t+11]]+c[e[t+12]]+c[e[t+13]]+c[e[t+14]]+c[e[t+15]]).toLowerCase();if(!("string"==typeof r&&p.test(r)))throw TypeError("Stringified UUID is invalid");return r},u=function(e,t,r){let s=(e=e||{}).random||(e.rng||function(){return l>a.length-16&&(o().randomFillSync(a),l=0),a.slice(l,l+=16)})();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=s[e];return t}return d(s)};class m{constructor(e="public/uploads",t="/uploads"){this.baseDir=e,this.baseUrl=t}validateFile(e,t={}){let{maxSize:r=0xa00000,allowedTypes:s=["image/jpeg","image/png","image/gif","image/webp","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/plain","text/csv"],allowedExtensions:i=[".jpg",".jpeg",".png",".gif",".webp",".pdf",".doc",".docx",".xls",".xlsx",".txt",".csv"]}=t;if(e.size>r)return{valid:!1,error:`File size exceeds maximum allowed size of ${Math.round(r/1048576)}MB`};if(!s.includes(e.type))return{valid:!1,error:`File type ${e.type} is not allowed`};let n=this.getFileExtension(e.name);return i.includes(n.toLowerCase())?{valid:!0}:{valid:!1,error:`File extension ${n} is not allowed`}}async uploadFile(e,t="general"){let r=this.validateFile(e);if(!r.valid)throw Error(r.error);let n=this.getFileExtension(e.name),o=`${u()}${n}`,a=(0,i.join)(this.baseDir,t),l=(0,i.join)(a,o);try{await (0,s.mkdir)(a,{recursive:!0});let r=await e.arrayBuffer(),i=Buffer.from(r);return await (0,s.writeFile)(l,i),{url:`${this.baseUrl}/${t}/${o}`,filename:o,originalName:e.name,size:e.size,mimeType:e.type,path:l}}catch(e){throw console.error("Error uploading file:",e),Error("Failed to upload file")}}async deleteFile(e){try{return await (0,s.unlink)(e),!0}catch(e){return console.error("Error deleting file:",e),!1}}async fileExists(e){try{return await (0,s.access)(e),!0}catch{return!1}}getFileExtension(e){return e.substring(e.lastIndexOf("."))}getFileUrl(e,t){return`${this.baseUrl}/${e}/${t}`}parseFileUrl(e){let t=e.replace(this.baseUrl+"/","").split("/");if(t.length>=2){let e=t.pop();return{folder:t.join("/"),filename:e}}return null}getFilePathFromUrl(e){let t=this.parseFileUrl(e);return t?(0,i.join)(this.baseDir,t.folder,t.filename):null}}let f=new m,g={images:{types:["image/jpeg","image/png","image/gif","image/webp"],extensions:[".jpg",".jpeg",".png",".gif",".webp"]},documents:{types:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/plain","text/csv"],extensions:[".pdf",".doc",".docx",".xls",".xlsx",".txt",".csv"]},all:{types:["image/jpeg","image/png","image/gif","image/webp","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/plain","text/csv"],extensions:[".jpg",".jpeg",".png",".gif",".webp",".pdf",".doc",".docx",".xls",".xlsx",".txt",".csv"]}};function x(e,t){return{allowedTypes:g[e].types,allowedExtensions:g[e].extensions,maxSize:t}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452],()=>r(54833));module.exports=s})();