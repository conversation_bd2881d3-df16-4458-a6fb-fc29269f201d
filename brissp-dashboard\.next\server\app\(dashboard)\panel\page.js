(()=>{var e={};e.id=4875,e.ids=[4875],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},85992:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var n=r(70260),i=r(28203),a=r(25155),s=r.n(a),o=r(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["(dashboard)",{children:["panel",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46881)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\panel\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\panel\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/panel/page",pathname:"/panel",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},87335:(e,t,r)=>{Promise.resolve().then(r.bind(r,46881))},17599:(e,t,r)=>{Promise.resolve().then(r.bind(r,37511))},37511:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ea});var n=r(45512),i=r(58009),a=r.n(i),s=r(97643),o=r(87021),l=r(13393),c=r(77252),d=r(23081),u=r(94199),p=r(14449),h=r(50218),f=r.n(h),m=r(80739),y=r.n(m),x=r(95936),v=r.n(x),b=r(82281),j=r(12599),g=r(13753),A=r(41681),w=r(77414),k=r(1370),P=r(97436),N=r(8057),S=r(28682),O=r(55145),D=["type","layout","connectNulls","ref"],E=["key"];function C(e){return(C="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function R(){return(R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function B(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function T(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?B(Object(r),!0).forEach(function(t){q(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function W(e){return function(e){if(Array.isArray(e))return M(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return M(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return M(e,void 0)}}(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function M(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function I(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,z(n.key),n)}}function Z(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(Z=function(){return!!e})()}function F(e){return(F=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function L(e,t){return(L=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function q(e,t,r){return(t=z(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function z(e){var t=function(e,t){if("object"!=C(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=C(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==C(t)?t:t+""}var G=function(e){var t,r;function n(){!function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}(this,n);for(var e,t,r,i=arguments.length,a=Array(i),s=0;s<i;s++)a[s]=arguments[s];return t=n,r=[].concat(a),t=F(t),q(e=function(e,t){if(t&&("object"===C(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,Z()?Reflect.construct(t,r||[],F(this).constructor):t.apply(this,r)),"state",{isAnimationFinished:!0,totalLength:0}),q(e,"generateSimpleStrokeDasharray",function(e,t){return"".concat(t,"px ").concat(e-t,"px")}),q(e,"getStrokeDasharray",function(t,r,i){var a=i.reduce(function(e,t){return e+t});if(!a)return e.generateSimpleStrokeDasharray(r,t);for(var s=Math.floor(t/a),o=t%a,l=r-t,c=[],d=0,u=0;d<i.length;u+=i[d],++d)if(u+i[d]>o){c=[].concat(W(i.slice(0,d)),[o-u]);break}var p=c.length%2==0?[0,l]:[l];return[].concat(W(n.repeat(i,s)),W(c),p).map(function(e){return"".concat(e,"px")}).join(", ")}),q(e,"id",(0,P.NF)("recharts-line-")),q(e,"pathRef",function(t){e.mainCurve=t}),q(e,"handleAnimationEnd",function(){e.setState({isAnimationFinished:!0}),e.props.onAnimationEnd&&e.props.onAnimationEnd()}),q(e,"handleAnimationStart",function(){e.setState({isAnimationFinished:!1}),e.props.onAnimationStart&&e.props.onAnimationStart()}),e}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&L(e,t)}(n,e),t=[{key:"componentDidMount",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();this.setState({totalLength:e})}}},{key:"componentDidUpdate",value:function(){if(this.props.isAnimationActive){var e=this.getTotalLength();e!==this.state.totalLength&&this.setState({totalLength:e})}}},{key:"getTotalLength",value:function(){var e=this.mainCurve;try{return e&&e.getTotalLength&&e.getTotalLength()||0}catch(e){return 0}}},{key:"renderErrorBar",value:function(e,t){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.points,i=r.xAxis,s=r.yAxis,o=r.layout,l=r.children,c=(0,N.aS)(l,k.u);if(!c)return null;var d=function(e,t){return{x:e.x,y:e.y,value:e.value,errorVal:(0,O.kr)(e.payload,t)}};return a().createElement(A.W,{clipPath:e?"url(#clipPath-".concat(t,")"):null},c.map(function(e){return a().cloneElement(e,{key:"bar-".concat(e.props.dataKey),data:n,xAxis:i,yAxis:s,layout:o,dataPointFormatter:d})}))}},{key:"renderDots",value:function(e,t,r){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var i=this.props,s=i.dot,o=i.points,l=i.dataKey,c=(0,N.J9)(this.props,!1),d=(0,N.J9)(s,!0),u=o.map(function(e,t){var r=T(T(T({key:"dot-".concat(t),r:3},c),d),{},{value:e.value,dataKey:l,cx:e.x,cy:e.y,index:t,payload:e.payload});return n.renderDotItem(s,r)}),p={clipPath:e?"url(#clipPath-".concat(t?"":"dots-").concat(r,")"):null};return a().createElement(A.W,R({className:"recharts-line-dots",key:"dots"},p),u)}},{key:"renderCurveStatically",value:function(e,t,r,n){var i=this.props,s=i.type,o=i.layout,l=i.connectNulls,c=(i.ref,_(i,D)),d=T(T(T({},(0,N.J9)(c,!0)),{},{fill:"none",className:"recharts-line-curve",clipPath:t?"url(#clipPath-".concat(r,")"):null,points:e},n),{},{type:s,layout:o,connectNulls:l});return a().createElement(j.I,R({},d,{pathRef:this.pathRef}))}},{key:"renderCurveWithAnimation",value:function(e,t){var r=this,n=this.props,i=n.points,s=n.strokeDasharray,o=n.isAnimationActive,l=n.animationBegin,c=n.animationDuration,d=n.animationEasing,u=n.animationId,h=n.animateNewValues,f=n.width,m=n.height,y=this.state,x=y.prevPoints,v=y.totalLength;return a().createElement(p.Ay,{begin:l,duration:c,isActive:o,easing:d,from:{t:0},to:{t:1},key:"line-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a,o=n.t;if(x){var l=x.length/i.length,c=i.map(function(e,t){var r=Math.floor(t*l);if(x[r]){var n=x[r],i=(0,P.Dj)(n.x,e.x),a=(0,P.Dj)(n.y,e.y);return T(T({},e),{},{x:i(o),y:a(o)})}if(h){var s=(0,P.Dj)(2*f,e.x),c=(0,P.Dj)(m/2,e.y);return T(T({},e),{},{x:s(o),y:c(o)})}return T(T({},e),{},{x:e.x,y:e.y})});return r.renderCurveStatically(c,e,t)}var d=(0,P.Dj)(0,v)(o);if(s){var u="".concat(s).split(/[,\s]+/gim).map(function(e){return parseFloat(e)});a=r.getStrokeDasharray(d,v,u)}else a=r.generateSimpleStrokeDasharray(v,d);return r.renderCurveStatically(i,e,t,{strokeDasharray:a})})}},{key:"renderCurve",value:function(e,t){var r=this.props,n=r.points,i=r.isAnimationActive,a=this.state,s=a.prevPoints,o=a.totalLength;return i&&n&&n.length&&(!s&&o>0||!v()(s,n))?this.renderCurveWithAnimation(e,t):this.renderCurveStatically(n,e,t)}},{key:"render",value:function(){var e,t=this.props,r=t.hide,n=t.dot,i=t.points,s=t.className,o=t.xAxis,l=t.yAxis,c=t.top,d=t.left,u=t.width,p=t.height,h=t.isAnimationActive,f=t.id;if(r||!i||!i.length)return null;var m=this.state.isAnimationFinished,x=1===i.length,v=(0,b.A)("recharts-line",s),j=o&&o.allowDataOverflow,g=l&&l.allowDataOverflow,k=j||g,P=y()(f)?this.id:f,S=null!==(e=(0,N.J9)(n,!1))&&void 0!==e?e:{r:3,strokeWidth:2},O=S.r,D=S.strokeWidth,E=((0,N.sT)(n)?n:{}).clipDot,C=void 0===E||E,_=2*(void 0===O?3:O)+(void 0===D?2:D);return a().createElement(A.W,{className:v},j||g?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(P)},a().createElement("rect",{x:j?d:d-u/2,y:g?c:c-p/2,width:j?u:2*u,height:g?p:2*p})),!C&&a().createElement("clipPath",{id:"clipPath-dots-".concat(P)},a().createElement("rect",{x:d-_/2,y:c-_/2,width:u+_,height:p+_}))):null,!x&&this.renderCurve(k,P),this.renderErrorBar(k,P),(x||n)&&this.renderDots(k,C,P),(!h||m)&&w.Z.renderCallByParent(this.props,i))}}],r=[{key:"getDerivedStateFromProps",value:function(e,t){return e.animationId!==t.prevAnimationId?{prevAnimationId:e.animationId,curPoints:e.points,prevPoints:t.curPoints}:e.points!==t.curPoints?{curPoints:e.points}:null}},{key:"repeat",value:function(e,t){for(var r=e.length%2!=0?[].concat(W(e),[0]):e,n=[],i=0;i<t;++i)n=[].concat(W(n),W(r));return n}},{key:"renderDotItem",value:function(e,t){var r;if(a().isValidElement(e))r=a().cloneElement(e,t);else if(f()(e))r=e(t);else{var n=t.key,i=_(t,E),s=(0,b.A)("recharts-line-dot","boolean"!=typeof e?e.className:"");r=a().createElement(g.c,R({key:n},i,{className:s}))}return r}}],t&&I(n.prototype,t),r&&I(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(i.PureComponent);q(G,"displayName","Line"),q(G,"defaultProps",{xAxisId:0,yAxisId:0,connectNulls:!1,activeDot:!0,dot:!0,legendType:"line",stroke:"#3182bd",strokeWidth:1,fill:"#fff",points:[],isAnimationActive:!S.m.isSsr,animateNewValues:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",hide:!1,label:!1}),q(G,"getComposedData",function(e){var t=e.props,r=e.xAxis,n=e.yAxis,i=e.xAxisTicks,a=e.yAxisTicks,s=e.dataKey,o=e.bandSize,l=e.displayedData,c=e.offset,d=t.layout;return T({points:l.map(function(e,t){var l=(0,O.kr)(e,s);return"horizontal"===d?{x:(0,O.nb)({axis:r,ticks:i,bandSize:o,entry:e,index:t}),y:y()(l)?null:n.scale(l),value:l,payload:e}:{x:y()(l)?null:r.scale(l),y:(0,O.nb)({axis:n,ticks:a,bandSize:o,entry:e,index:t}),value:l,payload:e}}),layout:d},c)});var K=r(11452),H=r(1263),V=r(6070),J=(0,u.gu)({chartName:"LineChart",GraphicalChild:G,axisComponents:[{axisType:"xAxis",AxisComp:K.W},{axisType:"yAxis",AxisComp:H.h}],formatAxisMap:V.pr}),U=r(26507),X=r(707),$=r(20111),Q=r(39327),Y=r(93346),ee=r(73325);let et=(0,r(94825).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var er=r(28531),en=r.n(er),ei=r(87664);function ea(){let[e,t]=(0,i.useState)(null),[r,a]=(0,i.useState)(!0);return r||!e?(0,n.jsx)("div",{className:"p-6",children:"Loading..."}):(0,n.jsxs)("div",{className:"p-6 space-y-6",children:[(0,n.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,n.jsxs)(s.Zp,{children:[(0,n.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(s.ZB,{className:"text-sm font-medium",children:"Total Students"}),(0,n.jsx)($.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,n.jsx)(s.Wu,{children:(0,n.jsx)("div",{className:"text-2xl font-bold",children:e.totalStudents})})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(s.ZB,{className:"text-sm font-medium",children:"Active Courses"}),(0,n.jsx)(Q.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,n.jsx)(s.Wu,{children:(0,n.jsx)("div",{className:"text-2xl font-bold",children:e.totalCourses})})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(s.ZB,{className:"text-sm font-medium",children:"Total Applications"}),(0,n.jsx)(Y.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,n.jsx)(s.Wu,{children:(0,n.jsx)("div",{className:"text-2xl font-bold",children:e.totalApplications})})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsxs)(s.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsx)(s.ZB,{className:"text-sm font-medium",children:"Pending Applications"}),(0,n.jsx)(ee.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,n.jsx)(s.Wu,{children:(0,n.jsx)("div",{className:"text-2xl font-bold",children:e.pendingApplications})})]})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsx)(s.aR,{children:(0,n.jsx)(s.ZB,{children:"Application Trends"})}),(0,n.jsx)(s.Wu,{children:(0,n.jsx)("div",{className:"h-[300px]",children:(0,n.jsx)(d.u,{width:"100%",height:"100%",children:(0,n.jsxs)(J,{data:e.applicationTrend,children:[(0,n.jsx)(U.d,{strokeDasharray:"3 3"}),(0,n.jsx)(K.W,{dataKey:"date"}),(0,n.jsx)(H.h,{}),(0,n.jsx)(X.m,{}),(0,n.jsx)(G,{type:"monotone",dataKey:"applications",stroke:"#4F46E5",strokeWidth:2})]})})})})]}),(0,n.jsxs)("div",{className:"grid gap-4 md:grid-cols-2",children:[(0,n.jsxs)(s.Zp,{children:[(0,n.jsxs)(s.aR,{className:"flex flex-row items-center justify-between",children:[(0,n.jsx)(s.ZB,{children:"Recent Applications"}),(0,n.jsx)(en(),{href:"/applications",children:(0,n.jsxs)(o.$,{variant:"ghost",size:"sm",children:["View All",(0,n.jsx)(et,{className:"ml-2 h-4 w-4"})]})})]}),(0,n.jsx)(s.Wu,{children:(0,n.jsxs)(l.XI,{children:[(0,n.jsx)(l.A0,{children:(0,n.jsxs)(l.Hj,{children:[(0,n.jsx)(l.nd,{children:"Name"}),(0,n.jsx)(l.nd,{children:"Course"}),(0,n.jsx)(l.nd,{children:"Status"}),(0,n.jsx)(l.nd,{children:"Date"})]})}),(0,n.jsx)(l.BF,{children:e.recentApplications.map(e=>(0,n.jsxs)(l.Hj,{children:[(0,n.jsxs)(l.nA,{children:[e.first_name," ",e.last_name]}),(0,n.jsx)(l.nA,{children:e.course_title}),(0,n.jsx)(l.nA,{children:(0,n.jsx)(c.E,{variant:"approved"===e.status?"default":"rejected"===e.status?"destructive":"pending"===e.status?"secondary":"default",children:e.status})}),(0,n.jsx)(l.nA,{children:(0,ei.A)(new Date(e.application_date),"MMM d, yyyy")})]},e.application_id))})]})})]}),(0,n.jsxs)(s.Zp,{children:[(0,n.jsx)(s.aR,{children:(0,n.jsx)(s.ZB,{children:"Course Statistics"})}),(0,n.jsx)(s.Wu,{children:(0,n.jsxs)(l.XI,{children:[(0,n.jsx)(l.A0,{children:(0,n.jsxs)(l.Hj,{children:[(0,n.jsx)(l.nd,{children:"Course"}),(0,n.jsx)(l.nd,{children:"Students"}),(0,n.jsx)(l.nd,{children:"Pending"})]})}),(0,n.jsx)(l.BF,{children:e.courseStats.map(e=>(0,n.jsxs)(l.Hj,{children:[(0,n.jsx)(l.nA,{children:e.course_title}),(0,n.jsx)(l.nA,{children:e.total_students}),(0,n.jsx)(l.nA,{children:e.pending_applications})]},e.course_title))})]})})]})]})]})}},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>o});var n=r(45512);r(58009);var i=r(21643),a=r(59462);let s=(0,i.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,n.jsx)("div",{className:(0,a.cn)(s({variant:t}),e),...r})}},97643:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>s,aR:()=>o,wL:()=>u});var n=r(45512),i=r(58009),a=r(59462);let s=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));s.displayName="Card";let o=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let u=i.forwardRef(({className:e,...t},r)=>(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},46881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\panel\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\panel\\page.tsx","default")},73325:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(94825).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},20111:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(94825).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,8403,7834,9267,4873,4434,7664,2449],()=>r(85992));module.exports=n})();