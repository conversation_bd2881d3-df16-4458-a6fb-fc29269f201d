"use strict";exports.id=9847,exports.ids=[9847],exports.modules={76142:(e,t,n)=>{n.d(t,{KE:()=>e5,YY:()=>em,CU:()=>eh,bP:()=>e7,gk:()=>X,iI:()=>j,BQ:()=>eH,OX:()=>e8,Zc:()=>te,KV:()=>_,jT:()=>e4,JJ:()=>e6,tG:()=>e9});var r=n(53786),i=n(45966),o=n(56884),s=n(88434),l=n(8789);let a=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function h(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("backward",e):!(n.parentOffset>0))?n:null}let d=(e,t,n)=>{let i=h(e,n);if(!i)return!1;let o=g(i);if(!o){let n=i.blockRange(),r=n&&(0,l.jP)(n);return null!=r&&(t&&t(e.tr.lift(n,r).scrollIntoView()),!0)}let a=o.nodeBefore;if(A(e,o,t,-1))return!0;if(0==i.parent.content.size&&(f(a,"end")||r.nh.isSelectable(a)))for(let n=i.depth;;n--){let h=(0,l.$L)(e.doc,i.before(n),i.after(n),s.Ji.empty);if(h&&h.slice.size<h.to-h.from){if(t){let n=e.tr.step(h);n.setSelection(f(a,"end")?r.LN.findFrom(n.doc.resolve(n.mapping.map(o.pos,-1)),-1):r.nh.create(n.doc,o.pos-a.nodeSize)),t(n.scrollIntoView())}return!0}if(1==n||i.node(n-1).childCount>1)break}return!!a.isAtom&&o.depth==i.depth-1&&(t&&t(e.tr.delete(o.pos-a.nodeSize,o.pos).scrollIntoView()),!0)},c=(e,t,n)=>{let r=h(e,n);if(!r)return!1;let i=g(r);return!!i&&u(e,i,t)},p=(e,t,n)=>{let r=y(e,n);if(!r)return!1;let i=w(r);return!!i&&u(e,i,t)};function u(e,t,n){let i=t.nodeBefore,o=t.pos-1;for(;!i.isTextblock;o--){if(i.type.spec.isolating)return!1;let e=i.lastChild;if(!e)return!1;i=e}let a=t.nodeAfter,h=t.pos+1;for(;!a.isTextblock;h++){if(a.type.spec.isolating)return!1;let e=a.firstChild;if(!e)return!1;a=e}let d=(0,l.$L)(e.doc,o,h,s.Ji.empty);if(!d||d.from!=o||d instanceof l.Ln&&d.slice.size>=h-o)return!1;if(n){let t=e.tr.step(d);t.setSelection(r.U3.create(t.doc,o)),n(t.scrollIntoView())}return!0}function f(e,t,n=!1){for(let r=e;r;r="start"==t?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)break}return!1}let m=(e,t,n)=>{let{$head:i,empty:o}=e.selection,s=i;if(!o)return!1;if(i.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):i.parentOffset>0)return!1;s=g(i)}let l=s&&s.nodeBefore;return!!(l&&r.nh.isSelectable(l))&&(t&&t(e.tr.setSelection(r.nh.create(e.doc,s.pos-l.nodeSize)).scrollIntoView()),!0)};function g(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function y(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("forward",e):!(n.parentOffset<n.parent.content.size))?n:null}let v=(e,t,n)=>{let i=y(e,n);if(!i)return!1;let o=w(i);if(!o)return!1;let a=o.nodeAfter;if(A(e,o,t,1))return!0;if(0==i.parent.content.size&&(f(a,"start")||r.nh.isSelectable(a))){let n=(0,l.$L)(e.doc,i.before(),i.after(),s.Ji.empty);if(n&&n.slice.size<n.to-n.from){if(t){let i=e.tr.step(n);i.setSelection(f(a,"start")?r.LN.findFrom(i.doc.resolve(i.mapping.map(o.pos)),1):r.nh.create(i.doc,i.mapping.map(o.pos))),t(i.scrollIntoView())}return!0}}return!!a.isAtom&&o.depth==i.depth-1&&(t&&t(e.tr.delete(o.pos,o.pos+a.nodeSize).scrollIntoView()),!0)},b=(e,t,n)=>{let{$head:i,empty:o}=e.selection,s=i;if(!o)return!1;if(i.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):i.parentOffset<i.parent.content.size)return!1;s=w(i)}let l=s&&s.nodeAfter;return!!(l&&r.nh.isSelectable(l))&&(t&&t(e.tr.setSelection(r.nh.create(e.doc,s.pos)).scrollIntoView()),!0)};function w(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}let k=(e,t)=>{let n=e.selection,i=n instanceof r.nh,o;if(i){if(n.node.isTextblock||!(0,l.n9)(e.doc,n.from))return!1;o=n.from}else if(null==(o=(0,l.N0)(e.doc,n.from,-1)))return!1;if(t){let n=e.tr.join(o);i&&n.setSelection(r.nh.create(n.doc,o-e.doc.resolve(o).nodeBefore.nodeSize)),t(n.scrollIntoView())}return!0},x=(e,t)=>{let n=e.selection,i;if(n instanceof r.nh){if(n.node.isTextblock||!(0,l.n9)(e.doc,n.to))return!1;i=n.to}else if(null==(i=(0,l.N0)(e.doc,n.to,1)))return!1;return t&&t(e.tr.join(i).scrollIntoView()),!0},S=(e,t)=>{let{$from:n,$to:r}=e.selection,i=n.blockRange(r),o=i&&(0,l.jP)(i);return null!=o&&(t&&t(e.tr.lift(i,o).scrollIntoView()),!0)},M=(e,t)=>{let{$head:n,$anchor:r}=e.selection;return!!(n.parent.type.spec.code&&n.sameParent(r))&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function C(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let O=(e,t)=>{let{$head:n,$anchor:i}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(i))return!1;let o=n.node(-1),s=n.indexAfter(-1),l=C(o.contentMatchAt(s));if(!l||!o.canReplaceWith(s,s,l))return!1;if(t){let i=n.after(),o=e.tr.replaceWith(i,i,l.createAndFill());o.setSelection(r.LN.near(o.doc.resolve(i),1)),t(o.scrollIntoView())}return!0},T=(e,t)=>{let n=e.selection,{$from:i,$to:o}=n;if(n instanceof r.i5||i.parent.inlineContent||o.parent.inlineContent)return!1;let s=C(o.parent.contentMatchAt(o.indexAfter()));if(!s||!s.isTextblock)return!1;if(t){let n=(!i.parentOffset&&o.index()<o.parent.childCount?i:o).pos,l=e.tr.insert(n,s.createAndFill());l.setSelection(r.U3.create(l.doc,n+1)),t(l.scrollIntoView())}return!0},N=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if((0,l.zy)(e.doc,r))return t&&t(e.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),i=r&&(0,l.jP)(r);return null!=i&&(t&&t(e.tr.lift(r,i).scrollIntoView()),!0)},E=(e,t)=>{let{$from:n,to:i}=e.selection,o,s=n.sharedDepth(i);return 0!=s&&(o=n.before(s),t&&t(e.tr.setSelection(r.nh.create(e.doc,o))),!0)};function A(e,t,n,i){let o,a,h,d=t.nodeBefore,c=t.nodeAfter,p,u,m=d.type.spec.isolating||c.type.spec.isolating;if(!m&&(o=t.nodeBefore,a=t.nodeAfter,h=t.index(),o&&a&&o.type.compatibleContent(a.type)&&(!o.content.size&&t.parent.canReplace(h-1,h)?(n&&n(e.tr.delete(t.pos-o.nodeSize,t.pos).scrollIntoView()),!0):!!(t.parent.canReplace(h,h+1)&&(a.isTextblock||(0,l.n9)(e.doc,t.pos)))&&(n&&n(e.tr.join(t.pos).scrollIntoView()),!0))))return!0;let g=!m&&t.parent.canReplace(t.index(),t.index()+1);if(g&&(p=(u=d.contentMatchAt(d.childCount)).findWrapping(c.type))&&u.matchType(p[0]||c.type).validEnd){if(n){let r=t.pos+c.nodeSize,i=s.FK.empty;for(let e=p.length-1;e>=0;e--)i=s.FK.from(p[e].create(null,i));i=s.FK.from(d.copy(i));let o=e.tr.step(new l.Wg(t.pos-1,r,t.pos,r,new s.Ji(i,1,0),p.length,!0)),a=o.doc.resolve(r+2*p.length);a.nodeAfter&&a.nodeAfter.type==d.type&&(0,l.n9)(o.doc,a.pos)&&o.join(a.pos),n(o.scrollIntoView())}return!0}let y=c.type.spec.isolating||i>0&&m?null:r.LN.findFrom(t,1),v=y&&y.$from.blockRange(y.$to),b=v&&(0,l.jP)(v);if(null!=b&&b>=t.depth)return n&&n(e.tr.lift(v,b).scrollIntoView()),!0;if(g&&f(c,"start",!0)&&f(d,"end")){let r=d,i=[];for(;i.push(r),!r.isTextblock;)r=r.lastChild;let o=c,a=1;for(;!o.isTextblock;o=o.firstChild)a++;if(r.canReplace(r.childCount,r.childCount,o.content)){if(n){let r=s.FK.empty;for(let e=i.length-1;e>=0;e--)r=s.FK.from(i[e].copy(r));n(e.tr.step(new l.Wg(t.pos-i.length,t.pos+c.nodeSize,t.pos+a,t.pos+c.nodeSize-a,new s.Ji(r,i.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function D(e){return function(t,n){let i=t.selection,o=e<0?i.$from:i.$to,s=o.depth;for(;o.node(s).isInline;){if(!s)return!1;s--}return!!o.node(s).isTextblock&&(n&&n(t.tr.setSelection(r.U3.create(t.doc,e<0?o.start(s):o.end(s)))),!0)}}let I=D(-1),R=D(1);function P(e,t=null){return function(n,r){let i=!1;for(let r=0;r<n.selection.ranges.length&&!i;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];n.doc.nodesBetween(o,s,(r,o)=>{if(i)return!1;if(!(!r.isTextblock||r.hasMarkup(e,t))){if(r.type==e)i=!0;else{let t=n.doc.resolve(o),r=t.index();i=t.parent.canReplaceWith(r,r+1,e)}}})}if(!i)return!1;if(r){let i=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];i.setBlockType(o,s,e,t)}r(i.scrollIntoView())}return!0}}function z(...e){return function(t,n,r){for(let i=0;i<e.length;i++)if(e[i](t,n,r))return!0;return!1}}let B=z(a,d,m),F=z(a,v,b),L={Enter:z(M,T,N,(e,t)=>{let{$from:n,$to:i}=e.selection;if(e.selection instanceof r.nh&&e.selection.node.isBlock)return!!(n.parentOffset&&(0,l.zy)(e.doc,n.pos))&&(t&&t(e.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let o=[],s,a,h=!1,d=!1;for(let e=n.depth;;e--){if(n.node(e).isBlock){let t;h=n.end(e)==n.pos+(n.depth-e),d=n.start(e)==n.pos-(n.depth-e),a=C(n.node(e-1).contentMatchAt(n.indexAfter(e-1))),o.unshift(t||(h&&a?{type:a}:null)),s=e;break}if(1==e)return!1;o.unshift(null)}let c=e.tr;(e.selection instanceof r.U3||e.selection instanceof r.i5)&&c.deleteSelection();let p=c.mapping.map(n.pos),u=(0,l.zy)(c.doc,p,o.length,o);if(u||(o[0]=a?{type:a}:null,u=(0,l.zy)(c.doc,p,o.length,o)),c.split(p,o.length,o),!h&&d&&n.node(s).type!=a){let e=c.mapping.map(n.before(s)),t=c.doc.resolve(e);a&&n.node(s-1).canReplaceWith(t.index(),t.index()+1,a)&&c.setNodeMarkup(c.mapping.map(n.before(s)),a)}return t&&t(c.scrollIntoView()),!0}),"Mod-Enter":O,Backspace:B,"Mod-Backspace":B,"Shift-Backspace":B,Delete:F,"Mod-Delete":F,"Mod-a":(e,t)=>(t&&t(e.tr.setSelection(new r.i5(e.doc))),!0)},$={"Ctrl-h":L.Backspace,"Alt-Backspace":L["Mod-Backspace"],"Ctrl-d":L.Delete,"Ctrl-Alt-Backspace":L["Mod-Delete"],"Alt-Delete":L["Mod-Delete"],"Alt-d":L["Mod-Delete"],"Ctrl-a":I,"Ctrl-e":R};for(let e in L)$[e]=L[e];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();function V(e){let{state:t,transaction:n}=e,{selection:r}=n,{doc:i}=n,{storedMarks:o}=n;return{...t,apply:t.apply.bind(t),applyTransaction:t.applyTransaction.bind(t),plugins:t.plugins,schema:t.schema,reconfigure:t.reconfigure.bind(t),toJSON:t.toJSON.bind(t),get storedMarks(){return o},get selection(){return r},get doc(){return i},get tr(){return r=n.selection,i=n.doc,o=n.storedMarks,n}}}class J{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:e,editor:t,state:n}=this,{view:r}=t,{tr:i}=n,o=this.buildProps(i);return Object.fromEntries(Object.entries(e).map(([e,t])=>[e,(...e)=>{let n=t(...e)(o);return i.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(i),n}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){let{rawCommands:n,editor:r,state:i}=this,{view:o}=r,s=[],l=!!e,a=e||i.tr,h={...Object.fromEntries(Object.entries(n).map(([e,n])=>[e,(...e)=>{let r=this.buildProps(a,t),i=n(...e)(r);return s.push(i),h}])),run:()=>(l||!t||a.getMeta("preventDispatch")||this.hasCustomState||o.dispatch(a),s.every(e=>!0===e))};return h}createCan(e){let{rawCommands:t,state:n}=this,r=e||n.tr,i=this.buildProps(r,!1);return{...Object.fromEntries(Object.entries(t).map(([e,t])=>[e,(...e)=>t(...e)({...i,dispatch:void 0})])),chain:()=>this.createChain(r,!1)}}buildProps(e,t=!0){let{rawCommands:n,editor:r,state:i}=this,{view:o}=r,s={tr:e,editor:r,view:o,state:V({state:i,transaction:e}),dispatch:t?()=>void 0:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map(([e,t])=>[e,(...e)=>t(...e)(s)]))}};return s}}class K{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){let n=this.callbacks[e];return n&&n.forEach(e=>e.apply(this,t)),this}off(e,t){let n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(e=>e!==t):delete this.callbacks[e]),this}once(e,t){let n=(...r)=>{this.off(e,n),t.apply(this,r)};return this.on(e,n)}removeAllListeners(){this.callbacks={}}}function j(e,t,n){return void 0===e.config[t]&&e.parent?j(e.parent,t,n):"function"==typeof e.config[t]?e.config[t].bind({...n,parent:e.parent?j(e.parent,t,n):null}):e.config[t]}function H(e){return{baseExtensions:e.filter(e=>"extension"===e.type),nodeExtensions:e.filter(e=>"node"===e.type),markExtensions:e.filter(e=>"mark"===e.type)}}function W(e){let t=[],{nodeExtensions:n,markExtensions:r}=H(e),i=[...n,...r],o={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return e.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage,extensions:i},r=j(e,"addGlobalAttributes",n);r&&r().forEach(e=>{e.types.forEach(n=>{Object.entries(e.attributes).forEach(([e,r])=>{t.push({type:n,name:e,attribute:{...o,...r}})})})})}),i.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage},r=j(e,"addAttributes",n);r&&Object.entries(r()).forEach(([n,r])=>{let i={...o,...r};"function"==typeof(null==i?void 0:i.default)&&(i.default=i.default()),(null==i?void 0:i.isRequired)&&(null==i?void 0:i.default)===void 0&&delete i.default,t.push({type:e.name,name:n,attribute:i})})}),t}function q(e,t){if("string"==typeof e){if(!t.nodes[e])throw Error(`There is no node type named '${e}'. Maybe you forgot to add the extension?`);return t.nodes[e]}return e}function _(...e){return e.filter(e=>!!e).reduce((e,t)=>{let n={...e};return Object.entries(t).forEach(([e,t])=>{if(!n[e]){n[e]=t;return}if("class"===e){let r=t?String(t).split(" "):[],i=n[e]?n[e].split(" "):[],o=r.filter(e=>!i.includes(e));n[e]=[...i,...o].join(" ")}else if("style"===e){let r=t?t.split(";").map(e=>e.trim()).filter(Boolean):[],i=n[e]?n[e].split(";").map(e=>e.trim()).filter(Boolean):[],o=new Map;i.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());o.set(t,n)}),r.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());o.set(t,n)}),n[e]=Array.from(o.entries()).map(([e,t])=>`${e}: ${t}`).join("; ")}else n[e]=t}),n},{})}function U(e,t){return t.filter(t=>t.type===e.type.name).filter(e=>e.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(e.attrs)||{}:{[t.name]:e.attrs[t.name]}).reduce((e,t)=>_(e,t),{})}function G(e){return"function"==typeof e}function X(e,t,...n){return G(e)?t?e.bind(t)(...n):e(...n):e}function Y(e,t){return"style"in e?e:{...e,getAttrs:n=>{let r=e.getAttrs?e.getAttrs(n):e.attrs;if(!1===r)return!1;let i=t.reduce((e,t)=>{var r;let i=t.attribute.parseHTML?t.attribute.parseHTML(n):"string"!=typeof(r=n.getAttribute(t.name))?r:r.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(r):"true"===r||"false"!==r&&r;return null==i?e:{...e,[t.name]:i}},{});return{...r,...i}}}}function Z(e){return Object.fromEntries(Object.entries(e).filter(([e,t])=>!("attrs"===e&&function(e={}){return 0===Object.keys(e).length&&e.constructor===Object}(t))&&null!=t))}function Q(e,t){return t.nodes[e]||t.marks[e]||null}function ee(e,t){return Array.isArray(t)?t.some(t=>("string"==typeof t?t:t.name)===e.name):t}function et(e,t){let n=s.ZF.fromSchema(t).serializeFragment(e),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}let en=(e,t=500)=>{let n="",r=e.parentOffset;return e.parent.nodesBetween(Math.max(0,r-t),r,(e,t,i,o)=>{var s,l;let a=(null===(l=(s=e.type.spec).toText)||void 0===l?void 0:l.call(s,{node:e,pos:t,parent:i,index:o}))||e.textContent||"%leaf%";n+=e.isAtom&&!e.isText?a:a.slice(0,Math.max(0,r-t))}),n};function er(e){return"[object RegExp]"===Object.prototype.toString.call(e)}class ei{constructor(e){this.find=e.find,this.handler=e.handler}}let eo=(e,t)=>{if(er(t))return t.exec(e);let n=t(e);if(!n)return null;let r=[n.text];return r.index=n.index,r.input=e,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r};function es(e){var t;let{editor:n,from:r,to:i,text:o,rules:s,plugin:l}=e,{view:a}=n;if(a.composing)return!1;let h=a.state.doc.resolve(r);if(h.parent.type.spec.code||(null===(t=h.nodeBefore||h.nodeAfter)||void 0===t?void 0:t.marks.find(e=>e.type.spec.code)))return!1;let d=!1,c=en(h)+o;return s.forEach(e=>{if(d)return;let t=eo(c,e.find);if(!t)return;let s=a.state.tr,h=V({state:a.state,transaction:s}),p={from:r-(t[0].length-o.length),to:i},{commands:u,chain:f,can:m}=new J({editor:n,state:h});null!==e.handler({state:h,range:p,match:t,commands:u,chain:f,can:m})&&s.steps.length&&(s.setMeta(l,{transform:s,from:r,to:i,text:o}),a.dispatch(s),d=!0)}),d}function el(e){return"Object"===Object.prototype.toString.call(e).slice(8,-1)&&e.constructor===Object&&Object.getPrototypeOf(e)===Object.prototype}function ea(e,t){let n={...e};return el(e)&&el(t)&&Object.keys(t).forEach(r=>{el(t[r])&&el(e[r])?n[r]=ea(e[r],t[r]):n[r]=t[r]}),n}class eh{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=X(j(this,"addOptions",{name:this.name}))),this.storage=X(j(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new eh(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>ea(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new eh(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=X(j(t,"addOptions",{name:t.name})),t.storage=X(j(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){let{tr:n}=e.state,r=e.state.selection.$from;if(r.pos===r.end()){let i=r.marks();if(!i.find(e=>(null==e?void 0:e.type.name)===t.name))return!1;let o=i.find(e=>(null==e?void 0:e.type.name)===t.name);return o&&n.removeStoredMark(o),n.insertText(" ",r.pos),e.view.dispatch(n),!0}return!1}}class ed{constructor(e){this.find=e.find,this.handler=e.handler}}let ec=(e,t,n)=>{if(er(t))return[...e.matchAll(t)];let r=t(e,n);return r?r.map(t=>{let n=[t.text];return n.index=t.index,n.input=e,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(t.replaceWith)),n}):[]},ep=null,eu=e=>{var t;let n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null===(t=n.clipboardData)||void 0===t||t.setData("text/html",e),n};class ef{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=ef.resolve(e),this.schema=function(e,t){var n;let r=W(e),{nodeExtensions:i,markExtensions:o}=H(e),l=null===(n=i.find(e=>j(e,"topNode")))||void 0===n?void 0:n.name,a=Object.fromEntries(i.map(n=>{let i=r.filter(e=>e.type===n.name),o={name:n.name,options:n.options,storage:n.storage,editor:t},s=Z({...e.reduce((e,t)=>{let r=j(t,"extendNodeSchema",o);return{...e,...r?r(n):{}}},{}),content:X(j(n,"content",o)),marks:X(j(n,"marks",o)),group:X(j(n,"group",o)),inline:X(j(n,"inline",o)),atom:X(j(n,"atom",o)),selectable:X(j(n,"selectable",o)),draggable:X(j(n,"draggable",o)),code:X(j(n,"code",o)),whitespace:X(j(n,"whitespace",o)),linebreakReplacement:X(j(n,"linebreakReplacement",o)),defining:X(j(n,"defining",o)),isolating:X(j(n,"isolating",o)),attrs:Object.fromEntries(i.map(e=>{var t;return[e.name,{default:null===(t=null==e?void 0:e.attribute)||void 0===t?void 0:t.default}]}))}),l=X(j(n,"parseHTML",o));l&&(s.parseDOM=l.map(e=>Y(e,i)));let a=j(n,"renderHTML",o);a&&(s.toDOM=e=>a({node:e,HTMLAttributes:U(e,i)}));let h=j(n,"renderText",o);return h&&(s.toText=h),[n.name,s]})),h=Object.fromEntries(o.map(n=>{let i=r.filter(e=>e.type===n.name),o={name:n.name,options:n.options,storage:n.storage,editor:t},s=Z({...e.reduce((e,t)=>{let r=j(t,"extendMarkSchema",o);return{...e,...r?r(n):{}}},{}),inclusive:X(j(n,"inclusive",o)),excludes:X(j(n,"excludes",o)),group:X(j(n,"group",o)),spanning:X(j(n,"spanning",o)),code:X(j(n,"code",o)),attrs:Object.fromEntries(i.map(e=>{var t;return[e.name,{default:null===(t=null==e?void 0:e.attribute)||void 0===t?void 0:t.default}]}))}),l=X(j(n,"parseHTML",o));l&&(s.parseDOM=l.map(e=>Y(e,i)));let a=j(n,"renderHTML",o);return a&&(s.toDOM=e=>a({mark:e,HTMLAttributes:U(e,i)})),[n.name,s]}));return new s.Sj({topNode:l,nodes:a,marks:h})}(this.extensions,t),this.setupExtensions()}static resolve(e){var t;let n=ef.sort(ef.flatten(e)),r=Array.from(new Set((t=n.map(e=>e.name)).filter((e,n)=>t.indexOf(e)!==n)));return r.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${r.map(e=>`'${e}'`).join(", ")}]. This can lead to issues.`),n}static flatten(e){return e.map(e=>{let t={name:e.name,options:e.options,storage:e.storage},n=j(e,"addExtensions",t);return n?[e,...this.flatten(n())]:e}).flat(10)}static sort(e){return e.sort((e,t)=>{let n=j(e,"priority")||100,r=j(t,"priority")||100;return n>r?-1:n<r?1:0})}get commands(){return this.extensions.reduce((e,t)=>{let n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:Q(t.name,this.schema)},r=j(t,"addCommands",n);return r?{...e,...r()}:e},{})}get plugins(){let{editor:e}=this,t=ef.sort([...this.extensions].reverse()),n=[],i=[],l=t.map(t=>{let r={name:t.name,options:t.options,storage:t.storage,editor:e,type:Q(t.name,this.schema)},s=[],l=j(t,"addKeyboardShortcuts",r),a={};if("mark"===t.type&&j(t,"exitable",r)&&(a.ArrowRight=()=>eh.handleExit({editor:e,mark:t})),l){let t=Object.fromEntries(Object.entries(l()).map(([t,n])=>[t,()=>n({editor:e})]));a={...a,...t}}let h=(0,o.w)(a);s.push(h);let d=j(t,"addInputRules",r);ee(t,e.options.enableInputRules)&&d&&n.push(...d());let c=j(t,"addPasteRules",r);ee(t,e.options.enablePasteRules)&&c&&i.push(...c());let p=j(t,"addProseMirrorPlugins",r);if(p){let e=p();s.push(...e)}return s}).flat();return[function(e){let{editor:t,rules:n}=e,i=new r.k_({state:{init:()=>null,apply(e,r,o){let l=e.getMeta(i);if(l)return l;let a=e.getMeta("applyInputRules");return a&&setTimeout(()=>{let{text:e}=a;"string"==typeof e||(e=et(s.FK.from(e),o.schema));let{from:r}=a,l=r+e.length;es({editor:t,from:r,to:l,text:e,rules:n,plugin:i})}),e.selectionSet||e.docChanged?null:r}},props:{handleTextInput:(e,r,o,s)=>es({editor:t,from:r,to:o,text:s,rules:n,plugin:i}),handleDOMEvents:{compositionend:e=>(setTimeout(()=>{let{$cursor:r}=e.state.selection;r&&es({editor:t,from:r.pos,to:r.pos,text:"",rules:n,plugin:i})}),!1)},handleKeyDown(e,r){if("Enter"!==r.key)return!1;let{$cursor:o}=e.state.selection;return!!o&&es({editor:t,from:o.pos,to:o.pos,text:"\n",rules:n,plugin:i})}},isInputRules:!0});return i}({editor:e,rules:n}),...function(e){let t;let{editor:n,rules:i}=e,o=null,l=!1,a=!1,h="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}let d=({state:e,from:r,to:i,rule:o,pasteEvt:s})=>{let l=e.tr;if(function(e){let{editor:t,state:n,from:r,to:i,rule:o,pasteEvent:s,dropEvent:l}=e,{commands:a,chain:h,can:d}=new J({editor:t,state:n}),c=[];return n.doc.nodesBetween(r,i,(e,t)=>{if(!e.isTextblock||e.type.spec.code)return;let p=Math.max(r,t),u=Math.min(i,t+e.content.size);ec(e.textBetween(p-t,u-t,void 0,"￼"),o.find,s).forEach(e=>{if(void 0===e.index)return;let t=p+e.index+1,r=t+e[0].length,i={from:n.tr.mapping.map(t),to:n.tr.mapping.map(r)},u=o.handler({state:n,range:i,match:e,commands:a,chain:h,can:d,pasteEvent:s,dropEvent:l});c.push(u)})}),c.every(e=>null!==e)}({editor:n,state:V({state:e,transaction:l}),from:Math.max(r-1,0),to:i.b-1,rule:o,pasteEvent:s,dropEvent:t})&&l.steps.length){try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}return h="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,l}};return i.map(e=>new r.k_({view(e){let t=t=>{var r;(o=(null===(r=e.dom.parentElement)||void 0===r?void 0:r.contains(t.target))?e.dom.parentElement:null)&&(ep=n)},r=()=>{ep&&(ep=null)};return window.addEventListener("dragstart",t),window.addEventListener("dragend",r),{destroy(){window.removeEventListener("dragstart",t),window.removeEventListener("dragend",r)}}},props:{handleDOMEvents:{drop:(e,n)=>{if(a=o===e.dom.parentElement,t=n,!a){let e=ep;e&&setTimeout(()=>{let t=e.state.selection;t&&e.commands.deleteRange({from:t.from,to:t.to})},10)}return!1},paste:(e,t)=>{var n;let r=null===(n=t.clipboardData)||void 0===n?void 0:n.getData("text/html");return h=t,l=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(t,n,r)=>{let i=t[0],o="paste"===i.getMeta("uiEvent")&&!l,c="drop"===i.getMeta("uiEvent")&&!a,p=i.getMeta("applyPasteRules"),u=!!p;if(!o&&!c&&!u)return;if(u){let{text:t}=p;"string"==typeof t||(t=et(s.FK.from(t),r.schema));let{from:n}=p,i=n+t.length;return d({rule:e,state:r,from:n,to:{b:i},pasteEvt:eu(t)})}let f=n.doc.content.findDiffStart(r.doc.content),m=n.doc.content.findDiffEnd(r.doc.content);if("number"==typeof f&&m&&f!==m.b)return d({rule:e,state:r,from:f,to:m,pasteEvt:h})}}))}({editor:e,rules:i}),...l]}get attributes(){return W(this.extensions)}get nodeViews(){let{editor:e}=this,{nodeExtensions:t}=H(this.extensions);return Object.fromEntries(t.filter(e=>!!j(e,"addNodeView")).map(t=>{let n=this.attributes.filter(e=>e.type===t.name),r={name:t.name,options:t.options,storage:t.storage,editor:e,type:q(t.name,this.schema)},i=j(t,"addNodeView",r);return i?[t.name,(r,o,s,l,a)=>{let h=U(r,n);return i()({node:r,view:o,getPos:s,decorations:l,innerDecorations:a,editor:e,extension:t,HTMLAttributes:h})}]:[]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;let n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:Q(e.name,this.schema)};"mark"===e.type&&(null===(t=X(j(e,"keepOnSplit",n)))||void 0===t||t)&&this.splittableMarks.push(e.name);let r=j(e,"onBeforeCreate",n),i=j(e,"onCreate",n),o=j(e,"onUpdate",n),s=j(e,"onSelectionUpdate",n),l=j(e,"onTransaction",n),a=j(e,"onFocus",n),h=j(e,"onBlur",n),d=j(e,"onDestroy",n);r&&this.editor.on("beforeCreate",r),i&&this.editor.on("create",i),o&&this.editor.on("update",o),s&&this.editor.on("selectionUpdate",s),l&&this.editor.on("transaction",l),a&&this.editor.on("focus",a),h&&this.editor.on("blur",h),d&&this.editor.on("destroy",d)})}}class em{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=X(j(this,"addOptions",{name:this.name}))),this.storage=X(j(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new em(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>ea(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new em({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=X(j(t,"addOptions",{name:t.name})),t.storage=X(j(t,"addStorage",{name:t.name,options:t.options})),t}}function eg(e,t,n){let{from:r,to:i}=t,{blockSeparator:o="\n\n",textSerializers:s={}}=n||{},l="";return e.nodesBetween(r,i,(e,n,a,h)=>{var d;e.isBlock&&n>r&&(l+=o);let c=null==s?void 0:s[e.type.name];if(c)return a&&(l+=c({node:e,pos:n,parent:a,index:h,range:t})),!1;e.isText&&(l+=null===(d=null==e?void 0:e.text)||void 0===d?void 0:d.slice(Math.max(r,n)-n,i-n))}),l}function ey(e){return Object.fromEntries(Object.entries(e.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}let ev=em.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new r.k_({key:new r.hs("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:e}=this,{state:t,schema:n}=e,{doc:r,selection:i}=t,{ranges:o}=i,s=Math.min(...o.map(e=>e.$from.pos)),l=Math.max(...o.map(e=>e.$to.pos)),a=ey(n);return eg(r,{from:s,to:l},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}});function eb(e,t,n={strict:!0}){let r=Object.keys(t);return!r.length||r.every(r=>n.strict?t[r]===e[r]:er(t[r])?t[r].test(e[r]):t[r]===e[r])}function ew(e,t,n={}){return e.find(e=>e.type===t&&eb(Object.fromEntries(Object.keys(n).map(t=>[t,e.attrs[t]])),n))}function ek(e,t,n={}){return!!ew(e,t,n)}function ex(e,t,n){var r;if(!e||!t)return;let i=e.parent.childAfter(e.parentOffset);if(i.node&&i.node.marks.some(e=>e.type===t)||(i=e.parent.childBefore(e.parentOffset)),!i.node||!i.node.marks.some(e=>e.type===t)||(n=n||(null===(r=i.node.marks[0])||void 0===r?void 0:r.attrs),!ew([...i.node.marks],t,n)))return;let o=i.index,s=e.start()+i.offset,l=o+1,a=s+i.node.nodeSize;for(;o>0&&ek([...e.parent.child(o-1).marks],t,n);)o-=1,s-=e.parent.child(o).nodeSize;for(;l<e.parent.childCount&&ek([...e.parent.child(l).marks],t,n);)a+=e.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function eS(e,t){if("string"==typeof e){if(!t.marks[e])throw Error(`There is no mark type named '${e}'. Maybe you forgot to add the extension?`);return t.marks[e]}return e}function eM(e){return e instanceof r.U3}function eC(e=0,t=0,n=0){return Math.min(Math.max(e,t),n)}function eO(e,t=null){if(!t)return null;let n=r.LN.atStart(e),i=r.LN.atEnd(e);if("start"===t||!0===t)return n;if("end"===t)return i;let o=n.from,s=i.to;return"all"===t?r.U3.create(e,eC(0,o,s),eC(e.content.size,o,s)):r.U3.create(e,eC(t,o,s),eC(t,o,s))}function eT(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}let eN=e=>{let t=e.childNodes;for(let n=t.length-1;n>=0;n-=1){let r=t[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?e.removeChild(r):1===r.nodeType&&eN(r)}return e};function eE(e){let t=`<body>${e}</body>`;return eN(new window.DOMParser().parseFromString(t,"text/html").body)}function eA(e,t,n){if(e instanceof s.bP||e instanceof s.FK)return e;n={slice:!0,parseOptions:{},...n};let r="object"==typeof e&&null!==e,i="string"==typeof e;if(r)try{if(Array.isArray(e)&&e.length>0)return s.FK.fromArray(e.map(e=>t.nodeFromJSON(e)));let r=t.nodeFromJSON(e);return n.errorOnInvalidContent&&r.check(),r}catch(r){if(n.errorOnInvalidContent)throw Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",e,"Error:",r),eA("",t,n)}if(i){if(n.errorOnInvalidContent){let r=!1,i="",o=new s.Sj({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:e=>(r=!0,i="string"==typeof e?e:e.outerHTML,null)}]}})});if(n.slice?s.S4.fromSchema(o).parseSlice(eE(e),n.parseOptions):s.S4.fromSchema(o).parse(eE(e),n.parseOptions),n.errorOnInvalidContent&&r)throw Error("[tiptap error]: Invalid HTML content",{cause:Error(`Invalid element found: ${i}`)})}let r=s.S4.fromSchema(t);return n.slice?r.parseSlice(eE(e),n.parseOptions).content:r.parse(eE(e),n.parseOptions)}return eA("",t,n)}let eD=e=>!("type"in e);function eI(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function eR(e,t,n={}){let{from:r,to:i,empty:o}=e.selection,s=t?q(t,e.schema):null,l=[];e.doc.nodesBetween(r,i,(e,t)=>{if(e.isText)return;let n=Math.max(r,t),o=Math.min(i,t+e.nodeSize);l.push({node:e,from:n,to:o})});let a=i-r,h=l.filter(e=>!s||s.name===e.node.type.name).filter(e=>eb(e.node.attrs,n,{strict:!1}));return o?!!h.length:h.reduce((e,t)=>e+t.to-t.from,0)>=a}function eP(e,t){return t.nodes[e]?"node":t.marks[e]?"mark":null}function ez(e,t){let n="string"==typeof t?[t]:t;return Object.keys(e).reduce((t,r)=>(n.includes(r)||(t[r]=e[r]),t),{})}function eB(e,t,n={},r={}){return eA(e,t,{slice:!1,parseOptions:n,errorOnInvalidContent:r.errorOnInvalidContent})}function eF(e,t){let n=eS(t,e.schema),{from:r,to:i,empty:o}=e.selection,s=[];o?(e.storedMarks&&s.push(...e.storedMarks),s.push(...e.selection.$head.marks())):e.doc.nodesBetween(r,i,e=>{s.push(...e.marks)});let l=s.find(e=>e.type.name===n.name);return l?{...l.attrs}:{}}function eL(e){return t=>(function(e,t){for(let n=e.depth;n>0;n-=1){let r=e.node(n);if(t(r))return{pos:n>0?e.before(n):0,start:e.start(n),depth:n,node:r}}})(t.$from,e)}function e$(e,t,n){let r=[];return e===t?n.resolve(e).marks().forEach(t=>{let i=ex(n.resolve(e),t.type);i&&r.push({mark:t,...i})}):n.nodesBetween(e,t,(e,t)=>{e&&(null==e?void 0:e.nodeSize)!==void 0&&r.push(...e.marks.map(n=>({from:t,to:t+e.nodeSize,mark:n})))}),r}function eV(e,t,n){return Object.fromEntries(Object.entries(n).filter(([n])=>{let r=e.find(e=>e.type===t&&e.name===n);return!!r&&r.attribute.keepOnSplit}))}function eJ(e,t,n={}){let{empty:r,ranges:i}=e.selection,o=t?eS(t,e.schema):null;if(r)return!!(e.storedMarks||e.selection.$from.marks()).filter(e=>!o||o.name===e.type.name).find(e=>eb(e.attrs,n,{strict:!1}));let s=0,l=[];if(i.forEach(({$from:t,$to:n})=>{let r=t.pos,i=n.pos;e.doc.nodesBetween(r,i,(e,t)=>{if(!e.isText&&!e.marks.length)return;let n=Math.max(r,t),o=Math.min(i,t+e.nodeSize);s+=o-n,l.push(...e.marks.map(e=>({mark:e,from:n,to:o})))})}),0===s)return!1;let a=l.filter(e=>!o||o.name===e.mark.type.name).filter(e=>eb(e.mark.attrs,n,{strict:!1})).reduce((e,t)=>e+t.to-t.from,0),h=l.filter(e=>!o||e.mark.type!==o&&e.mark.type.excludes(o)).reduce((e,t)=>e+t.to-t.from,0);return(a>0?a+h:a)>=s}function eK(e,t){let{nodeExtensions:n}=H(t),r=n.find(t=>t.name===e);if(!r)return!1;let i={name:r.name,options:r.options,storage:r.storage},o=X(j(r,"group",i));return"string"==typeof o&&o.split(" ").includes("list")}function ej(e,{checkChildren:t=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if("hardBreak"===e.type.name)return!0;if(e.isText)return/^\s*$/m.test(null!==(r=e.text)&&void 0!==r?r:"")}if(e.isText)return!e.text;if(e.isAtom||e.isLeaf)return!1;if(0===e.content.childCount)return!0;if(t){let r=!0;return e.content.forEach(e=>{!1!==r&&(ej(e,{ignoreWhitespace:n,checkChildren:t})||(r=!1))}),r}return!1}function eH(e){return e instanceof r.nh}function eW(e,t){let n=e.storedMarks||e.selection.$to.parentOffset&&e.selection.$from.marks();if(n){let r=n.filter(e=>null==t?void 0:t.includes(e.type.name));e.tr.ensureMarks(r)}}let eq=(e,t)=>{let n=eL(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;let i=e.doc.nodeAt(r);return!(n.node.type===(null==i?void 0:i.type)&&(0,l.n9)(e.doc,n.pos))||(e.join(n.pos),!0)},e_=(e,t)=>{let n=eL(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;let i=e.doc.nodeAt(r);return!(n.node.type===(null==i?void 0:i.type)&&(0,l.n9)(e.doc,r))||(e.join(r),!0)};var eU=Object.freeze({__proto__:null,blur:()=>({editor:e,view:t})=>(requestAnimationFrame(()=>{var n;e.isDestroyed||(t.dom.blur(),null===(n=null==window?void 0:window.getSelection())||void 0===n||n.removeAllRanges())}),!0),clearContent:(e=!1)=>({commands:t})=>t.setContent("",e),clearNodes:()=>({state:e,tr:t,dispatch:n})=>{let{selection:r}=t,{ranges:i}=r;return!n||(i.forEach(({$from:n,$to:r})=>{e.doc.nodesBetween(n.pos,r.pos,(e,n)=>{if(e.type.isText)return;let{doc:r,mapping:i}=t,o=r.resolve(i.map(n)),s=r.resolve(i.map(n+e.nodeSize)),a=o.blockRange(s);if(!a)return;let h=(0,l.jP)(a);if(e.type.isTextblock){let{defaultType:e}=o.parent.contentMatchAt(o.index());t.setNodeMarkup(a.start,e)}(h||0===h)&&t.lift(a,h)})}),!0)},command:e=>t=>e(t),createParagraphNear:()=>({state:e,dispatch:t})=>T(e,t),cut:(e,t)=>({editor:n,tr:i})=>{let{state:o}=n,s=o.doc.slice(e.from,e.to);i.deleteRange(e.from,e.to);let l=i.mapping.map(t);return i.insert(l,s.content),i.setSelection(new r.U3(i.doc.resolve(l-1))),!0},deleteCurrentNode:()=>({tr:e,dispatch:t})=>{let{selection:n}=e,r=n.$anchor.node();if(r.content.size>0)return!1;let i=e.selection.$anchor;for(let n=i.depth;n>0;n-=1)if(i.node(n).type===r.type){if(t){let t=i.before(n),r=i.after(n);e.delete(t,r).scrollIntoView()}return!0}return!1},deleteNode:e=>({tr:t,state:n,dispatch:r})=>{let i=q(e,n.schema),o=t.selection.$anchor;for(let e=o.depth;e>0;e-=1)if(o.node(e).type===i){if(r){let n=o.before(e),r=o.after(e);t.delete(n,r).scrollIntoView()}return!0}return!1},deleteRange:e=>({tr:t,dispatch:n})=>{let{from:r,to:i}=e;return n&&t.delete(r,i),!0},deleteSelection:()=>({state:e,dispatch:t})=>a(e,t),enter:()=>({commands:e})=>e.keyboardShortcut("Enter"),exitCode:()=>({state:e,dispatch:t})=>O(e,t),extendMarkRange:(e,t={})=>({tr:n,state:i,dispatch:o})=>{let s=eS(e,i.schema),{doc:l,selection:a}=n,{$from:h,from:d,to:c}=a;if(o){let e=ex(h,s,t);if(e&&e.from<=d&&e.to>=c){let t=r.U3.create(l,e.from,e.to);n.setSelection(t)}}return!0},first:e=>t=>{let n="function"==typeof e?e(t):e;for(let e=0;e<n.length;e+=1)if(n[e](t))return!0;return!1},focus:(e=null,t={})=>({editor:n,view:r,tr:i,dispatch:o})=>{t={scrollIntoView:!0,...t};let s=()=>{(eT()||"Android"===navigator.platform||/android/i.test(navigator.userAgent))&&r.dom.focus(),requestAnimationFrame(()=>{!n.isDestroyed&&(r.focus(),(null==t?void 0:t.scrollIntoView)&&n.commands.scrollIntoView())})};if(r.hasFocus()&&null===e||!1===e)return!0;if(o&&null===e&&!eM(n.state.selection))return s(),!0;let l=eO(i.doc,e)||n.state.selection,a=n.state.selection.eq(l);return o&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),s()),!0},forEach:(e,t)=>n=>e.every((e,r)=>t(e,{...n,index:r})),insertContent:(e,t)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},e,t),insertContentAt:(e,t,n)=>({tr:i,dispatch:o,editor:a})=>{var h;if(o){let o,d;n={parseOptions:a.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};try{o=eA(t,a.schema,{parseOptions:{preserveWhitespace:"full",...n.parseOptions},errorOnInvalidContent:null!==(h=n.errorOnInvalidContent)&&void 0!==h?h:a.options.enableContentCheck})}catch(e){return a.emit("contentError",{editor:a,error:e,disableCollaboration:()=>{a.storage.collaboration&&(a.storage.collaboration.isDisabled=!0)}}),!1}let{from:c,to:p}="number"==typeof e?{from:e,to:e}:{from:e.from,to:e.to},u=!0,f=!0;if((eD(o)?o:[o]).forEach(e=>{e.check(),u=!!u&&e.isText&&0===e.marks.length,f=!!f&&e.isBlock}),c===p&&f){let{parent:e}=i.doc.resolve(c);!e.isTextblock||e.type.spec.code||e.childCount||(c-=1,p+=1)}if(u){if(Array.isArray(t))d=t.map(e=>e.text||"").join("");else if(t instanceof s.FK){let e="";t.forEach(t=>{t.text&&(e+=t.text)}),d=e}else d="object"==typeof t&&t&&t.text?t.text:t;i.insertText(d,c,p)}else d=o,i.replaceWith(c,p,d);n.updateSelection&&function(e,t,n){let i=e.steps.length-1;if(i<t)return;let o=e.steps[i];if(!(o instanceof l.Ln||o instanceof l.Wg))return;let s=e.mapping.maps[i],a=0;s.forEach((e,t,n,r)=>{0===a&&(a=r)}),e.setSelection(r.LN.near(e.doc.resolve(a),-1))}(i,i.steps.length-1,0),n.applyInputRules&&i.setMeta("applyInputRules",{from:c,text:d}),n.applyPasteRules&&i.setMeta("applyPasteRules",{from:c,text:d})}return!0},joinBackward:()=>({state:e,dispatch:t})=>d(e,t),joinDown:()=>({state:e,dispatch:t})=>x(e,t),joinForward:()=>({state:e,dispatch:t})=>v(e,t),joinItemBackward:()=>({state:e,dispatch:t,tr:n})=>{try{let r=(0,l.N0)(e.doc,e.selection.$from.pos,-1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},joinItemForward:()=>({state:e,dispatch:t,tr:n})=>{try{let r=(0,l.N0)(e.doc,e.selection.$from.pos,1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},joinTextblockBackward:()=>({state:e,dispatch:t})=>c(e,t),joinTextblockForward:()=>({state:e,dispatch:t})=>p(e,t),joinUp:()=>({state:e,dispatch:t})=>k(e,t),keyboardShortcut:e=>({editor:t,view:n,tr:r,dispatch:i})=>{let o=(function(e){let t,n,r,i;let o=e.split(/-(?!$)/),s=o[o.length-1];"Space"===s&&(s=" ");for(let e=0;e<o.length-1;e+=1){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))eT()||eI()?i=!0:n=!0;else throw Error(`Unrecognized modifier name: ${s}`)}return t&&(s=`Alt-${s}`),n&&(s=`Ctrl-${s}`),i&&(s=`Meta-${s}`),r&&(s=`Shift-${s}`),s})(e).split(/-(?!$)/),s=o.find(e=>!["Alt","Ctrl","Meta","Shift"].includes(e)),l=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:o.includes("Alt"),ctrlKey:o.includes("Ctrl"),metaKey:o.includes("Meta"),shiftKey:o.includes("Shift"),bubbles:!0,cancelable:!0}),a=t.captureTransaction(()=>{n.someProp("handleKeyDown",e=>e(n,l))});return null==a||a.steps.forEach(e=>{let t=e.map(r.mapping);t&&i&&r.maybeStep(t)}),!0},lift:(e,t={})=>({state:n,dispatch:r})=>{let i=q(e,n.schema);return!!eR(n,i,t)&&S(n,r)},liftEmptyBlock:()=>({state:e,dispatch:t})=>N(e,t),liftListItem:e=>({state:t,dispatch:n})=>(function(e){return function(t,n){let{$from:r,$to:i}=t.selection,o=r.blockRange(i,t=>t.childCount>0&&t.firstChild.type==e);return!!o&&(!n||(r.node(o.depth-1).type==e?function(e,t,n,r){let i=e.tr,o=r.end,a=r.$to.end(r.depth);o<a&&(i.step(new l.Wg(o-1,a,o,a,new s.Ji(s.FK.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new s.u$(i.doc.resolve(r.$from.pos),i.doc.resolve(a),r.depth));let h=(0,l.jP)(r);if(null==h)return!1;i.lift(r,h);let d=i.mapping.map(o,-1)-1;return(0,l.n9)(i.doc,d)&&i.join(d),t(i.scrollIntoView()),!0}(t,n,e,o):function(e,t,n){let r=e.tr,i=n.parent;for(let e=n.end,t=n.endIndex-1,o=n.startIndex;t>o;t--)e-=i.child(t).nodeSize,r.delete(e-1,e+1);let o=r.doc.resolve(n.start),a=o.nodeAfter;if(r.mapping.map(n.end)!=n.start+o.nodeAfter.nodeSize)return!1;let h=0==n.startIndex,d=n.endIndex==i.childCount,c=o.node(-1),p=o.index(-1);if(!c.canReplace(p+(h?0:1),p+1,a.content.append(d?s.FK.empty:s.FK.from(i))))return!1;let u=o.pos,f=u+a.nodeSize;return r.step(new l.Wg(u-(h?1:0),f+(d?1:0),u+1,f-1,new s.Ji((h?s.FK.empty:s.FK.from(i.copy(s.FK.empty))).append(d?s.FK.empty:s.FK.from(i.copy(s.FK.empty))),h?0:1,d?0:1),h?0:1)),t(r.scrollIntoView()),!0}(t,n,o)))}})(q(e,t.schema))(t,n),newlineInCode:()=>({state:e,dispatch:t})=>M(e,t),resetAttributes:(e,t)=>({tr:n,state:r,dispatch:i})=>{let o=null,s=null,l=eP("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(o=q(e,r.schema)),"mark"===l&&(s=eS(e,r.schema)),i&&n.selection.ranges.forEach(e=>{r.doc.nodesBetween(e.$from.pos,e.$to.pos,(e,r)=>{o&&o===e.type&&n.setNodeMarkup(r,void 0,ez(e.attrs,t)),s&&e.marks.length&&e.marks.forEach(i=>{s===i.type&&n.addMark(r,r+e.nodeSize,s.create(ez(i.attrs,t)))})})}),!0)},scrollIntoView:()=>({tr:e,dispatch:t})=>(t&&e.scrollIntoView(),!0),selectAll:()=>({tr:e,dispatch:t})=>{if(t){let t=new r.i5(e.doc);e.setSelection(t)}return!0},selectNodeBackward:()=>({state:e,dispatch:t})=>m(e,t),selectNodeForward:()=>({state:e,dispatch:t})=>b(e,t),selectParentNode:()=>({state:e,dispatch:t})=>E(e,t),selectTextblockEnd:()=>({state:e,dispatch:t})=>R(e,t),selectTextblockStart:()=>({state:e,dispatch:t})=>I(e,t),setContent:(e,t=!1,n={},r={})=>({editor:i,tr:o,dispatch:s,commands:l})=>{var a,h;let{doc:d}=o;if("full"!==n.preserveWhitespace){let l=eB(e,i.schema,n,{errorOnInvalidContent:null!==(a=r.errorOnInvalidContent)&&void 0!==a?a:i.options.enableContentCheck});return s&&o.replaceWith(0,d.content.size,l).setMeta("preventUpdate",!t),!0}return s&&o.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:d.content.size},e,{parseOptions:n,errorOnInvalidContent:null!==(h=r.errorOnInvalidContent)&&void 0!==h?h:i.options.enableContentCheck})},setMark:(e,t={})=>({tr:n,state:r,dispatch:i})=>{let{selection:o}=n,{empty:s,ranges:l}=o,a=eS(e,r.schema);if(i){if(s){let e=eF(r,a);n.addStoredMark(a.create({...e,...t}))}else l.forEach(e=>{let i=e.$from.pos,o=e.$to.pos;r.doc.nodesBetween(i,o,(e,r)=>{let s=Math.max(r,i),l=Math.min(r+e.nodeSize,o);e.marks.find(e=>e.type===a)?e.marks.forEach(e=>{a===e.type&&n.addMark(s,l,a.create({...e.attrs,...t}))}):n.addMark(s,l,a.create(t))})})}return function(e,t,n){var r;let{selection:i}=t,o=null;if(eM(i)&&(o=i.$cursor),o){let t=null!==(r=e.storedMarks)&&void 0!==r?r:o.marks();return!!n.isInSet(t)||!t.some(e=>e.type.excludes(n))}let{ranges:s}=i;return s.some(({$from:t,$to:r})=>{let i=0===t.depth&&e.doc.inlineContent&&e.doc.type.allowsMarkType(n);return e.doc.nodesBetween(t.pos,r.pos,(e,t,r)=>{if(i)return!1;if(e.isInline){let t=!r||r.type.allowsMarkType(n),o=!!n.isInSet(e.marks)||!e.marks.some(e=>e.type.excludes(n));i=t&&o}return!i}),i})}(r,n,a)},setMeta:(e,t)=>({tr:n})=>(n.setMeta(e,t),!0),setNode:(e,t={})=>({state:n,dispatch:r,chain:i})=>{let o;let s=q(e,n.schema);return(n.selection.$anchor.sameParent(n.selection.$head)&&(o=n.selection.$anchor.parent.attrs),s.isTextblock)?i().command(({commands:e})=>!!P(s,{...o,...t})(n)||e.clearNodes()).command(({state:e})=>P(s,{...o,...t})(e,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,i=eC(e,0,n.content.size),o=r.nh.create(n,i);t.setSelection(o)}return!0},setTextSelection:e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,{from:i,to:o}="number"==typeof e?{from:e,to:e}:e,s=r.U3.atStart(n).from,l=r.U3.atEnd(n).to,a=eC(i,s,l),h=eC(o,s,l),d=r.U3.create(n,a,h);t.setSelection(d)}return!0},sinkListItem:e=>({state:t,dispatch:n})=>(function(e){return function(t,n){let{$from:r,$to:i}=t.selection,o=r.blockRange(i,t=>t.childCount>0&&t.firstChild.type==e);if(!o)return!1;let a=o.startIndex;if(0==a)return!1;let h=o.parent,d=h.child(a-1);if(d.type!=e)return!1;if(n){let r=d.lastChild&&d.lastChild.type==h.type,i=s.FK.from(r?e.create():null),a=new s.Ji(s.FK.from(e.create(null,s.FK.from(h.type.create(null,i)))),r?3:1,0),c=o.start,p=o.end;n(t.tr.step(new l.Wg(c-(r?3:1),p,c,p,a,1,!0)).scrollIntoView())}return!0}})(q(e,t.schema))(t,n),splitBlock:({keepMarks:e=!0}={})=>({tr:t,state:n,dispatch:i,editor:o})=>{let{selection:s,doc:a}=t,{$from:h,$to:d}=s,c=eV(o.extensionManager.attributes,h.node().type.name,h.node().attrs);if(s instanceof r.nh&&s.node.isBlock)return!!(h.parentOffset&&(0,l.zy)(a,h.pos))&&(i&&(e&&eW(n,o.extensionManager.splittableMarks),t.split(h.pos).scrollIntoView()),!0);if(!h.parent.isBlock)return!1;let p=d.parentOffset===d.parent.content.size,u=0===h.depth?void 0:function(e){for(let t=0;t<e.edgeCount;t+=1){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(h.node(-1).contentMatchAt(h.indexAfter(-1))),f=p&&u?[{type:u,attrs:c}]:void 0,m=(0,l.zy)(t.doc,t.mapping.map(h.pos),1,f);if(!f&&!m&&(0,l.zy)(t.doc,t.mapping.map(h.pos),1,u?[{type:u}]:void 0)&&(m=!0,f=u?[{type:u,attrs:c}]:void 0),i){if(m&&(s instanceof r.U3&&t.deleteSelection(),t.split(t.mapping.map(h.pos),1,f),u&&!p&&!h.parentOffset&&h.parent.type!==u)){let e=t.mapping.map(h.before()),n=t.doc.resolve(e);h.node(-1).canReplaceWith(n.index(),n.index()+1,u)&&t.setNodeMarkup(t.mapping.map(h.before()),u)}e&&eW(n,o.extensionManager.splittableMarks),t.scrollIntoView()}return m},splitListItem:(e,t={})=>({tr:n,state:i,dispatch:o,editor:a})=>{var h;let d=q(e,i.schema),{$from:c,$to:p}=i.selection,u=i.selection.node;if(u&&u.isBlock||c.depth<2||!c.sameParent(p))return!1;let f=c.node(-1);if(f.type!==d)return!1;let m=a.extensionManager.attributes;if(0===c.parent.content.size&&c.node(-1).childCount===c.indexAfter(-1)){if(2===c.depth||c.node(-3).type!==d||c.index(-2)!==c.node(-2).childCount-1)return!1;if(o){let e=s.FK.empty,i=c.index(-1)?1:c.index(-2)?2:3;for(let t=c.depth-i;t>=c.depth-3;t-=1)e=s.FK.from(c.node(t).copy(e));let o=c.indexAfter(-1)<c.node(-2).childCount?1:c.indexAfter(-2)<c.node(-3).childCount?2:3,l={...eV(m,c.node().type.name,c.node().attrs),...t},a=(null===(h=d.contentMatch.defaultType)||void 0===h?void 0:h.createAndFill(l))||void 0;e=e.append(s.FK.from(d.createAndFill(null,a)||void 0));let p=c.before(c.depth-(i-1));n.replace(p,c.after(-o),new s.Ji(e,4-i,0));let u=-1;n.doc.nodesBetween(p,n.doc.content.size,(e,t)=>{if(u>-1)return!1;e.isTextblock&&0===e.content.size&&(u=t+1)}),u>-1&&n.setSelection(r.U3.near(n.doc.resolve(u))),n.scrollIntoView()}return!0}let g=p.pos===c.end()?f.contentMatchAt(0).defaultType:null,y={...eV(m,f.type.name,f.attrs),...t},v={...eV(m,c.node().type.name,c.node().attrs),...t};n.delete(c.pos,p.pos);let b=g?[{type:d,attrs:y},{type:g,attrs:v}]:[{type:d,attrs:y}];if(!(0,l.zy)(n.doc,c.pos,2))return!1;if(o){let{selection:e,storedMarks:t}=i,{splittableMarks:r}=a.extensionManager,s=t||e.$to.parentOffset&&e.$from.marks();if(n.split(c.pos,2,b).scrollIntoView(),!s||!o)return!0;let l=s.filter(e=>r.includes(e.type.name));n.ensureMarks(l)}return!0},toggleList:(e,t,n,r={})=>({editor:i,tr:o,state:s,dispatch:l,chain:a,commands:h,can:d})=>{let{extensions:c,splittableMarks:p}=i.extensionManager,u=q(e,s.schema),f=q(t,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:v}=m,b=y.blockRange(v),w=g||m.$to.parentOffset&&m.$from.marks();if(!b)return!1;let k=eL(e=>eK(e.type.name,c))(m);if(b.depth>=1&&k&&b.depth-k.depth<=1){if(k.node.type===u)return h.liftListItem(f);if(eK(k.node.type.name,c)&&u.validContent(k.node.content)&&l)return a().command(()=>(o.setNodeMarkup(k.pos,u),!0)).command(()=>eq(o,u)).command(()=>e_(o,u)).run()}return n&&w&&l?a().command(()=>{let e=d().wrapInList(u,r),t=w.filter(e=>p.includes(e.type.name));return o.ensureMarks(t),!!e||h.clearNodes()}).wrapInList(u,r).command(()=>eq(o,u)).command(()=>e_(o,u)).run():a().command(()=>!!d().wrapInList(u,r)||h.clearNodes()).wrapInList(u,r).command(()=>eq(o,u)).command(()=>e_(o,u)).run()},toggleMark:(e,t={},n={})=>({state:r,commands:i})=>{let{extendEmptyMarkRange:o=!1}=n,s=eS(e,r.schema);return eJ(r,s,t)?i.unsetMark(s,{extendEmptyMarkRange:o}):i.setMark(s,t)},toggleNode:(e,t,n={})=>({state:r,commands:i})=>{let o;let s=q(e,r.schema),l=q(t,r.schema),a=eR(r,s,n);return(r.selection.$anchor.sameParent(r.selection.$head)&&(o=r.selection.$anchor.parent.attrs),a)?i.setNode(l,o):i.setNode(s,{...o,...n})},toggleWrap:(e,t={})=>({state:n,commands:r})=>{let i=q(e,n.schema);return eR(n,i,t)?r.lift(i):r.wrapIn(i,t)},undoInputRule:()=>({state:e,dispatch:t})=>{let n=e.plugins;for(let r=0;r<n.length;r+=1){let i;let o=n[r];if(o.spec.isInputRules&&(i=o.getState(e))){if(t){let t=e.tr,n=i.transform;for(let e=n.steps.length-1;e>=0;e-=1)t.step(n.steps[e].invert(n.docs[e]));if(i.text){let n=t.doc.resolve(i.from).marks();t.replaceWith(i.from,i.to,e.schema.text(i.text,n))}else t.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:e,dispatch:t})=>{let{selection:n}=e,{empty:r,ranges:i}=n;return!!r||(t&&i.forEach(t=>{e.removeMark(t.$from.pos,t.$to.pos)}),!0)},unsetMark:(e,t={})=>({tr:n,state:r,dispatch:i})=>{var o;let{extendEmptyMarkRange:s=!1}=t,{selection:l}=n,a=eS(e,r.schema),{$from:h,empty:d,ranges:c}=l;if(!i)return!0;if(d&&s){let{from:e,to:t}=l,r=null===(o=h.marks().find(e=>e.type===a))||void 0===o?void 0:o.attrs,i=ex(h,a,r);i&&(e=i.from,t=i.to),n.removeMark(e,t,a)}else c.forEach(e=>{n.removeMark(e.$from.pos,e.$to.pos,a)});return n.removeStoredMark(a),!0},updateAttributes:(e,t={})=>({tr:n,state:r,dispatch:i})=>{let o=null,s=null,l=eP("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(o=q(e,r.schema)),"mark"===l&&(s=eS(e,r.schema)),i&&n.selection.ranges.forEach(e=>{let i,l,a,h;let d=e.$from.pos,c=e.$to.pos;n.selection.empty?r.doc.nodesBetween(d,c,(e,t)=>{o&&o===e.type&&(a=Math.max(t,d),h=Math.min(t+e.nodeSize,c),i=t,l=e)}):r.doc.nodesBetween(d,c,(e,r)=>{r<d&&o&&o===e.type&&(a=Math.max(r,d),h=Math.min(r+e.nodeSize,c),i=r,l=e),r>=d&&r<=c&&(o&&o===e.type&&n.setNodeMarkup(r,void 0,{...e.attrs,...t}),s&&e.marks.length&&e.marks.forEach(i=>{if(s===i.type){let o=Math.max(r,d),l=Math.min(r+e.nodeSize,c);n.addMark(o,l,s.create({...i.attrs,...t}))}}))}),l&&(void 0!==i&&n.setNodeMarkup(i,void 0,{...l.attrs,...t}),s&&l.marks.length&&l.marks.forEach(e=>{s===e.type&&n.addMark(a,h,s.create({...e.attrs,...t}))}))}),!0)},wrapIn:(e,t={})=>({state:n,dispatch:r})=>(function(e,t=null){return function(n,r){let{$from:i,$to:o}=n.selection,s=i.blockRange(o),a=s&&(0,l.oM)(s,e,t);return!!a&&(r&&r(n.tr.wrap(s,a).scrollIntoView()),!0)}})(q(e,n.schema),t)(n,r),wrapInList:(e,t={})=>({state:n,dispatch:r})=>(function(e,t=null){return function(n,r){let{$from:i,$to:o}=n.selection,a=i.blockRange(o);if(!a)return!1;let h=r?n.tr:null;return!!function(e,t,n,r=null){let i=!1,o=t,a=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=a.resolve(t.start-2);o=new s.u$(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new s.u$(t.$from,a.resolve(t.$to.end(t.depth)),t.depth)),i=!0}let h=(0,l.oM)(o,n,r,t);return!!h&&(e&&function(e,t,n,r,i){let o=s.FK.empty;for(let e=n.length-1;e>=0;e--)o=s.FK.from(n[e].type.create(n[e].attrs,o));e.step(new l.Wg(t.start-(r?2:0),t.end,t.start,t.end,new s.Ji(o,0,0),n.length,!0));let a=0;for(let e=0;e<n.length;e++)n[e].type==i&&(a=e+1);let h=n.length-a,d=t.start+n.length-(r?2:0),c=t.parent;for(let n=t.startIndex,r=t.endIndex,i=!0;n<r;n++,i=!1)!i&&(0,l.zy)(e.doc,d,h)&&(e.split(d,h),d+=2*h),d+=c.child(n).nodeSize}(e,t,h,i,n),!0)}(h,a,e,t)&&(r&&r(h.scrollIntoView()),!0)}})(q(e,n.schema),t)(n,r)});let eG=em.create({name:"commands",addCommands:()=>({...eU})}),eX=em.create({name:"drop",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("tiptapDrop"),props:{handleDrop:(e,t,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:t,slice:n,moved:r})}}})]}}),eY=em.create({name:"editable",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("editable"),props:{editable:()=>this.editor.options.editable}})]}}),eZ=em.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:e}=this;return[new r.k_({key:new r.hs("focusEvents"),props:{handleDOMEvents:{focus:(t,n)=>{e.isFocused=!0;let r=e.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1},blur:(t,n)=>{e.isFocused=!1;let r=e.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1}}}})]}}),eQ=em.create({name:"keymap",addKeyboardShortcuts(){let e=()=>this.editor.commands.first(({commands:e})=>[()=>e.undoInputRule(),()=>e.command(({tr:t})=>{let{selection:n,doc:i}=t,{empty:o,$anchor:s}=n,{pos:l,parent:a}=s,h=s.parent.isTextblock&&l>0?t.doc.resolve(l-1):s,d=h.parent.type.spec.isolating,c=s.pos-s.parentOffset,p=d&&1===h.parent.childCount?c===s.pos:r.LN.atStart(i).from===l;return!!o&&!!a.type.isTextblock&&!a.textContent.length&&!!p&&(!p||"paragraph"!==s.parent.type.name)&&e.clearNodes()}),()=>e.deleteSelection(),()=>e.joinBackward(),()=>e.selectNodeBackward()]),t=()=>this.editor.commands.first(({commands:e})=>[()=>e.deleteSelection(),()=>e.deleteCurrentNode(),()=>e.joinForward(),()=>e.selectNodeForward()]),n={Enter:()=>this.editor.commands.first(({commands:e})=>[()=>e.newlineInCode(),()=>e.createParagraphNear(),()=>e.liftEmptyBlock(),()=>e.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:e,"Mod-Backspace":e,"Shift-Backspace":e,Delete:t,"Mod-Delete":t,"Mod-a":()=>this.editor.commands.selectAll()},i={...n},o={...n,"Ctrl-h":e,"Alt-Backspace":e,"Ctrl-d":t,"Ctrl-Alt-Backspace":t,"Alt-Delete":t,"Alt-d":t,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return eT()||eI()?o:i},addProseMirrorPlugins(){return[new r.k_({key:new r.hs("clearDocument"),appendTransaction:(e,t,n)=>{if(e.some(e=>e.getMeta("composition")))return;let i=e.some(e=>e.docChanged)&&!t.doc.eq(n.doc),o=e.some(e=>e.getMeta("preventClearDocument"));if(!i||o)return;let{empty:s,from:l,to:a}=t.selection,h=r.LN.atStart(t.doc).from,d=r.LN.atEnd(t.doc).to;if(s||!(l===h&&a===d)||!ej(n.doc))return;let c=n.tr,p=V({state:n,transaction:c}),{commands:u}=new J({editor:this.editor,state:p});if(u.clearNodes(),c.steps.length)return c}})]}}),e0=em.create({name:"paste",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("tiptapPaste"),props:{handlePaste:(e,t,n)=>{this.editor.emit("paste",{editor:this.editor,event:t,slice:n})}}})]}}),e1=em.create({name:"tabindex",addProseMirrorPlugins(){return[new r.k_({key:new r.hs("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class e2{get name(){return this.node.type.name}constructor(e,t,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=r}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return null!==(e=this.actualDepth)&&void 0!==e?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(0===this.content.size){console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(0===this.depth)return null;let e=this.resolvedPos.start(this.resolvedPos.depth-1);return new e2(this.resolvedPos.doc.resolve(e),this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new e2(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new e2(e,this.editor)}get children(){let e=[];return this.node.content.forEach((t,n)=>{let r=t.isBlock&&!t.isTextblock,i=t.isAtom&&!t.isText,o=this.pos+n+(i?0:1),s=this.resolvedPos.doc.resolve(o);if(!r&&s.depth<=this.depth)return;let l=new e2(s,this.editor,r,r?t:null);r&&(l.actualDepth=this.depth+1),e.push(new e2(s,this.editor,r,r?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){let e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===e){if(Object.keys(t).length>0){let e=r.node.attrs,n=Object.keys(t);for(let r=0;r<n.length;r+=1){let i=n[r];if(e[i]!==t[i])break}}else n=r}r=r.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;let i=Object.keys(t);return this.children.forEach(o=>{(!n||!(r.length>0))&&(o.node.type.name===e&&i.every(e=>t[e]===o.node.attrs[e])&&r.push(o),n&&r.length>0||(r=r.concat(o.querySelectorAll(e,t,n))))}),r}setAttribute(e){let{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}let e3=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;class e5 extends K{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:e})=>{throw e},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:e,slice:t,moved:n})=>this.options.onDrop(e,t,n)),this.on("paste",({event:e,slice:t})=>this.options.onPaste(e,t)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t,n){let r=document.querySelector("style[data-tiptap-style]");if(null!==r)return r;let i=document.createElement("style");return t&&i.setAttribute("nonce",t),i.setAttribute("data-tiptap-style",""),i.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(i),i}(e3,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){let n=G(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(e){if(this.isDestroyed)return;let t=this.state.plugins,n=t;if([].concat(e).forEach(e=>{let r="string"==typeof e?`${e}$`:e.key;n=t.filter(e=>!e.key.startsWith(r))}),t.length===n.length)return;let r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var e,t;let n=[...this.options.enableCoreExtensions?[eY,ev.configure({blockSeparator:null===(t=null===(e=this.options.coreExtensionOptions)||void 0===e?void 0:e.clipboardTextSerializer)||void 0===t?void 0:t.blockSeparator}),eG,eZ,eQ,e1,eX,e0].filter(e=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[e.name]):[],...this.options.extensions].filter(e=>["extension","node","mark"].includes(null==e?void 0:e.type));this.extensionManager=new ef(n,this)}createCommandManager(){this.commandManager=new J({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=eB(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(e){if(!(e instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(e.message))throw e;this.emit("contentError",{editor:this,error:e,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(e=>"collaboration"!==e.name),this.createExtensionManager()}}),t=eB(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}let n=eO(t,this.options.autofocus);this.view=new i.Lz(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null===(e=this.options.editorProps)||void 0===e?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:r.$t.create({doc:t,selection:n||void 0})});let o=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(o),this.createNodeViews(),this.prependClass(),this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;let t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(e=>{var t;return null===(t=this.capturedTransaction)||void 0===t?void 0:t.step(e)});return}let t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});let r=e.getMeta("focus"),i=e.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:e}),i&&this.emit("blur",{editor:this,event:i.event,transaction:e}),!e.docChanged||e.getMeta("preventUpdate")||this.emit("update",{editor:this,transaction:e})}getAttributes(e){return function(e,t){let n=eP("string"==typeof t?t:t.name,e.schema);return"node"===n?function(e,t){let n=q(t,e.schema),{from:r,to:i}=e.selection,o=[];e.doc.nodesBetween(r,i,e=>{o.push(e)});let s=o.reverse().find(e=>e.type.name===n.name);return s?{...s.attrs}:{}}(e,t):"mark"===n?eF(e,t):{}}(this.state,e)}isActive(e,t){let n="string"==typeof e?e:null,r="string"==typeof e?t:e;return function(e,t,n={}){if(!t)return eR(e,null,n)||eJ(e,null,n);let r=eP(t,e.schema);return"node"===r?eR(e,t,n):"mark"===r&&eJ(e,t,n)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return et(this.state.doc.content,this.schema)}getText(e){let{blockSeparator:t="\n\n",textSerializers:n={}}=e||{};return function(e,t){let n={from:0,to:e.content.size};return eg(e,n,t)}(this.state.doc,{blockSeparator:t,textSerializers:{...ey(this.schema),...n}})}get isEmpty(){return ej(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){let e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(null===(e=this.view)||void 0===e?void 0:e.docView)}$node(e,t){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelectorAll(e,t))||null}$pos(e){return new e2(this.state.doc.resolve(e),this)}get $doc(){return this.$pos(0)}}function e8(e){return new ei({find:e.find,handler:({state:t,range:n,match:r})=>{let i=X(e.getAttributes,void 0,r);if(!1===i||null===i)return null;let{tr:o}=t,s=r[r.length-1],l=r[0];if(s){let r=l.search(/\S/),a=n.from+l.indexOf(s),h=a+s.length;if(e$(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>a).length)return null;h<n.to&&o.delete(h,n.to),a>n.from&&o.delete(n.from+r,a);let d=n.from+r+s.length;o.addMark(n.from+r,d,e.type.create(i||{})),o.removeStoredMark(e.type)}}})}function e4(e){return new ei({find:e.find,handler:({state:t,range:n,match:r})=>{let i=X(e.getAttributes,void 0,r)||{},{tr:o}=t,s=n.from,l=n.to,a=e.type.create(i);if(r[1]){let e=s+r[0].lastIndexOf(r[1]);e>l?e=l:l=e+r[1].length;let t=r[0][r[0].length-1];o.insertText(t,s+r[0].length-1),o.replaceWith(e,l,a)}else if(r[0]){let t=e.type.isInline?s:s-1;o.insert(t,e.type.create(i)).delete(o.mapping.map(s),o.mapping.map(l))}o.scrollIntoView()}})}function e6(e){return new ei({find:e.find,handler:({state:t,range:n,match:r})=>{let i=t.doc.resolve(n.from),o=X(e.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),e.type))return null;t.tr.delete(n.from,n.to).setBlockType(n.from,n.from,e.type,o)}})}function e9(e){return new ei({find:e.find,handler:({state:t,range:n,match:r,chain:i})=>{let o=X(e.getAttributes,void 0,r)||{},s=t.tr.delete(n.from,n.to),a=s.doc.resolve(n.from).blockRange(),h=a&&(0,l.oM)(a,e.type,o);if(!h)return null;if(s.wrap(a,h),e.keepMarks&&e.editor){let{selection:n,storedMarks:r}=t,{splittableMarks:i}=e.editor.extensionManager,o=r||n.$to.parentOffset&&n.$from.marks();if(o){let e=o.filter(e=>i.includes(e.type.name));s.ensureMarks(e)}}if(e.keepAttributes){let t="bulletList"===e.type.name||"orderedList"===e.type.name?"listItem":"taskList";i().updateAttributes(t,o).run()}let d=s.doc.resolve(n.from-1).nodeBefore;d&&d.type===e.type&&(0,l.n9)(s.doc,n.from-1)&&(!e.joinPredicate||e.joinPredicate(r,d))&&s.join(n.from-1)}})}class e7{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=X(j(this,"addOptions",{name:this.name}))),this.storage=X(j(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new e7(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>ea(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new e7(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=X(j(t,"addOptions",{name:t.name})),t.storage=X(j(t,"addStorage",{name:t.name,options:t.options})),t}}function te(e){return new ed({find:e.find,handler:({state:t,range:n,match:r,pasteEvent:i})=>{let o=X(e.getAttributes,void 0,r,i);if(!1===o||null===o)return null;let{tr:s}=t,l=r[r.length-1],a=r[0],h=n.to;if(l){let r=a.search(/\S/),i=n.from+a.indexOf(l),d=i+l.length;if(e$(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>i).length)return null;d<n.to&&s.delete(d,n.to),i>n.from&&s.delete(n.from+r,i),h=n.from+r+l.length,s.addMark(n.from+r,h,e.type.create(o||{})),s.removeStoredMark(e.type)}}})}},47920:(e,t,n)=>{n.d(t,{$Z:()=>m,hG:()=>C});var r,i,o=n(58009),s=n(55740),l=n(76142),a={exports:{}},h={};a.exports=function(){if(r)return h;r=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=o.useState,n=o.useEffect,i=o.useLayoutEffect,s=o.useDebugValue;function l(t){var n=t.getSnapshot;t=t.value;try{var r=n();return!e(t,r)}catch(e){return!0}}var a="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,r){var o=r(),a=t({inst:{value:o,getSnapshot:r}}),h=a[0].inst,d=a[1];return i(function(){h.value=o,h.getSnapshot=r,l(h)&&d({inst:h})},[e,o,r]),n(function(){return l(h)&&d({inst:h}),e(function(){l(h)&&d({inst:h})})},[e]),s(o),o};return h.useSyncExternalStore=void 0!==o.useSyncExternalStore?o.useSyncExternalStore:a,h}();var d=a.exports;let c=(...e)=>t=>{e.forEach(e=>{"function"==typeof e?e(t):e&&(e.current=t)})},p=({contentComponent:e})=>{let t=d.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getServerSnapshot);return o.createElement(o.Fragment,null,Object.values(t))};class u extends o.Component{constructor(e){var t;super(e),this.editorContentRef=o.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null===(t=e.editor)||void 0===t?void 0:t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;let t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=function(){let e=new Set,t={};return{subscribe:t=>(e.add(t),()=>{e.delete(t)}),getSnapshot:()=>t,getServerSnapshot:()=>t,setRenderer(n,r){t={...t,[n]:s.createPortal(r.reactElement,r.element,n)},e.forEach(e=>e())},removeRenderer(n){let r={...t};delete r[n],t=r,e.forEach(e=>e())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe(()=>{this.setState(e=>e.hasContentComponentInitialized?e:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let e=this.props.editor;if(!e||(this.initialized=!1,e.isDestroyed||e.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),e.contentComponent=null,!e.options.element.firstChild))return;let t=document.createElement("div");t.append(...e.options.element.childNodes),e.setOptions({element:t})}render(){let{editor:e,innerRef:t,...n}=this.props;return o.createElement(o.Fragment,null,o.createElement("div",{ref:c(t,this.editorContentRef),...n}),(null==e?void 0:e.contentComponent)&&o.createElement(p,{contentComponent:e.contentComponent}))}}let f=(0,o.forwardRef)((e,t)=>{let n=o.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[e.editor]);return o.createElement(u,{key:n,innerRef:t,...e})}),m=o.memo(f);var g=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(!e(t[i],n[i]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;for(i of t.entries())if(!e(i[1],n.get(i[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(t[i]!==n[i])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var r,i,o,s=o[i];if(("_owner"!==s||!t.$$typeof)&&!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n}),y={exports:{}},v={};y.exports=function(){if(i)return v;i=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=d.useSyncExternalStore,n=o.useRef,r=o.useEffect,s=o.useMemo,l=o.useDebugValue;return v.useSyncExternalStoreWithSelector=function(i,o,a,h,d){var c=n(null);if(null===c.current){var p={hasValue:!1,value:null};c.current=p}else p=c.current;var u=t(i,(c=s(function(){function t(t){if(!i){if(i=!0,n=t,t=h(t),void 0!==d&&p.hasValue){var o=p.value;if(d(o,t))return r=o}return r=t}if(o=r,e(n,t))return o;var s=h(t);return void 0!==d&&d(o,s)?o:(n=t,r=s)}var n,r,i=!1,s=void 0===a?null:a;return[function(){return t(o())},null===s?void 0:function(){return t(s())}]},[o,a,h,d]))[0],c[1]);return r(function(){p.hasValue=!0,p.value=u},[u]),l(u),u},v}();var b=y.exports;let w="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;class k{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){let e=()=>{this.transactionNumber+=1,this.subscribers.forEach(e=>e())},t=this.editor;return t.on("transaction",e),()=>{t.off("transaction",e)}}}}let x="undefined"==typeof window,S=x||!!("undefined"!=typeof window&&window.next);class M{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(e=>e())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?x||S?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){let e={...this.options.current,onBeforeCreate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onBeforeCreate)||void 0===n?void 0:n.call(t,...e)},onBlur:(...e)=>{var t,n;return null===(n=(t=this.options.current).onBlur)||void 0===n?void 0:n.call(t,...e)},onCreate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onCreate)||void 0===n?void 0:n.call(t,...e)},onDestroy:(...e)=>{var t,n;return null===(n=(t=this.options.current).onDestroy)||void 0===n?void 0:n.call(t,...e)},onFocus:(...e)=>{var t,n;return null===(n=(t=this.options.current).onFocus)||void 0===n?void 0:n.call(t,...e)},onSelectionUpdate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onSelectionUpdate)||void 0===n?void 0:n.call(t,...e)},onTransaction:(...e)=>{var t,n;return null===(n=(t=this.options.current).onTransaction)||void 0===n?void 0:n.call(t,...e)},onUpdate:(...e)=>{var t,n;return null===(n=(t=this.options.current).onUpdate)||void 0===n?void 0:n.call(t,...e)},onContentError:(...e)=>{var t,n;return null===(n=(t=this.options.current).onContentError)||void 0===n?void 0:n.call(t,...e)},onDrop:(...e)=>{var t,n;return null===(n=(t=this.options.current).onDrop)||void 0===n?void 0:n.call(t,...e)},onPaste:(...e)=>{var t,n;return null===(n=(t=this.options.current).onPaste)||void 0===n?void 0:n.call(t,...e)}};return new l.KE(e)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&e.extensions&&t.extensions?e.extensions.length===t.extensions.length&&e.extensions.every((e,n)=>{var r;return e===(null===(r=t.extensions)||void 0===r?void 0:r[n])}):e[n]===t[n]))}onRender(e){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===e.length?M.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(e),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=e;return}if(this.previousDeps.length===e.length&&this.previousDeps.every((t,n)=>t===e[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){let e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===e){t&&t.setOptions(this.options.current);return}t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))},1)}}function C(e={},t=[]){let n=(0,o.useRef)(e);n.current=e;let[r]=(0,o.useState)(()=>new M(n)),i=d.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return(0,o.useDebugValue)(i),(0,o.useEffect)(r.onRender(t)),function(e){var t;let[n]=(0,o.useState)(()=>new k(e.editor)),r=b.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,null!==(t=e.equalityFn)&&void 0!==t?t:g);w(()=>n.watch(e.editor),[e.editor,n]),(0,o.useDebugValue)(r)}({editor:i,selector:({transactionNumber:t})=>!1===e.shouldRerenderOnTransaction?null:e.immediatelyRender&&0===t?0:t+1}),i}let O=((0,o.createContext)({editor:null}).Consumer,(0,o.createContext)({onDragStart:void 0})),T=()=>(0,o.useContext)(O);o.forwardRef((e,t)=>{let{onDragStart:n}=T(),r=e.as||"div";return o.createElement(r,{...e,ref:t,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...e.style}})})},27042:(e,t,n)=>{n.d(t,{A:()=>ey});var r=n(76142);let i=/^\s*>\s$/,o=r.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return["blockquote",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,r.tG)({find:i,type:this.type})]}}),s=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,l=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,a=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,h=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,d=r.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return["strong",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,r.OX)({find:s,type:this.type}),(0,r.OX)({find:a,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:l,type:this.type}),(0,r.Zc)({find:h,type:this.type})]}}),c="textStyle",p=/^\s*([-+*])\s$/,u=r.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(c)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=(0,r.tG)({find:p,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,r.tG)({find:p,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(c),editor:this.editor})),[e]}}),f=/(^|[^`])`([^`]+)`(?!`)/,m=/(^|[^`])`([^`]+)`(?!`)/g,g=r.CU.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:e}){return["code",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setCode:()=>({commands:e})=>e.setMark(this.name),toggleCode:()=>({commands:e})=>e.toggleMark(this.name),unsetCode:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,r.OX)({find:f,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:m,type:this.type})]}});var y=n(53786);let v=/^```([a-z]+)?[\s\n]$/,b=/^~~~([a-z]+)?[\s\n]$/,w=r.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;let{languageClassPrefix:n}=this.options;return[...(null===(t=e.firstElementChild)||void 0===t?void 0:t.classList)||[]].filter(e=>e.startsWith(n)).map(e=>e.replace(n,""))[0]||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",(0,r.KV)(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!!e&&t.parent.type.name===this.name&&(!!n||!t.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:t}=e,{selection:n}=t,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;let o=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith("\n\n");return!!o&&!!s&&e.chain().command(({tr:e})=>(e.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;let{state:t}=e,{selection:n,doc:r}=t,{$from:i,empty:o}=n;if(!o||i.parent.type!==this.type||i.parentOffset!==i.parent.nodeSize-2)return!1;let s=i.after();return void 0!==s&&(r.nodeAt(s)?e.commands.command(({tr:e})=>(e.setSelection(y.LN.near(r.resolve(s))),!0)):e.commands.exitCode())}}},addInputRules(){return[(0,r.JJ)({find:v,type:this.type,getAttributes:e=>({language:e[1]})}),(0,r.JJ)({find:b,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new y.k_({key:new y.hs("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData||this.editor.isActive(this.type.name))return!1;let n=t.clipboardData.getData("text/plain"),r=t.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,o=null==i?void 0:i.mode;if(!n||!o)return!1;let{tr:s,schema:l}=e.state,a=l.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:o},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(y.U3.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),e.dispatch(s),!0}}})]}}),k=r.bP.create({name:"doc",topNode:!0,content:"block+"});var x=n(8789);class S{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!==(n=t.width)&&void 0!==n?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t,n=this.editorView.state.doc.resolve(this.cursorPos),r=!n.parent.inlineContent,i;if(r){let e=n.nodeBefore,t=n.nodeAfter;if(e||t){let n=this.editorView.nodeDOM(this.cursorPos-(e?e.nodeSize:0));if(n){let r=n.getBoundingClientRect(),o=e?r.bottom:r.top;e&&t&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2),i={left:r.left,right:r.right,top:o-this.width/2,bottom:o+this.width/2}}}}if(!i){let e=this.editorView.coordsAtPos(this.cursorPos);i={left:e.left-this.width/2,right:e.left+this.width/2,top:e.top,bottom:e.bottom}}let o=this.editorView.dom.offsetParent;if(!this.element&&(this.element=o.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",r),this.element.classList.toggle("prosemirror-dropcursor-inline",!r),o&&(o!=document.body||"static"!=getComputedStyle(o).position)){let n=o.getBoundingClientRect();e=n.left-o.scrollLeft,t=n.top-o.scrollTop}else e=-pageXOffset,t=-pageYOffset;this.element.style.left=i.left-e+"px",this.element.style.top=i.top-t+"px",this.element.style.width=i.right-i.left+"px",this.element.style.height=i.bottom-i.top+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),r=n&&n.type.spec.disableDropCursor,i="function"==typeof r?r(this.editorView,t,e):r;if(t&&!i){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=(0,x.Um)(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){e.target!=this.editorView.dom&&this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}let M=r.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(e={}){return new y.k_({view:t=>new S(t,e)})}(this.options)]}});var C=n(56884),O=n(88434),T=n(45966);class N extends y.LN{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return N.valid(n)?new N(n):y.LN.near(n)}content(){return O.Ji.empty}eq(e){return e instanceof N&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new N(e.resolve(t.pos))}getBookmark(){return new E(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),r=e.node(t);if(0==n){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),r=e.node(t);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let r=t.contentMatchAt(e.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(e,t,n=!1){e:for(;;){if(!n&&N.valid(e))return e;let r=e.pos,i=null;for(let n=e.depth;;n--){let o=e.node(n);if(t>0?e.indexAfter(n)<o.childCount:e.index(n)>0){i=o.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;r+=t;let s=e.doc.resolve(r);if(N.valid(s))return s}for(;;){let o=t>0?i.firstChild:i.lastChild;if(!o){if(i.isAtom&&!i.isText&&!y.nh.isSelectable(i)){e=e.doc.resolve(r+i.nodeSize*t),n=!1;continue e}break}i=o,r+=t;let s=e.doc.resolve(r);if(N.valid(s))return s}return null}}}N.prototype.visible=!1,N.findFrom=N.findGapCursorFrom,y.LN.jsonID("gapcursor",N);class E{constructor(e){this.pos=e}map(e){return new E(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return N.valid(t)?new N(t):y.LN.near(t)}}let A=(0,C.K)({ArrowLeft:D("horiz",-1),ArrowRight:D("horiz",1),ArrowUp:D("vert",-1),ArrowDown:D("vert",1)});function D(e,t){let n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,r,i){let o=e.selection,s=t>0?o.$to:o.$from,l=o.empty;if(o instanceof y.U3){if(!i.endOfTextblock(n)||0==s.depth)return!1;l=!1,s=e.doc.resolve(t>0?s.after():s.before())}let a=N.findGapCursorFrom(s,t,l);return!!a&&(r&&r(e.tr.setSelection(new N(a))),!0)}}function I(e,t,n){if(!e||!e.editable)return!1;let r=e.state.doc.resolve(t);if(!N.valid(r))return!1;let i=e.posAtCoords({left:n.clientX,top:n.clientY});return!(i&&i.inside>-1&&y.nh.isSelectable(e.state.doc.nodeAt(i.inside)))&&(e.dispatch(e.state.tr.setSelection(new N(r))),!0)}function R(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof N))return!1;let{$from:n}=e.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!r)return!1;let i=O.FK.empty;for(let e=r.length-1;e>=0;e--)i=O.FK.from(r[e].createAndFill(null,i));let o=e.state.tr.replace(n.pos,n.pos,new O.Ji(i,0,0));return o.setSelection(y.U3.near(o.doc.resolve(n.pos+1))),e.dispatch(o),!1}function P(e){if(!(e.selection instanceof N))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",T.zF.create(e.doc,[T.NZ.widget(e.selection.head,t,{key:"gapcursor"})])}let z=r.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new y.k_({props:{decorations:P,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&N.valid(n)?new N(n):null,handleClick:I,handleKeyDown:A,handleDOMEvents:{beforeinput:R}}})],extendNodeSchema(e){var t;let n={name:e.name,options:e.options,storage:e.storage};return{allowGapCursor:null!==(t=(0,r.gk)((0,r.iI)(e,"allowGapCursor",n)))&&void 0!==t?t:null}}}),B=r.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",(0,r.KV)(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:r})=>e.first([()=>e.exitCode(),()=>e.command(()=>{let{selection:e,storedMarks:i}=n;if(e.$from.parent.type.spec.isolating)return!1;let{keepMarks:o}=this.options,{splittableMarks:s}=r.extensionManager,l=i||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command(({tr:e,dispatch:t})=>{if(t&&l&&o){let t=l.filter(e=>s.includes(e.type.name));e.ensureMarks(t)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),F=r.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(e=>({tag:`h${e}`,attrs:{level:e}}))},renderHTML({node:e,HTMLAttributes:t}){let n=this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0];return[`h${n}`,(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce((e,t)=>({...e,[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})}),{})},addInputRules(){return this.options.levels.map(e=>(0,r.JJ)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}}))}});var L=function(){};L.prototype.append=function(e){return e.length?(e=L.from(e),!this.length&&e||e.length<200&&this.leafAppend(e)||this.length<200&&e.leafPrepend(this)||this.appendInner(e)):this},L.prototype.prepend=function(e){return e.length?L.from(e).append(this):this},L.prototype.appendInner=function(e){return new V(this,e)},L.prototype.slice=function(e,t){return(void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t)?L.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},L.prototype.get=function(e){if(!(e<0)&&!(e>=this.length))return this.getInner(e)},L.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},L.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(t,n){return r.push(e(t,n))},t,n),r},L.from=function(e){return e instanceof L?e:e&&e.length?new $(e):L.empty};var $=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,r){for(var i=t;i<n;i++)if(!1===e(this.values[i],r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){for(var i=t-1;i>=n;i--)if(!1===e(this.values[i],r+i))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=200)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=200)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}(L);L.empty=new $([]);var V=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,r){var i=this.left.length;if(t<i&&!1===this.left.forEachInner(e,t,Math.min(n,i),r)||n>i&&!1===this.right.forEachInner(e,Math.max(t-i,0),Math.min(this.length,n)-i,r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){var i=this.left.length;if(t>i&&!1===this.right.forEachInvertedInner(e,t-i,Math.max(n,i)-i,r+i)||n<i&&!1===this.left.forEachInvertedInner(e,Math.min(t,i),n,r))return!1},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}(L);class J{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){let n,r,i,o;if(0==this.eventCount)return null;let s=this.items.length;for(;;s--)if(this.items.get(s-1).selection){--s;break}t&&(r=(n=this.remapping(s,this.items.length)).maps.length);let l=e.tr,a=[],h=[];return this.items.forEach((e,t)=>{if(!e.step){n||(r=(n=this.remapping(s,t+1)).maps.length),r--,h.push(e);return}if(n){h.push(new K(e.map));let t=e.step.map(n.slice(r)),i;t&&l.maybeStep(t).doc&&(i=l.mapping.maps[l.mapping.maps.length-1],a.push(new K(i,void 0,void 0,a.length+h.length))),r--,i&&n.appendMap(i,r)}else l.maybeStep(e.step);if(e.selection)return i=n?e.selection.map(n.slice(r)):e.selection,o=new J(this.items.slice(0,s).append(h.reverse().concat(a)),this.eventCount-1),!1},this.items.length,0),{remaining:o,transform:l,selection:i}}addTransform(e,t,n,r){var i,o;let s,l=[],a=this.eventCount,h=this.items,d=!r&&h.length?h.get(h.length-1):null;for(let n=0;n<e.steps.length;n++){let i=e.steps[n].invert(e.docs[n]),o=new K(e.mapping.maps[n],i,t),s;(s=d&&d.merge(o))&&(o=s,n?l.pop():h=h.slice(0,h.length-1)),l.push(o),t&&(a++,t=void 0),r||(d=o)}let c=a-n.depth;return c>H&&(i=h,o=c,i.forEach((e,t)=>{if(e.selection&&0==o--)return s=t,!1}),h=i.slice(s),a-=c),new J(h.append(l),a)}remapping(e,t){let n=new x.X9;return this.items.forEach((t,r)=>{let i=null!=t.mirrorOffset&&r-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,i)},e,t),n}addMaps(e){return 0==this.eventCount?this:new J(this.items.append(e.map(e=>new K(e))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),i=e.mapping,o=e.steps.length,s=this.eventCount;this.items.forEach(e=>{e.selection&&s--},r);let l=t;this.items.forEach(t=>{let r=i.getMirror(--l);if(null==r)return;o=Math.min(o,r);let a=i.maps[r];if(t.step){let o=e.steps[r].invert(e.docs[r]),h=t.selection&&t.selection.map(i.slice(l+1,r));h&&s++,n.push(new K(a,o,h))}else n.push(new K(a))},r);let a=[];for(let e=t;e<o;e++)a.push(new K(i.maps[e]));let h=new J(this.items.slice(0,r).append(a).append(n),s);return h.emptyItemCount()>500&&(h=h.compress(this.items.length-n.length)),h}emptyItemCount(){let e=0;return this.items.forEach(t=>{!t.step&&e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],i=0;return this.items.forEach((o,s)=>{if(s>=e)r.push(o),o.selection&&i++;else if(o.step){let e=o.step.map(t.slice(n)),s=e&&e.getMap();if(n--,s&&t.appendMap(s,n),e){let l=o.selection&&o.selection.map(t.slice(n));l&&i++;let a=new K(s.invert(),e,l),h,d=r.length-1;(h=r.length&&r[d].merge(a))?r[d]=h:r.push(a)}}else o.map&&n--},this.items.length,0),new J(L.from(r.reverse()),i)}}J.empty=new J(L.empty,0);class K{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new K(t.getMap().invert(),t,this.selection)}}}class j{constructor(e,t,n,r,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=i}}let H=20;function W(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach((e,n,r,i)=>t.push(r,i));return t}function q(e,t){if(!e)return null;let n=[];for(let r=0;r<e.length;r+=2){let i=t.map(e[r],1),o=t.map(e[r+1],-1);i<=o&&n.push(i,o)}return n}let _=!1,U=null;function G(e){let t=e.plugins;if(U!=t){_=!1,U=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){_=!0;break}}return _}let X=new y.hs("history"),Y=new y.hs("closeHistory");function Z(e,t){return(n,r)=>{let i=X.getState(n);if(!i||0==(e?i.undone:i.done).eventCount)return!1;if(r){let o=function(e,t,n){let r=G(t),i=X.get(t).spec.config,o=(n?e.undone:e.done).popEvent(t,r);if(!o)return null;let s=o.selection.resolve(o.transform.doc),l=(n?e.done:e.undone).addTransform(o.transform,t.selection.getBookmark(),i,r),a=new j(n?l:o.remaining,n?o.remaining:l,null,0,-1);return o.transform.setSelection(s).setMeta(X,{redo:n,historyState:a})}(i,n,e);o&&r(t?o.scrollIntoView():o)}return!0}}let Q=Z(!1,!0),ee=Z(!0,!0);Z(!1,!1),Z(!0,!1);let et=r.YY.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>Q(e,t),redo:()=>({state:e,dispatch:t})=>ee(e,t)}),addProseMirrorPlugins(){return[function(e={}){return e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},new y.k_({key:X,state:{init:()=>new j(J.empty,J.empty,null,0,-1),apply:(t,n,r)=>(function(e,t,n,r){let i=n.getMeta(X),o;if(i)return i.historyState;n.getMeta(Y)&&(e=new j(e.done,e.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(s&&s.getMeta(X))return s.getMeta(X).redo?new j(e.done.addTransform(n,void 0,r,G(t)),e.undone,W(n.mapping.maps),e.prevTime,e.prevComposition):new j(e.done,e.undone.addTransform(n,void 0,r,G(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))return(o=n.getMeta("rebased"))?new j(e.done.rebased(n,o),e.undone.rebased(n,o),q(e.prevRanges,n.mapping),e.prevTime,e.prevComposition):new j(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),q(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let i=n.getMeta("composition"),o=0==e.prevTime||!s&&e.prevComposition!=i&&(e.prevTime<(n.time||0)-r.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach((e,r)=>{for(let i=0;i<t.length;i+=2)e<=t[i+1]&&r>=t[i]&&(n=!0)}),n}(n,e.prevRanges)),l=s?q(e.prevRanges,n.mapping):W(n.mapping.maps);return new j(e.done.addTransform(n,o?t.selection.getBookmark():void 0,r,G(t)),J.empty,l,n.time,null==i?e.prevComposition:i)}})(n,r,t,e)},config:e,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r="historyUndo"==n?Q:"historyRedo"==n?ee:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),en=r.bP.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:e}){return["hr",(0,r.KV)(this.options.HTMLAttributes,e)]},addCommands(){return{setHorizontalRule:()=>({chain:e,state:t})=>{let{selection:n}=t,{$from:i,$to:o}=n,s=e();return 0===i.parentOffset?s.insertContentAt({from:Math.max(i.pos-1,0),to:o.pos},{type:this.name}):(0,r.BQ)(n)?s.insertContentAt(o.pos,{type:this.name}):s.insertContent({type:this.name}),s.command(({tr:e,dispatch:t})=>{var n;if(t){let{$to:t}=e.selection,r=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?e.setSelection(y.U3.create(e.doc,t.pos+1)):t.nodeAfter.isBlock?e.setSelection(y.nh.create(e.doc,t.pos)):e.setSelection(y.U3.create(e.doc,t.pos));else{let i=null===(n=t.parent.type.contentMatch.defaultType)||void 0===n?void 0:n.create();i&&(e.insert(r,i),e.setSelection(y.U3.create(e.doc,r+1)))}e.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,r.jT)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),er=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,ei=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,eo=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,es=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,el=r.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,r.OX)({find:er,type:this.type}),(0,r.OX)({find:eo,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:ei,type:this.type}),(0,r.Zc)({find:es,type:this.type})]}}),ea=r.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",(0,r.KV)(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),eh="textStyle",ed=/^(\d+)\.\s$/,ec=r.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:void 0,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){let{start:t,...n}=e;return 1===t?["ol",(0,r.KV)(this.options.HTMLAttributes,n),0]:["ol",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(eh)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=(0,r.tG)({find:ed,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=(0,r.tG)({find:ed,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(eh)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),ep=r.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),eu=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,ef=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,em=r.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,r.OX)({find:eu,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:ef,type:this.type})]}}),eg=r.bP.create({name:"text",group:"inline"}),ey=r.YY.create({name:"starterKit",addExtensions(){var e,t,n,r,i,s,l,a,h,c,p,f,m,y,v,b,x,S;let C=[];return!1!==this.options.bold&&C.push(d.configure(null===(e=this.options)||void 0===e?void 0:e.bold)),!1!==this.options.blockquote&&C.push(o.configure(null===(t=this.options)||void 0===t?void 0:t.blockquote)),!1!==this.options.bulletList&&C.push(u.configure(null===(n=this.options)||void 0===n?void 0:n.bulletList)),!1!==this.options.code&&C.push(g.configure(null===(r=this.options)||void 0===r?void 0:r.code)),!1!==this.options.codeBlock&&C.push(w.configure(null===(i=this.options)||void 0===i?void 0:i.codeBlock)),!1!==this.options.document&&C.push(k.configure(null===(s=this.options)||void 0===s?void 0:s.document)),!1!==this.options.dropcursor&&C.push(M.configure(null===(l=this.options)||void 0===l?void 0:l.dropcursor)),!1!==this.options.gapcursor&&C.push(z.configure(null===(a=this.options)||void 0===a?void 0:a.gapcursor)),!1!==this.options.hardBreak&&C.push(B.configure(null===(h=this.options)||void 0===h?void 0:h.hardBreak)),!1!==this.options.heading&&C.push(F.configure(null===(c=this.options)||void 0===c?void 0:c.heading)),!1!==this.options.history&&C.push(et.configure(null===(p=this.options)||void 0===p?void 0:p.history)),!1!==this.options.horizontalRule&&C.push(en.configure(null===(f=this.options)||void 0===f?void 0:f.horizontalRule)),!1!==this.options.italic&&C.push(el.configure(null===(m=this.options)||void 0===m?void 0:m.italic)),!1!==this.options.listItem&&C.push(ea.configure(null===(y=this.options)||void 0===y?void 0:y.listItem)),!1!==this.options.orderedList&&C.push(ec.configure(null===(v=this.options)||void 0===v?void 0:v.orderedList)),!1!==this.options.paragraph&&C.push(ep.configure(null===(b=this.options)||void 0===b?void 0:b.paragraph)),!1!==this.options.strike&&C.push(em.configure(null===(x=this.options)||void 0===x?void 0:x.strike)),!1!==this.options.text&&C.push(eg.configure(null===(S=this.options)||void 0===S?void 0:S.text)),C}})},56884:(e,t,n)=>{n.d(t,{K:()=>u,w:()=>p});for(var r={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},i={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},o="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),s="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),l=0;l<10;l++)r[48+l]=r[96+l]=String(l);for(var l=1;l<=24;l++)r[l+111]="F"+l;for(var l=65;l<=90;l++)r[l]=String.fromCharCode(l+32),i[l]=String.fromCharCode(l);for(var a in r)i.hasOwnProperty(a)||(i[a]=r[a]);var h=n(53786);let d="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function c(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function p(e){return new h.k_({props:{handleKeyDown:u(e)}})}function u(e){let t=function(e){let t=Object.create(null);for(let n in e)t[function(e){let t,n,r,i,o=e.split(/-(?!$)/),s=o[o.length-1];"Space"==s&&(s=" ");for(let e=0;e<o.length-1;e++){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))d?i=!0:n=!0;else throw Error("Unrecognized modifier name: "+s)}return t&&(s="Alt-"+s),n&&(s="Ctrl-"+s),i&&(s="Meta-"+s),r&&(s="Shift-"+s),s}(n)]=e[n];return t}(e);return function(e,n){var l;let a=("Esc"==(l=!(o&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||s&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?i:r)[n.keyCode]||n.key||"Unidentified")&&(l="Escape"),"Del"==l&&(l="Delete"),"Left"==l&&(l="ArrowLeft"),"Up"==l&&(l="ArrowUp"),"Right"==l&&(l="ArrowRight"),"Down"==l&&(l="ArrowDown"),l),h,d=t[c(a,n)];if(d&&d(e.state,e.dispatch,e))return!0;if(1==a.length&&" "!=a){if(n.shiftKey){let r=t[c(a,n,!1)];if(r&&r(e.state,e.dispatch,e))return!0}if((n.shiftKey||n.altKey||n.metaKey||a.charCodeAt(0)>127)&&(h=r[n.keyCode])&&h!=a){let r=t[c(h,n)];if(r&&r(e.state,e.dispatch,e))return!0}}return!1}}},88434:(e,t,n)=>{function r(e){this.content=e}n.d(t,{S4:()=>V,ZF:()=>G,FK:()=>i,CU:()=>a,sX:()=>F,bP:()=>S,u$:()=>k,vI:()=>h,Sj:()=>L,Ji:()=>d}),r.prototype={constructor:r,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return -1},get:function(e){var t=this.find(e);return -1==t?void 0:this.content[t+1]},update:function(e,t,n){var i=n&&n!=e?this.remove(n):this,o=i.find(e),s=i.content.slice();return -1==o?s.push(n||e,t):(s[o+1]=t,n&&(s[o]=n)),new r(s)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new r(n)},addToStart:function(e,t){return new r([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new r(n)},addBefore:function(e,t,n){var i=this.remove(t),o=i.content.slice(),s=i.find(e);return o.splice(-1==s?o.length:s,0,t,n),new r(o)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=r.from(e)).size?new r(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=r.from(e)).size?new r(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=r.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach(function(t,n){e[t]=n}),e},get size(){return this.content.length>>1}},r.from=function(e){if(e instanceof r)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new r(t)};class i{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let t=0;t<e.length;t++)this.size+=e[t].nodeSize}nodesBetween(e,t,n,r=0,i){for(let o=0,s=0;s<t;o++){let l=this.content[o],a=s+l.nodeSize;if(a>e&&!1!==n(l,r+s,i||null,o)&&l.content.size){let i=s+1;l.nodesBetween(Math.max(0,e-i),Math.min(l.content.size,t-i),n,r+i)}s=a}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let i="",o=!0;return this.nodesBetween(e,t,(s,l)=>{let a=s.isText?s.text.slice(Math.max(e,l)-l,t-l):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(o?o=!1:i+=n),i+=a},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),o=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),o=1);o<e.content.length;o++)r.push(e.content[o]);return new i(r,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],r=0;if(t>e)for(let i=0,o=0;o<t;i++){let s=this.content[i],l=o+s.nodeSize;l>e&&((o<e||l>t)&&(s=s.isText?s.cut(Math.max(0,e-o),Math.min(s.text.length,t-o)):s.cut(Math.max(0,e-o-1),Math.min(s.content.size,t-o-1))),n.push(s),r+=s.nodeSize),o=l}return new i(n,r)}cutByIndex(e,t){return e==t?i.empty:0==e&&t==this.content.length?this:new i(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),o=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new i(r,o)}addToStart(e){return new i([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new i(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return function e(t,n,r){for(let i=0;;i++){if(i==t.childCount||i==n.childCount)return t.childCount==n.childCount?null:r;let o=t.child(i),s=n.child(i);if(o==s){r+=o.nodeSize;continue}if(!o.sameMarkup(s))return r;if(o.isText&&o.text!=s.text){for(let e=0;o.text[e]==s.text[e];e++)r++;return r}if(o.content.size||s.content.size){let t=e(o.content,s.content,r+1);if(null!=t)return t}r+=o.nodeSize}}(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return function e(t,n,r,i){for(let o=t.childCount,s=n.childCount;;){if(0==o||0==s)return o==s?null:{a:r,b:i};let l=t.child(--o),a=n.child(--s),h=l.nodeSize;if(l==a){r-=h,i-=h;continue}if(!l.sameMarkup(a))return{a:r,b:i};if(l.isText&&l.text!=a.text){let e=0,t=Math.min(l.text.length,a.text.length);for(;e<t&&l.text[l.text.length-e-1]==a.text[a.text.length-e-1];)e++,r--,i--;return{a:r,b:i}}if(l.content.size||a.content.size){let t=e(l.content,a.content,r-1,i-1);if(t)return t}r-=h,i-=h}}(this,e,t,n)}findIndex(e,t=-1){if(0==e)return s(0,e);if(e==this.size)return s(this.content.length,e);if(e>this.size||e<0)throw RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,r=0;;n++){let i=r+this.child(n).nodeSize;if(i>=e){if(i==e||t>0)return s(n+1,i);return s(n,r)}r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return i.empty;if(!Array.isArray(t))throw RangeError("Invalid input for Fragment.fromJSON");return new i(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return i.empty;let t,n=0;for(let r=0;r<e.length;r++){let i=e[r];n+=i.nodeSize,r&&i.isText&&e[r-1].sameMarkup(i)?(t||(t=e.slice(0,r)),t[t.length-1]=i.withText(t[t.length-1].text+i.text)):t&&t.push(i)}return new i(t||e,n)}static from(e){if(!e)return i.empty;if(e instanceof i)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new i([e],e.nodeSize);throw RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}i.empty=new i([],0);let o={index:0,offset:0};function s(e,t){return o.index=e,o.offset=t,o}function l(e,t){if(e===t)return!0;if(!(e&&"object"==typeof e)||!(t&&"object"==typeof t))return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!l(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!l(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}class a{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let r=0;r<e.length;r++){let i=e[r];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,r));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&l(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw RangeError(`There is no mark type ${t.type} in this schema`);let r=n.create(t.attrs);return n.checkAttrs(r.attrs),r}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&0==e.length)return a.none;if(e instanceof a)return[e];let t=e.slice();return t.sort((e,t)=>e.type.rank-t.type.rank),t}}a.none=[];class h extends Error{}class d{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=function e(t,n,r,i){let{index:o,offset:s}=t.findIndex(n),l=t.maybeChild(o);if(s==n||l.isText)return i&&!i.canReplace(o,o,r)?null:t.cut(0,n).append(r).append(t.cut(n));let a=e(l.content,n-s-1,r);return a&&t.replaceChild(o,l.copy(a))}(this.content,e+this.openStart,t);return n&&new d(n,this.openStart,this.openEnd)}removeBetween(e,t){return new d(function e(t,n,r){let{index:i,offset:o}=t.findIndex(n),s=t.maybeChild(i),{index:l,offset:a}=t.findIndex(r);if(o==n||s.isText){if(a!=r&&!t.child(l).isText)throw RangeError("Removing non-flat range");return t.cut(0,n).append(t.cut(r))}if(i!=l)throw RangeError("Removing non-flat range");return t.replaceChild(i,s.copy(e(s.content,n-o-1,r-o-1)))}(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return d.empty;let n=t.openStart||0,r=t.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw RangeError("Invalid input for Slice.fromJSON");return new d(i.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let r=e.firstChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=e.lastChild;n&&!n.isLeaf&&(t||!n.type.spec.isolating);n=n.lastChild)r++;return new d(e,n,r)}}function c(e,t){if(!t.type.compatibleContent(e.type))throw new h("Cannot join "+t.type.name+" onto "+e.type.name)}function p(e,t,n){let r=e.node(n);return c(r,t.node(n)),r}function u(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function f(e,t,n,r){let i=(t||e).node(n),o=0,s=t?t.index(n):i.childCount;e&&(o=e.index(n),e.depth>n?o++:e.textOffset&&(u(e.nodeAfter,r),o++));for(let e=o;e<s;e++)u(i.child(e),r);t&&t.depth==n&&t.textOffset&&u(t.nodeBefore,r)}function m(e,t){return e.type.checkContent(t),e.copy(t)}function g(e,t,n){let r=[];return f(null,e,n,r),e.depth>n&&u(m(p(e,t,n+1),g(e,t,n+1)),r),f(t,null,n,r),new i(r)}d.empty=new d(i.empty,0,0);class y{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1;for(let t=0;t<e;t++)r+=n.child(t).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return a.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let e=n;n=r,r=e}let i=n.marks;for(var o=0;o<i.length;o++)!1!==i[o].type.spec.inclusive||r&&i[o].isInSet(r.marks)||(i=i[o--].removeFromSet(i));return i}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new k(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw RangeError("Position "+t+" out of range");let n=[],r=0,i=t;for(let t=e;;){let{index:e,offset:o}=t.content.findIndex(i),s=i-o;if(n.push(t,e,r+o),!s||(t=t.child(e)).isText)break;i=s-1,r+=o+1}return new y(t,n,i)}static resolveCached(e,t){let n=w.get(e);if(n)for(let e=0;e<n.elts.length;e++){let r=n.elts[e];if(r.pos==t)return r}else w.set(e,n=new v);let r=n.elts[n.i]=y.resolve(e,t);return n.i=(n.i+1)%b,r}}class v{constructor(){this.elts=[],this.i=0}}let b=12,w=new WeakMap;class k{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let x=Object.create(null);class S{constructor(e,t,n,r=a.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||i.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&l(this.attrs,t||e.defaultAttrs||x)&&a.sameSet(this.marks,n||a.none)}copy(e=null){return e==this.content?this:new S(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new S(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return d.empty;let r=this.resolve(e),i=this.resolve(t),o=n?0:r.sharedDepth(t),s=r.start(o);return new d(r.node(o).content.cut(r.pos-s,i.pos-s),r.depth-o,i.depth-o)}replace(e,t,n){return function(e,t,n){if(n.openStart>e.depth)throw new h("Inserted content deeper than insertion position");if(e.depth-n.openStart!=t.depth-n.openEnd)throw new h("Inconsistent open depths");return function e(t,n,r,o){let s=t.index(o),l=t.node(o);if(s==n.index(o)&&o<t.depth-r.openStart){let i=e(t,n,r,o+1);return l.copy(l.content.replaceChild(s,i))}if(!r.content.size)return m(l,g(t,n,o));if(r.openStart||r.openEnd||t.depth!=o||n.depth!=o){let{start:e,end:s}=function(e,t){let n=t.depth-e.openStart,r=t.node(n).copy(e.content);for(let e=n-1;e>=0;e--)r=t.node(e).copy(i.from(r));return{start:r.resolveNoCache(e.openStart+n),end:r.resolveNoCache(r.content.size-e.openEnd-n)}}(r,t);return m(l,function e(t,n,r,o,s){let l=t.depth>s&&p(t,n,s+1),a=o.depth>s&&p(r,o,s+1),h=[];return f(null,t,s,h),l&&a&&n.index(s)==r.index(s)?(c(l,a),u(m(l,e(t,n,r,o,s+1)),h)):(l&&u(m(l,g(t,n,s+1)),h),f(n,r,s,h),a&&u(m(a,g(r,o,s+1)),h)),f(o,null,s,h),new i(h)}(t,e,s,n,o))}{let e=t.parent,i=e.content;return m(e,i.cut(0,t.parentOffset).append(r.content).append(i.cut(n.parentOffset)))}}(e,t,n,0)}(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(!(t=t.maybeChild(n)))return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return y.resolveCached(this,e)}resolveNoCache(e){return y.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,e=>(n.isInSet(e.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),C(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=i.empty,r=0,o=n.childCount){let s=this.contentMatchAt(e).matchFragment(n,r,o),l=s&&s.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let e=r;e<o;e++)if(!this.type.allowsMarks(n.child(e).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(e).matchType(n),o=i&&i.matchFragment(this.content,t);return!!o&&o.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=a.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!a.sameSet(e,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(e=>e.type.name)}`);this.content.forEach(e=>e.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(e=>e.toJSON())),e}static fromJSON(e,t){let n;if(!t)throw RangeError("Invalid input for Node.fromJSON");if(t.marks){if(!Array.isArray(t.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=i.fromJSON(e,t.content),o=e.nodeType(t.type).create(t.attrs,r,n);return o.type.checkAttrs(o.attrs),o}}S.prototype.text=void 0;class M extends S{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):C(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new M(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new M(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function C(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class O{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){var n;let r,i=new T(e,t);if(null==i.next)return O.empty;let o=function e(t){let n=[];do n.push(function(t){let n=[];do n.push(function(t){let n=function(t){if(t.eat("(")){let n=e(t);return t.eat(")")||t.err("Missing closing paren"),n}if(/\W/.test(t.next))t.err("Unexpected token '"+t.next+"'");else{let e=(function(e,t){let n=e.nodeTypes,r=n[t];if(r)return[r];let i=[];for(let e in n){let r=n[e];r.isInGroup(t)&&i.push(r)}return 0==i.length&&e.err("No node type or group '"+t+"' found"),i})(t,t.next).map(e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e}));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}}(t);for(;;)if(t.eat("+"))n={type:"plus",expr:n};else if(t.eat("*"))n={type:"star",expr:n};else if(t.eat("?"))n={type:"opt",expr:n};else if(t.eat("{"))n=function(e,t){let n=N(e),r=n;return e.eat(",")&&(r="}"!=e.next?N(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:t}}(t,n);else break;return n}(t));while(t.next&&")"!=t.next&&"|"!=t.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(t));while(t.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(i);i.next&&i.err("Unexpected trailing text");let s=(n=function(e){let t=[[]];return i(function e(t,o){if("choice"==t.type)return t.exprs.reduce((t,n)=>t.concat(e(n,o)),[]);if("seq"==t.type)for(let r=0;;r++){let s=e(t.exprs[r],o);if(r==t.exprs.length-1)return s;i(s,o=n())}else if("star"==t.type){let s=n();return r(o,s),i(e(t.expr,s),s),[r(s)]}else if("plus"==t.type){let s=n();return i(e(t.expr,o),s),i(e(t.expr,s),s),[r(s)]}else if("opt"==t.type)return[r(o)].concat(e(t.expr,o));else if("range"==t.type){let s=o;for(let r=0;r<t.min;r++){let r=n();i(e(t.expr,s),r),s=r}if(-1==t.max)i(e(t.expr,s),s);else for(let o=t.min;o<t.max;o++){let o=n();r(s,o),i(e(t.expr,s),o),s=o}return[r(s)]}else if("name"==t.type)return[r(o,void 0,t.value)];else throw Error("Unknown expr type")}(e,0),n()),t;function n(){return t.push([])-1}function r(e,n,r){let i={term:r,to:n};return t[e].push(i),i}function i(e,t){e.forEach(e=>e.to=t)}}(o),r=Object.create(null),function e(t){let i=[];t.forEach(e=>{n[e].forEach(({term:e,to:t})=>{let r;if(e){for(let t=0;t<i.length;t++)i[t][0]==e&&(r=i[t][1]);A(n,t).forEach(t=>{r||i.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)})}})});let o=r[t.join(",")]=new O(t.indexOf(n.length-1)>-1);for(let t=0;t<i.length;t++){let n=i[t][1].sort(E);o.next.push({type:i[t][0],next:r[n.join(",")]||e(n)})}return o}(A(n,0)));return function(e,t){for(let n=0,r=[e];n<r.length;n++){let e=r[n],i=!e.validEnd,o=[];for(let t=0;t<e.next.length;t++){let{type:n,next:s}=e.next[t];o.push(n.name),i&&!(n.isText||n.hasRequiredAttrs())&&(i=!1),-1==r.indexOf(s)&&r.push(s)}i&&t.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(s,i),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let i=t;r&&i<n;i++)r=r.matchType(e.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function o(s,l){let a=s.matchFragment(e,n);if(a&&(!t||a.validEnd))return i.from(l.map(e=>e.createAndFill()));for(let e=0;e<s.next.length;e++){let{type:t,next:n}=s.next[e];if(!(t.isText||t.hasRequiredAttrs())&&-1==r.indexOf(n)){r.push(n);let e=o(n,l.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let t=0;t<this.wrapCache.length;t+=2)if(this.wrapCache[t]==e)return this.wrapCache[t+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(e)){let e=[];for(let t=r;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<i.next.length;e++){let{type:o,next:s}=i.next[e];o.isLeaf||o.hasRequiredAttrs()||o.name in t||r.type&&!s.validEnd||(n.push({match:o.contentMatch,type:o,via:r}),t[o.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return function t(n){e.push(n);for(let r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map((t,n)=>{let r=n+(t.validEnd?"*":" ")+" ";for(let n=0;n<t.next.length;n++)r+=(n?", ":"")+t.next[n].type.name+"->"+e.indexOf(t.next[n].next);return r}).join("\n")}}O.empty=new O(!0);class T{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw SyntaxError(e+" (in content expression '"+this.string+"')")}}function N(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function E(e,t){return t-e}function A(e,t){let n=[];return function t(r){let i=e[r];if(1==i.length&&!i[0].term)return t(i[0].to);n.push(r);for(let e=0;e<i.length;e++){let{term:r,to:o}=i[e];r||-1!=n.indexOf(o)||t(o)}}(t),n.sort(E)}function D(e){let t=Object.create(null);for(let n in e){let r=e[n];if(!r.hasDefault)return null;t[n]=r.default}return t}function I(e,t){let n=Object.create(null);for(let r in e){let i=t&&t[r];if(void 0===i){let t=e[r];if(t.hasDefault)i=t.default;else throw RangeError("No value supplied for attribute "+r)}n[r]=i}return n}function R(e,t,n,r){for(let r in t)if(!(r in e))throw RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in e){let r=e[n];r.validate&&r.validate(t[n])}}function P(e,t){let n=Object.create(null);if(t)for(let r in t)n[r]=new B(e,r,t[r]);return n}class z{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=P(e,n.attrs),this.defaultAttrs=D(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==O.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:I(this.attrs,e)}create(e=null,t,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new S(this,this.computeAttrs(e),i.from(t),a.setFrom(n))}createChecked(e=null,t,n){return t=i.from(t),this.checkContent(t),new S(this,this.computeAttrs(e),t,a.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=i.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let r=this.contentMatch.matchFragment(t),o=r&&r.fillBefore(i.empty,!0);return o?new S(this,e,t.append(o),a.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let t=0;t<e.childCount;t++)if(!this.allowsMarks(e.child(t).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){R(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){let t;if(null==this.markSet)return e;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:a.none:e}static compile(e,t){let n=Object.create(null);e.forEach((e,r)=>n[e]=new z(e,t,r));let r=t.spec.topNode||"doc";if(!n[r])throw RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let e in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class B{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(e,t,n){let r=n.split("|");return n=>{let i=null===n?"null":typeof n;if(0>r.indexOf(i))throw RangeError(`Expected value of type ${r} for attribute ${t} on type ${e}, got ${i}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class F{constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=P(e,r.attrs),this.excluded=null;let i=D(this.attrs);this.instance=i?new a(this,i):null}create(e=null){return!e&&this.instance?this.instance:new a(this,I(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach((e,i)=>n[e]=new F(e,r++,t,i)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){R(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class L{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let n in e)t[n]=e[n];t.nodes=r.from(e.nodes),t.marks=r.from(e.marks||{}),this.nodes=z.compile(this.spec.nodes,this),this.marks=F.compile(this.spec.marks,this);let n=Object.create(null);for(let e in this.nodes){if(e in this.marks)throw RangeError(e+" can not be both a node and a mark");let t=this.nodes[e],r=t.spec.content||"",i=t.spec.marks;if(t.contentMatch=n[r]||(n[r]=O.parse(r,this.nodes)),t.inlineContent=t.contentMatch.inlineContent,t.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!t.isInline||!t.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=t}t.markSet="_"==i?null:i?$(this,i.split(" ")):""!=i&&t.inlineContent?null:[]}for(let e in this.marks){let t=this.marks[e],n=t.spec.excludes;t.excluded=null==n?[t]:""==n?[]:$(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if("string"==typeof e)e=this.nodeType(e);else if(e instanceof z){if(e.schema!=this)throw RangeError("Node type from different schema used ("+e.name+")")}else throw RangeError("Invalid node type: "+e);return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new M(n,n.defaultAttrs,e,a.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return S.fromJSON(this,e)}markFromJSON(e){return a.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw RangeError("Unknown node type: "+e);return t}}function $(e,t){let n=[];for(let r=0;r<t.length;r++){let i=t[r],o=e.marks[i],s=o;if(o)n.push(o);else for(let t in e.marks){let r=e.marks[t];("_"==i||r.spec.group&&r.spec.group.split(" ").indexOf(i)>-1)&&n.push(s=r)}if(!s)throw SyntaxError("Unknown mark type: '"+t[r]+"'")}return n}class V{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(e=>{if(null!=e.tag)this.tags.push(e);else if(null!=e.style){let t=/[^=]*/.exec(e.style)[0];0>n.indexOf(t)&&n.push(t),this.styles.push(e)}}),this.normalizeLists=!this.tags.some(t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)})}parse(e,t={}){let n=new q(this,t,!1);return n.addAll(e,a.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new q(this,t,!0);return n.addAll(e,a.none,t.from,t.to),d.maxOpen(n.finish())}matchTag(e,t,n){for(let i=n?this.tags.indexOf(n)+1:0;i<this.tags.length;i++){var r;let n=this.tags[i];if(r=n.tag,(e.matches||e.msMatchesSelector||e.webkitMatchesSelector||e.mozMatchesSelector).call(e,r)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],o=r.style;if(0==o.indexOf(e)&&(!r.context||n.matchesContext(r.context))&&(!(o.length>e.length)||61==o.charCodeAt(e.length)&&o.slice(e.length+1)==t)){if(r.getAttrs){let e=r.getAttrs(t);if(!1===e)continue;r.attrs=e||void 0}return r}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,r=0;for(;r<t.length;r++){let e=t[r];if((null==e.priority?50:e.priority)<n)break}t.splice(r,0,e)}for(let t in e.marks){let r=e.marks[t].spec.parseDOM;r&&r.forEach(e=>{n(e=_(e)),e.mark||e.ignore||e.clearMark||(e.mark=t)})}for(let t in e.nodes){let r=e.nodes[t].spec.parseDOM;r&&r.forEach(e=>{n(e=_(e)),e.node||e.ignore||e.mark||(e.node=t)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new V(e,V.schemaRules(e)))}}let J={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},K={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},j={ol:!0,ul:!0};function H(e,t,n){return null!=t?(t?1:0)|("full"===t?2:0):e&&"pre"==e.whitespace?3:-5&n}class W{constructor(e,t,n,r,i,o){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=o,this.content=[],this.activeMarks=a.none,this.match=i||(4&o?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(i.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let t=this.type.contentMatch,n;return(n=t.findWrapping(e.type))?(this.match=t,n):null}}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e=this.content[this.content.length-1],t;e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))&&(e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=e.withText(e.text.slice(0,e.text.length-t[0].length)))}let t=i.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(i.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!J.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class q{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r=t.topNode,i,o=H(null,t.preserveWhitespace,0)|(n?4:0);i=r?new W(r.type,r.attrs,a.none,!0,t.topMatch||r.type.contentMatch,o):n?new W(null,null,a.none,!0,null,o):new W(e.schema.topNodeType,null,a.none,!0,null,o),this.nodes=[i],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top,i=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===i||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(i)n="full"!==i?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=r.content[r.content.length-1],i=e.previousSibling;(!t||i&&"BR"==i.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r=this.localPreserveWS,i=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),s;j.hasOwnProperty(o)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&j.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let l=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(s=this.parser.matchTag(e,this,n));t:if(l?l.ignore:K.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(e=l.skip);let n,r=this.needsBlock;if(J.hasOwnProperty(o))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),n=!0,i.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break t}let s=l&&l.skip?t:this.readStyles(e,t);s&&this.addAll(e,s),n&&this.sync(i),this.needsBlock=r}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,l,n,!1===l.consuming?s:void 0)}this.localPreserveWS=r}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let e=0;e<this.parser.matchedStyles.length;e++){let r=this.parser.matchedStyles[e],i=n.getPropertyValue(r);if(i)for(let e;;){let n=this.parser.matchStyle(r,i,this,e);if(!n)break;if(n.ignore)return null;if(t=n.clearMark?t.filter(e=>!n.clearMark(e)):t.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)e=n;else break}}return t}addElementByRule(e,t,n,r){let i,o;if(t.node){if((o=this.parser.schema.nodes[t.node]).isLeaf)this.insertNode(o.create(t.attrs),n)||this.leafFallback(e,n);else{let e=this.enter(o,t.attrs||null,n,t.preserveWhitespace);e&&(i=!0,n=e)}}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let s=this.top;if(o&&o.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(e=>this.insertNode(e,n));else{let r=e;"string"==typeof t.contentElement?r=e.querySelector(t.contentElement):"function"==typeof t.contentElement?r=t.contentElement(e):t.contentElement&&(r=t.contentElement),this.findAround(e,r,!0),this.addAll(r,n),this.findAround(e,r,!1)}i&&this.sync(s)&&this.open--}addAll(e,t,n,r){let i=n||0;for(let o=n?e.childNodes[n]:e.firstChild,s=null==r?null:e.childNodes[r];o!=s;o=o.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(o,t);this.findAtPoint(e,i)}findPlace(e,t){let n,r;for(let t=this.open;t>=0;t--){let i=this.nodes[t],o=i.findWrapping(e);if(o&&(!n||n.length>o.length)&&(n=o,r=i,!o.length)||i.solid)break}if(!n)return null;this.sync(r);for(let e=0;e<n.length;e++)t=this.enterInner(n[e],null,t,!1);return t}insertNode(e,t){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let n=this.findPlace(e,t);if(n){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let r=a.none;for(let i of n.concat(e.marks))(t.type?t.type.allowsMarkType(i.type):U(i.type,e.type))&&(r=i.addToSet(r));return t.content.push(e.mark(r)),!0}return!1}enter(e,t,n,r){let i=this.findPlace(e.create(t),n);return i&&(i=this.enterInner(e,t,n,!0,r)),i}enterInner(e,t,n,r=!1,i){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let s=H(e,i,o.options);4&o.options&&0==o.content.length&&(s|=4);let l=a.none;return n=n.filter(t=>(o.type?!o.type.allowsMarkType(t.type):!U(t.type,e))||(l=t.addToSet(l),!1)),this.nodes.push(new W(e,t,l,r,null,s)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=1)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node)&&t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),i=-(n?n.depth+1:0)+(r?0:1),o=(e,s)=>{for(;e>=0;e--){let l=t[e];if(""==l){if(e==t.length-1||0==e)continue;for(;s>=i;s--)if(o(e-1,s))return!0;return!1}{let e=s>0||0==s&&r?this.nodes[s].type:n&&s>=i?n.node(s-i).type:null;if(!e||e.name!=l&&!e.isInGroup(l))return!1;s--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let e in this.parser.schema.nodes){let t=this.parser.schema.nodes[e];if(t.isTextblock&&t.defaultAttrs)return t}}}function _(e){let t={};for(let n in e)t[n]=e[n];return t}function U(e,t){let n=t.schema.nodes;for(let r in n){let i=n[r];if(!i.allowsMarkType(e))continue;let o=[],s=e=>{o.push(e);for(let n=0;n<e.edgeCount;n++){let{type:r,next:i}=e.edge(n);if(r==t||0>o.indexOf(i)&&s(i))return!0}};if(s(i.contentMatch))return!0}}class G{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=Y(t).createDocumentFragment());let r=n,i=[];return e.forEach(e=>{if(i.length||e.marks.length){let n=0,o=0;for(;n<i.length&&o<e.marks.length;){let t=e.marks[o];if(!this.marks[t.type.name]){o++;continue}if(!t.eq(i[n][0])||!1===t.type.spec.spanning)break;n++,o++}for(;n<i.length;)r=i.pop()[1];for(;o<e.marks.length;){let n=e.marks[o++],s=this.serializeMark(n,e.isInline,t);s&&(i.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(e,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=Q(Y(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let i=this.serializeMark(e.marks[r],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&Q(Y(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return Q(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new G(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=X(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return X(e.marks)}}function X(e){let t={};for(let n in e){let r=e[n].spec.toDOM;r&&(t[n]=r)}return t}function Y(e){return e.document||window.document}let Z=new WeakMap;function Q(e,t,n,r){let i,o,s;if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let l=t[0],a;if("string"!=typeof l)throw RangeError("Invalid array passed to renderSpec");if(r&&(void 0===(o=Z.get(r))&&Z.set(r,(s=null,function e(t){if(t&&"object"==typeof t){if(Array.isArray(t)){if("string"==typeof t[0])s||(s=[]),s.push(t);else for(let n=0;n<t.length;n++)e(t[n])}else for(let n in t)e(t[n])}}(r),o=s)),a=o)&&a.indexOf(t)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let h=l.indexOf(" ");h>0&&(n=l.slice(0,h),l=l.slice(h+1));let d=n?e.createElementNS(n,l):e.createElement(l),c=t[1],p=1;if(c&&"object"==typeof c&&null==c.nodeType&&!Array.isArray(c)){for(let e in p=2,c)if(null!=c[e]){let t=e.indexOf(" ");t>0?d.setAttributeNS(e.slice(0,t),e.slice(t+1),c[e]):d.setAttribute(e,c[e])}}for(let o=p;o<t.length;o++){let s=t[o];if(0===s){if(o<t.length-1||o>p)throw RangeError("Content hole must be the only child of its parent node");return{dom:d,contentDOM:d}}{let{dom:t,contentDOM:o}=Q(e,s,n,r);if(d.appendChild(t),o){if(i)throw RangeError("Multiple content holes");i=o}}}return{dom:d,contentDOM:i}}},53786:(e,t,n)=>{n.d(t,{$t:()=>S,LN:()=>s,U3:()=>d,hs:()=>T,i5:()=>f,k_:()=>M,nh:()=>p});var r=n(88434),i=n(8789);let o=Object.create(null);class s{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new l(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=r.Ji.empty){let n=t.content.lastChild,i=null;for(let e=0;e<t.openEnd;e++)i=n,n=n.lastChild;let o=e.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:h}=s[l],d=e.mapping.slice(o);e.replaceRange(d.map(a.pos),d.map(h.pos),l?r.Ji.empty:t),0==l&&y(e,o,(n?n.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],l=e.mapping.slice(n),a=l.map(o.pos),h=l.map(s.pos);i?e.deleteRange(a,h):(e.replaceRangeWith(a,h,t),y(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new d(e):g(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let r=e.depth-1;r>=0;r--){let i=t<0?g(e.node(0),e.node(r),e.before(r+1),e.index(r),t,n):g(e.node(0),e.node(r),e.after(r+1),e.index(r)+1,t,n);if(i)return i}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new f(e.node(0))}static atStart(e){return g(e,e,0,0,1)||new f(e)}static atEnd(e){return g(e,e,e.content.size,e.childCount,-1)||new f(e)}static fromJSON(e,t){if(!t||!t.type)throw RangeError("Invalid input for Selection.fromJSON");let n=o[t.type];if(!n)throw RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in o)throw RangeError("Duplicate use of selection JSON ID "+e);return o[e]=t,t.prototype.jsonID=e,t}getBookmark(){return d.between(this.$anchor,this.$head).getBookmark()}}s.prototype.visible=!0;class l{constructor(e,t){this.$from=e,this.$to=t}}let a=!1;function h(e){a||e.parent.inlineContent||(a=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class d extends s{constructor(e,t=e){h(e),h(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return s.near(n);let r=e.resolve(t.map(this.anchor));return new d(r.parent.inlineContent?r:n,n)}replace(e,t=r.Ji.empty){if(super.replace(e,t),t==r.Ji.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof d&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new c(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new d(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if((!n||r)&&(n=r>=0?1:-1),!t.parent.inlineContent){let e=s.findFrom(t,n,!0)||s.findFrom(t,-n,!0);if(!e)return s.near(t,n);t=e.$head}return e.parent.inlineContent||(0==r?e=t:(e=(s.findFrom(e,-n,!0)||s.findFrom(e,n,!0)).$anchor).pos<t.pos==r<0||(e=t)),new d(e,t)}}s.jsonID("text",d);class c{constructor(e,t){this.anchor=e,this.head=t}map(e){return new c(e.map(this.anchor),e.map(this.head))}resolve(e){return d.between(e.resolve(this.anchor),e.resolve(this.head))}}class p extends s{constructor(e){let t=e.nodeAfter;super(e,e.node(0).resolve(e.pos+t.nodeSize)),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),i=e.resolve(r);return n?s.near(i):new p(i)}content(){return new r.Ji(r.FK.from(this.node),0,0)}eq(e){return e instanceof p&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new u(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new p(e.resolve(t.anchor))}static create(e,t){return new p(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}p.prototype.visible=!1,s.jsonID("node",p);class u{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new c(n,n):new u(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&p.isSelectable(n)?new p(t):s.near(t)}}class f extends s{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=r.Ji.empty){if(t==r.Ji.empty){e.delete(0,e.doc.content.size);let t=s.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new f(e)}map(e){return new f(e)}eq(e){return e instanceof f}getBookmark(){return m}}s.jsonID("all",f);let m={map(){return this},resolve:e=>new f(e)};function g(e,t,n,r,i,o=!1){if(t.inlineContent)return d.create(e,n);for(let s=r-(i>0?0:1);i>0?s<t.childCount:s>=0;s+=i){let r=t.child(s);if(r.isAtom){if(!o&&p.isSelectable(r))return p.create(e,n-(i<0?r.nodeSize:0))}else{let t=g(e,r,n+i,i<0?r.childCount:0,i,o);if(t)return t}n+=r.nodeSize*i}return null}function y(e,t,n){let r,o=e.steps.length-1;if(o<t)return;let l=e.steps[o];(l instanceof i.Ln||l instanceof i.Wg)&&(e.mapping.maps[o].forEach((e,t,n,i)=>{null==r&&(r=i)}),e.setSelection(s.near(e.doc.resolve(r),n)))}class v extends i.dL{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return r.CU.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||r.CU.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let i=this.storedMarks;if(!i){let e=this.doc.resolve(t);i=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,i)),this.selection.empty||this.setSelection(s.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function b(e,t){return t&&e?e.bind(t):e}class w{constructor(e,t,n){this.name=e,this.init=b(t.init,n),this.apply=b(t.apply,n)}}let k=[new w("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new w("selection",{init:(e,t)=>e.selection||s.atStart(t.doc),apply:e=>e.selection}),new w("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,r)=>r.selection.$cursor?e.storedMarks:null}),new w("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class x{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=k.slice(),t&&t.forEach(e=>{if(this.pluginsByKey[e.key])throw RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new w(e.key,e.spec.state,e))})}}class S{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let i=!1;for(let o=0;o<this.config.plugins.length;o++){let s=this.config.plugins[o];if(s.spec.appendTransaction){let l=r?r[o].n:0,a=r?r[o].state:this,h=l<t.length&&s.spec.appendTransaction.call(s,l?t.slice(l):t,a,n);if(h&&n.filterTransaction(h,o)){if(h.setMeta("appendedTransaction",e),!r){r=[];for(let e=0;e<this.config.plugins.length;e++)r.push(e<o?{state:n,n:t.length}:{state:this,n:0})}t.push(h),n=n.applyInner(h),i=!0}r&&(r[o]={state:n,n:t.length})}}if(!i)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let t=new S(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];t[i.name]=i.apply(e,this[i.name],this,t)}return t}get tr(){return new v(this)}static create(e){let t=new x(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new S(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new x(this.schema,e.plugins),n=t.fields,r=new S(t);for(let t=0;t<n.length;t++){let i=n[t].name;r[i]=this.hasOwnProperty(i)?this[i]:n[t].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(e=>e.toJSON())),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],i=r.spec.state;i&&i.toJSON&&(t[n]=i.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw RangeError("Required config field 'schema' missing");let i=new x(e.schema,e.plugins),o=new S(i);return i.fields.forEach(i=>{if("doc"==i.name)o.doc=r.bP.fromJSON(e.schema,t.doc);else if("selection"==i.name)o.selection=s.fromJSON(o.doc,t.selection);else if("storedMarks"==i.name)t.storedMarks&&(o.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let r in n){let s=n[r],l=s.spec.state;if(s.key==i.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(t,r)){o[i.name]=l.fromJSON.call(s,e,t[r],o);return}}o[i.name]=i.init(e,o)}}),o}}class M{constructor(e){this.spec=e,this.props={},e.props&&function e(t,n,r){for(let i in t){let o=t[i];o instanceof Function?o=o.bind(n):"handleDOMEvents"==i&&(o=e(o,n,{})),r[i]=o}return r}(e.props,this,this.props),this.key=e.key?e.key.key:O("plugin")}getState(e){return e[this.key]}}let C=Object.create(null);function O(e){return e in C?e+"$"+ ++C[e]:(C[e]=0,e+"$")}class T{constructor(e="key"){this.key=O(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}},8789:(e,t,n)=>{n.d(t,{$L:()=>E,Ln:()=>m,N0:()=>T,Um:()=>N,Wg:()=>g,X9:()=>s,dL:()=>V,jP:()=>b,n9:()=>C,oM:()=>w,zy:()=>M});var r=n(88434);class i{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class o{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&o.empty)return o.empty}recover(e){let t=0,n=65535&e;if(!this.inverted)for(let e=0;e<n;e++)t+=this.ranges[3*e+2]-this.ranges[3*e+1];return this.ranges[3*n]+t+(e-(65535&e))/65536}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,o=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let h=this.ranges[l+o],d=this.ranges[l+s],c=a+h;if(e<=c){let o=h?e==a?-1:e==c?1:t:t,s=a+r+(o<0?0:d);if(n)return s;let p=e==(t<0?a:c)?null:l/3+(e-a)*65536,u=e==a?2:e==c?1:4;return(t<0?e!=a:e!=c)&&(u|=8),new i(s,u,p)}r+=d-h}return n?e+r:new i(e+r,0,null)}touches(e,t){let n=0,r=65535&t,i=this.inverted?2:1,o=this.inverted?1:2;for(let t=0;t<this.ranges.length;t+=3){let s=this.ranges[t]-(this.inverted?n:0);if(s>e)break;let l=this.ranges[t+i];if(e<=s+l&&t==3*r)return!0;n+=this.ranges[t+o]-l}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let o=this.ranges[r],s=o-(this.inverted?i:0),l=o+(this.inverted?0:i),a=this.ranges[r+t],h=this.ranges[r+n];e(s,s+a,l,l+h),i+=h-a}}invert(){return new o(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?o.empty:new o(e<0?[0,-e,0]:[0,0,e])}}o.empty=new o([]);class s{constructor(e=[],t,n=0,r=e.length){this.maps=e,this.mirror=t,this.from=n,this.to=r}slice(e=0,t=this.maps.length){return new s(this.maps,this.mirror,e,t)}copy(){return new s(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(e,t){this.to=this.maps.push(e),null!=t&&this.setMirror(this.maps.length-1,t)}appendMapping(e){for(let t=0,n=this.maps.length;t<e.maps.length;t++){let r=e.getMirror(t);this.appendMap(e.maps[t],null!=r&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this.maps.length+e.maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e.maps[t].invert(),null!=r&&r>t?n-r-1:void 0)}}invert(){let e=new s;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this.maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let n=this.from;n<this.to;n++){let i=this.maps[n].mapResult(e,t);if(null!=i.recover){let t=this.getMirror(n);if(null!=t&&t>n&&t<this.to){n=t,e=this.maps[t].recover(i.recover);continue}}r|=i.delInfo,e=i.pos}return n?e:new i(e,r,null)}}let l=Object.create(null);class a{getMap(){return o.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=l[t.stepType];if(!n)throw RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in l)throw RangeError("Duplicate use of step JSON ID "+e);return l[e]=t,t.prototype.jsonID=e,t}}class h{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new h(e,null)}static fail(e){return new h(null,e)}static fromReplace(e,t,n,i){try{return h.ok(e.replace(t,n,i))}catch(e){if(e instanceof r.vI)return h.fail(e.message);throw e}}}function d(e,t,n){let i=[];for(let r=0;r<e.childCount;r++){let o=e.child(r);o.content.size&&(o=o.copy(d(o.content,t,o))),o.isInline&&(o=t(o,n,r)),i.push(o)}return r.FK.fromArray(i)}class c extends a{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),i=n.node(n.sharedDepth(this.to)),o=new r.Ji(d(t.content,(e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e,i),t.openStart,t.openEnd);return h.fromReplace(e,this.from,this.to,o)}invert(){return new p(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new c(t.pos,n.pos,this.mark)}merge(e){return e instanceof c&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new c(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new c(t.from,t.to,e.markFromJSON(t.mark))}}a.jsonID("addMark",c);class p extends a{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new r.Ji(d(t.content,e=>e.mark(this.mark.removeFromSet(e.marks)),e),t.openStart,t.openEnd);return h.fromReplace(e,this.from,this.to,n)}invert(){return new c(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new p(t.pos,n.pos,this.mark)}merge(e){return e instanceof p&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new p(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new p(t.from,t.to,e.markFromJSON(t.mark))}}a.jsonID("removeMark",p);class u extends a{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return h.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return h.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new u(this.pos,t.marks[n]);return new u(this.pos,this.mark)}}return new f(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new u(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new u(t.pos,e.markFromJSON(t.mark))}}a.jsonID("addNodeMark",u);class f extends a{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return h.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return h.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new u(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new f(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new f(t.pos,e.markFromJSON(t.mark))}}a.jsonID("removeNodeMark",f);class m extends a{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&y(e,this.from,this.to)?h.fail("Structure replace would overwrite content"):h.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new o([this.from,this.to-this.from,this.slice.size])}invert(e){return new m(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new m(t.pos,Math.max(t.pos,n.pos),this.slice)}merge(e){if(!(e instanceof m)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart){if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;{let t=this.slice.size+e.slice.size==0?r.Ji.empty:new r.Ji(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new m(e.from,this.to,t,this.structure)}}{let t=this.slice.size+e.slice.size==0?r.Ji.empty:new r.Ji(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new m(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new m(t.from,t.to,r.Ji.fromJSON(e,t.slice),!!t.structure)}}a.jsonID("replace",m);class g extends a{constructor(e,t,n,r,i,o,s=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=o,this.structure=s}apply(e){if(this.structure&&(y(e,this.from,this.gapFrom)||y(e,this.gapTo,this.to)))return h.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return h.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?h.fromReplace(e,this.from,this.to,n):h.fail("Content does not fit in gap")}getMap(){return new o([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new g(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||i>n.pos?null:new g(t.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new g(t.from,t.to,t.gapFrom,t.gapTo,r.Ji.fromJSON(e,t.slice),t.insert,!!t.structure)}}function y(e,t,n){let r=e.resolve(t),i=n-t,o=r.depth;for(;i>0&&o>0&&r.indexAfter(o)==r.node(o).childCount;)o--,i--;if(i>0){let e=r.node(o).maybeChild(r.indexAfter(o));for(;i>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,i--}}return!1}function v(e,t,n,i=n.contentMatch,o=!0){let s=e.doc.nodeAt(t),l=[],a=t+1;for(let t=0;t<s.childCount;t++){let h=s.child(t),d=a+h.nodeSize,c=i.matchType(h.type);if(c){i=c;for(let t=0;t<h.marks.length;t++)n.allowsMarkType(h.marks[t].type)||e.step(new p(a,d,h.marks[t]));if(o&&h.isText&&"pre"!=n.whitespace){let e,t=/\r?\n|\r/g,i;for(;e=t.exec(h.text);)i||(i=new r.Ji(r.FK.from(n.schema.text(" ",n.allowedMarks(h.marks))),0,0)),l.push(new m(a+e.index,a+e.index+e[0].length,i))}}else l.push(new m(a,d,r.Ji.empty));a=d}if(!i.validEnd){let t=i.fillBefore(r.FK.empty,!0);e.replace(a,a,new r.Ji(t,0,0))}for(let t=l.length-1;t>=0;t--)e.step(l[t])}function b(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let r=e.$from.node(n),i=e.$from.index(n),o=e.$to.indexAfter(n);if(n<e.depth&&r.canReplace(i,o,t))return n;if(0==n||r.type.spec.isolating||!((0==i||r.canReplace(i,r.childCount))&&(o==r.childCount||r.canReplace(0,o))))break}return null}function w(e,t,n=null,r=e){let i=function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.contentMatchAt(r).findWrapping(t);if(!o)return null;let s=o.length?o[0]:t;return n.canReplaceWith(r,i,s)?o:null}(e,t),o=i&&function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.child(r),s=t.contentMatch.findWrapping(o.type);if(!s)return null;let l=(s.length?s[s.length-1]:t).contentMatch;for(let e=r;l&&e<i;e++)l=l.matchType(n.child(e).type);return l&&l.validEnd?s:null}(r,t);return o?i.map(k).concat({type:t,attrs:n}).concat(o.map(k)):null}function k(e){return{type:e,attrs:null}}function x(e,t,n,r){t.forEach((i,o)=>{if(i.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(i.text);){let i=e.mapping.slice(r).map(n+1+o+s.index);e.replaceWith(i,i+1,t.type.schema.linebreakReplacement.create())}}})}function S(e,t,n,r){t.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let i=e.mapping.slice(r).map(n+1+o);e.replaceWith(i,i+1,t.type.schema.text("\n"))}})}function M(e,t,n=1,r){let i=e.resolve(t),o=i.depth-n,s=r&&r[r.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let e=i.depth-1,t=n-2;e>o;e--,t--){let n=i.node(e),o=i.index(e);if(n.type.spec.isolating)return!1;let s=n.content.cutByIndex(o,n.childCount),l=r&&r[t+1];l&&(s=s.replaceChild(0,l.type.create(l.attrs)));let a=r&&r[t]||n;if(!n.canReplace(o+1,n.childCount)||!a.type.validContent(s))return!1}let l=i.indexAfter(o),a=r&&r[0];return i.node(o).canReplaceWith(l,l,a?a.type:i.node(o+1).type)}function C(e,t){let n=e.resolve(t),r=n.index();return O(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function O(e,t){return!!(e&&t&&!e.isLeaf&&function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:r}=e.type.schema;for(let i=0;i<t.childCount;i++){let o=t.child(i),s=o.type==r?e.type.schema.nodes.text:o.type;if(!(n=n.matchType(s))||!e.type.allowsMarks(o.marks))return!1}return n.validEnd}(e,t))}function T(e,t,n=-1){let r=e.resolve(t);for(let e=r.depth;;e--){let i,o,s=r.index(e);if(e==r.depth?(i=r.nodeBefore,o=r.nodeAfter):n>0?(i=r.node(e+1),s++,o=r.node(e).maybeChild(s)):(i=r.node(e).maybeChild(s-1),o=r.node(e+1)),i&&!i.isTextblock&&O(i,o)&&r.node(e).canReplace(s,s+1))return t;if(0==e)break;t=n<0?r.before(e):r.after(e)}}function N(e,t,n){let r=e.resolve(t);if(!n.content.size)return t;let i=n.content;for(let e=0;e<n.openStart;e++)i=i.firstChild.content;for(let e=1;e<=(0==n.openStart&&n.size?2:1);e++)for(let t=r.depth;t>=0;t--){let n=t==r.depth?0:r.pos<=(r.start(t+1)+r.end(t+1))/2?-1:1,o=r.index(t)+(n>0?1:0),s=r.node(t),l=!1;if(1==e)l=s.canReplace(o,o,i);else{let e=s.contentMatchAt(o).findWrapping(i.firstChild.type);l=e&&s.canReplaceWith(o,o,e[0])}if(l)return 0==n?r.pos:n<0?r.before(t+1):r.after(t+1)}return null}function E(e,t,n=t,i=r.Ji.empty){if(t==n&&!i.size)return null;let o=e.resolve(t),s=e.resolve(n);return A(o,s,i)?new m(t,n,i):new D(o,s,i).fit()}function A(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}a.jsonID("replaceAround",g);class D{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=r.FK.empty;for(let t=0;t<=e.depth;t++){let n=e.node(t);this.frontier.push({type:n.type,match:n.contentMatchAt(e.indexAfter(t))})}for(let t=e.depth;t>0;t--)this.placed=r.FK.from(e.node(t).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,i=this.close(e<0?this.$to:n.doc.resolve(e));if(!i)return null;let o=this.placed,s=n.depth,l=i.depth;for(;s&&l&&1==o.childCount;)o=o.firstChild.content,s--,l--;let a=new r.Ji(o,s,l);return e>-1?new g(n.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||n.pos!=this.$to.pos?new m(n.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let i=t.firstChild;if(t.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){e=n;break}t=i.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e=null,i=(n?(e=P(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let o=this.depth;o>=0;o--){let{type:s,match:l}=this.frontier[o],a,h=null;if(1==t&&(i?l.matchType(i.type)||(h=l.fillBefore(r.FK.from(i),!1)):e&&s.compatibleContent(e.type)))return{sliceDepth:n,frontierDepth:o,parent:e,inject:h};if(2==t&&i&&(a=l.findWrapping(i.type)))return{sliceDepth:n,frontierDepth:o,parent:e,wrap:a};if(e&&l.matchType(e.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=P(e,t);return!!i.childCount&&!i.firstChild.isLeaf&&(this.unplaced=new r.Ji(e,t+1,Math.max(n,i.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,i=P(e,t);if(i.childCount<=1&&t>0){let o=e.size-t<=t+i.size;this.unplaced=new r.Ji(I(e,t-1,1),t-1,o?t-1:n)}else this.unplaced=new r.Ji(I(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:i,wrap:o}){for(;this.depth>t;)this.closeFrontierNode();if(o)for(let e=0;e<o.length;e++)this.openFrontierNode(o[e]);let s=this.unplaced,l=n?n.content:s.content,a=s.openStart-e,h=0,d=[],{match:c,type:p}=this.frontier[t];if(i){for(let e=0;e<i.childCount;e++)d.push(i.child(e));c=c.matchFragment(i)}let u=l.size+e-(s.content.size-s.openEnd);for(;h<l.childCount;){let e=l.child(h),t=c.matchType(e.type);if(!t)break;(++h>1||0==a||e.content.size)&&(c=t,d.push(function e(t,n,i){if(n<=0)return t;let o=t.content;return n>1&&(o=o.replaceChild(0,e(o.firstChild,n-1,1==o.childCount?i-1:0))),n>0&&(o=t.type.contentMatch.fillBefore(o).append(o),i<=0&&(o=o.append(t.type.contentMatch.matchFragment(o).fillBefore(r.FK.empty,!0)))),t.copy(o)}(e.mark(p.allowedMarks(e.marks)),1==h?a:0,h==l.childCount?u:-1)))}let f=h==l.childCount;f||(u=-1),this.placed=R(this.placed,t,r.FK.from(d)),this.frontier[t].match=c,f&&u<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let e=0,t=l;e<u;e++){let e=t.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),t=e.content}this.unplaced=f?0==e?r.Ji.empty:new r.Ji(I(s.content,e-1,1),e-1,u<0?s.openEnd:e-1):new r.Ji(I(s.content,e,h),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!z(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return -1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){n:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],i=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=z(e,t,r,n,i);if(o){for(let n=t-1;n>=0;n--){let{match:t,type:r}=this.frontier[n],i=z(e,n,r,t,!0);if(!i||i.childCount)continue n}return{depth:t,fit:o,move:i?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=R(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),r=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,r)}return e}openFrontierNode(e,t=null,n){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=R(this.placed,this.depth,r.FK.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(r.FK.empty,!0);e.childCount&&(this.placed=R(this.placed,this.frontier.length,e))}}function I(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(I(e.firstChild.content,t-1,n)))}function R(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(R(e.lastChild.content,t-1,n)))}function P(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function z(e,t,n,r,i){let o=e.node(t),s=i?e.indexAfter(t):e.index(t);if(s==o.childCount&&!n.compatibleContent(o.type))return null;let l=r.fillBefore(o.content,!0,s);return l&&!function(e,t,n){for(let r=n;r<t.childCount;r++)if(!e.allowsMarks(t.child(r).marks))return!0;return!1}(n,o.content,s)?l:null}function B(e,t){let n=[],r=Math.min(e.depth,t.depth);for(let i=r;i>=0;i--){let r=e.start(i);if(r<e.pos-(e.depth-i)||t.end(i)>t.pos+(t.depth-i)||e.node(i).type.spec.isolating||t.node(i).type.spec.isolating)break;(r==t.start(i)||i==e.depth&&i==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&i&&t.start(i-1)==r-1)&&n.push(i)}return n}class F extends a{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return h.fail("No node at attribute step's position");let n=Object.create(null);for(let e in t.attrs)n[e]=t.attrs[e];n[this.attr]=this.value;let i=t.type.create(n,null,t.marks);return h.fromReplace(e,this.pos,this.pos+1,new r.Ji(r.FK.from(i),0,t.isLeaf?0:1))}getMap(){return o.empty}invert(e){return new F(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new F(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new F(t.pos,t.attr,t.value)}}a.jsonID("attr",F);class L extends a{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let n in e.attrs)t[n]=e.attrs[n];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return h.ok(n)}getMap(){return o.empty}invert(e){return new L(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new L(t.attr,t.value)}}a.jsonID("docAttr",L);let $=class extends Error{};($=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n}).prototype=Object.create(Error.prototype),$.prototype.constructor=$,$.prototype.name="TransformError";class V{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new s}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new $(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=r.Ji.empty){let i=E(this.doc,e,t,n);return i&&this.step(i),this}replaceWith(e,t,n){return this.replace(e,t,new r.Ji(r.FK.from(n),0,0))}delete(e,t){return this.replace(e,t,r.Ji.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return function(e,t,n,i){if(!i.size)return e.deleteRange(t,n);let o=e.doc.resolve(t),s=e.doc.resolve(n);if(A(o,s,i))return e.step(new m(t,n,i));let l=B(o,e.doc.resolve(n));0==l[l.length-1]&&l.pop();let a=-(o.depth+1);l.unshift(a);for(let e=o.depth,t=o.pos-1;e>0;e--,t--){let n=o.node(e).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;l.indexOf(e)>-1?a=e:o.before(e)==t&&l.splice(1,0,-e)}let h=l.indexOf(a),d=[],c=i.openStart;for(let e=i.content,t=0;;t++){let n=e.firstChild;if(d.push(n),t==i.openStart)break;e=n.content}for(let e=c-1;e>=0;e--){var p;let t=d[e],n=(p=t.type).spec.defining||p.spec.definingForContent;if(n&&!t.sameMarkup(o.node(Math.abs(a)-1)))c=e;else if(n||!t.type.isTextblock)break}for(let t=i.openStart;t>=0;t--){let a=(t+c+1)%(i.openStart+1),p=d[a];if(p)for(let t=0;t<l.length;t++){let d=l[(t+h)%l.length],c=!0;d<0&&(c=!1,d=-d);let u=o.node(d-1),f=o.index(d-1);if(u.canReplaceWith(f,f,p.type,p.marks))return e.replace(o.before(d),c?s.after(d):n,new r.Ji(function e(t,n,i,o,s){if(n<i){let r=t.firstChild;t=t.replaceChild(0,r.copy(e(r.content,n+1,i,o,r)))}if(n>o){let e=s.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(r.FK.empty,!0))}return t}(i.content,0,i.openStart,a),a,i.openEnd))}}let u=e.steps.length;for(let r=l.length-1;r>=0&&(e.replace(t,n,i),!(e.steps.length>u));r--){let e=l[r];e<0||(t=o.before(e),n=s.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){return function(e,t,n,i){if(!i.isInline&&t==n&&e.doc.resolve(t).parent.content.size){let r=function(e,t,n){let r=e.resolve(t);if(r.parent.canReplaceWith(r.index(),r.index(),n))return t;if(0==r.parentOffset)for(let e=r.depth-1;e>=0;e--){let t=r.index(e);if(r.node(e).canReplaceWith(t,t,n))return r.before(e+1);if(t>0)return null}if(r.parentOffset==r.parent.content.size)for(let e=r.depth-1;e>=0;e--){let t=r.indexAfter(e);if(r.node(e).canReplaceWith(t,t,n))return r.after(e+1);if(t<r.node(e).childCount)break}return null}(e.doc,t,i.type);null!=r&&(t=n=r)}e.replaceRange(t,n,new r.Ji(r.FK.from(i),0,0))}(this,e,t,n),this}deleteRange(e,t){return function(e,t,n){let r=e.doc.resolve(t),i=e.doc.resolve(n),o=B(r,i);for(let t=0;t<o.length;t++){let n=o[t],s=t==o.length-1;if(s&&0==n||r.node(n).type.contentMatch.validEnd)return e.delete(r.start(n),i.end(n));if(n>0&&(s||r.node(n-1).canReplace(r.index(n-1),i.indexAfter(n-1))))return e.delete(r.before(n),i.after(n))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(t-r.start(o)==r.depth-o&&n>r.end(o)&&i.end(o)-n!=i.depth-o&&r.start(o-1)==i.start(o-1)&&r.node(o-1).canReplace(r.index(o-1),i.index(o-1)))return e.delete(r.before(o),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return function(e,t,n){let{$from:i,$to:o,depth:s}=t,l=i.before(s+1),a=o.after(s+1),h=l,d=a,c=r.FK.empty,p=0;for(let e=s,t=!1;e>n;e--)t||i.index(e)>0?(t=!0,c=r.FK.from(i.node(e).copy(c)),p++):h--;let u=r.FK.empty,f=0;for(let e=s,t=!1;e>n;e--)t||o.after(e+1)<o.end(e)?(t=!0,u=r.FK.from(o.node(e).copy(u)),f++):d++;e.step(new g(h,d,l,a,new r.Ji(c.append(u),p,f),c.size-p,!0))}(this,e,t),this}join(e,t=1){return function(e,t,n){let i=null,{linebreakReplacement:o}=e.doc.type.schema,s=e.doc.resolve(t-n),l=s.node().type;if(o&&l.inlineContent){let e="pre"==l.whitespace,t=!!l.contentMatch.matchType(o);e&&!t?i=!1:!e&&t&&(i=!0)}let a=e.steps.length;if(!1===i){let r=e.doc.resolve(t+n);S(e,r.node(),r.before(),a)}l.inlineContent&&v(e,t+n-1,l,s.node().contentMatchAt(s.index()),null==i);let h=e.mapping.slice(a),d=h.map(t-n);if(e.step(new m(d,h.map(t+n,-1),r.Ji.empty,!0)),!0===i){let t=e.doc.resolve(d);x(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return function(e,t,n){let i=r.FK.empty;for(let e=n.length-1;e>=0;e--){if(i.size){let t=n[e].type.contentMatch.matchFragment(i);if(!t||!t.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}i=r.FK.from(n[e].type.create(n[e].attrs,i))}let o=t.start,s=t.end;e.step(new g(o,s,o,s,new r.Ji(i,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,i=null){return function(e,t,n,i,o){if(!i.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let s=e.steps.length;e.doc.nodesBetween(t,n,(t,n)=>{var l,a;let h,d,c="function"==typeof o?o(t):o;if(t.isTextblock&&!t.hasMarkup(i,c)&&(l=e.doc,a=e.mapping.slice(s).map(n),d=(h=l.resolve(a)).index(),h.parent.canReplaceWith(d,d+1,i))){let o=null;if(i.schema.linebreakReplacement){let e="pre"==i.whitespace,t=!!i.contentMatch.matchType(i.schema.linebreakReplacement);e&&!t?o=!1:!e&&t&&(o=!0)}!1===o&&S(e,t,n,s),v(e,e.mapping.slice(s).map(n,1),i,void 0,null===o);let l=e.mapping.slice(s),a=l.map(n,1),h=l.map(n+t.nodeSize,1);return e.step(new g(a,h,a+1,h-1,new r.Ji(r.FK.from(i.create(c,null,t.marks)),0,0),1,!0)),!0===o&&x(e,t,n,s),!1}})}(this,e,t,n,i),this}setNodeMarkup(e,t,n=null,i){return function(e,t,n,i,o){let s=e.doc.nodeAt(t);if(!s)throw RangeError("No node at given position");n||(n=s.type);let l=n.create(i,null,o||s.marks);if(s.isLeaf)return e.replaceWith(t,t+s.nodeSize,l);if(!n.validContent(s.content))throw RangeError("Invalid content for node type "+n.name);e.step(new g(t,t+s.nodeSize,t+1,t+s.nodeSize-1,new r.Ji(r.FK.from(l),0,0),1,!0))}(this,e,t,n,i),this}setNodeAttribute(e,t,n){return this.step(new F(e,t,n)),this}setDocAttribute(e,t){return this.step(new L(e,t)),this}addNodeMark(e,t){return this.step(new u(e,t)),this}removeNodeMark(e,t){if(!(t instanceof r.CU)){let n=this.doc.nodeAt(e);if(!n)throw RangeError("No node at position "+e);if(!(t=t.isInSet(n.marks)))return this}return this.step(new f(e,t)),this}split(e,t=1,n){return function(e,t,n=1,i){let o=e.doc.resolve(t),s=r.FK.empty,l=r.FK.empty;for(let e=o.depth,t=o.depth-n,a=n-1;e>t;e--,a--){s=r.FK.from(o.node(e).copy(s));let t=i&&i[a];l=r.FK.from(t?t.type.create(t.attrs,l):o.node(e).copy(l))}e.step(new m(t,t,new r.Ji(s.append(l),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){var r;let i,o,s,l;return r=this,s=[],l=[],r.doc.nodesBetween(e,t,(r,a,h)=>{if(!r.isInline)return;let d=r.marks;if(!n.isInSet(d)&&h.type.allowsMarkType(n.type)){let h=Math.max(a,e),u=Math.min(a+r.nodeSize,t),f=n.addToSet(d);for(let e=0;e<d.length;e++)d[e].isInSet(f)||(i&&i.to==h&&i.mark.eq(d[e])?i.to=u:s.push(i=new p(h,u,d[e])));o&&o.to==h?o.to=u:l.push(o=new c(h,u,n))}}),s.forEach(e=>r.step(e)),l.forEach(e=>r.step(e)),this}removeMark(e,t,n){var i;let o,s;return i=this,o=[],s=0,i.doc.nodesBetween(e,t,(i,l)=>{if(!i.isInline)return;s++;let a=null;if(n instanceof r.sX){let e=i.marks,t;for(;t=n.isInSet(e);)(a||(a=[])).push(t),e=t.removeFromSet(e)}else n?n.isInSet(i.marks)&&(a=[n]):a=i.marks;if(a&&a.length){let n=Math.min(l+i.nodeSize,t);for(let t=0;t<a.length;t++){let r=a[t],i;for(let e=0;e<o.length;e++){let t=o[e];t.step==s-1&&r.eq(o[e].style)&&(i=t)}i?(i.to=n,i.step=s):o.push({style:r,from:Math.max(l,e),to:n,step:s})}}}),o.forEach(e=>i.step(new p(e.from,e.to,e.style))),this}clearIncompatible(e,t,n){return v(this,e,t,n),this}}},45966:(e,t,n)=>{n.d(t,{Lz:()=>tJ,NZ:()=>tm,zF:()=>tv});var r=n(53786),i=n(88434),o=n(8789);let s=function(e){for(var t=0;;t++)if(!(e=e.previousSibling))return t},l=function(e){let t=e.assignedSlot||e.parentNode;return t&&11==t.nodeType?t.host:t},a=null,h=function(e,t,n){let r=a||(a=document.createRange());return r.setEnd(e,null==n?e.nodeValue.length:n),r.setStart(e,t||0),r},d=function(){a=null},c=function(e,t,n,r){return n&&(u(e,t,n,r,-1)||u(e,t,n,r,1))},p=/^(img|br|input|textarea|hr)$/i;function u(e,t,n,r,i){for(;;){if(e==n&&t==r)return!0;if(t==(i<0?0:f(e))){let n=e.parentNode;if(!n||1!=n.nodeType||m(e)||p.test(e.nodeName)||"false"==e.contentEditable)return!1;t=s(e)+(i<0?0:1),e=n}else{if(1!=e.nodeType||"false"==(e=e.childNodes[t+(i<0?-1:0)]).contentEditable)return!1;t=i<0?f(e):0}}}function f(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function m(e){let t;for(let n=e;n&&!(t=n.pmViewDesc);n=n.parentNode);return t&&t.node&&t.node.isBlock&&(t.dom==e||t.contentDOM==e)}let g=function(e){return e.focusNode&&c(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset)};function y(e,t){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=e,n.key=n.code=t,n}let v="undefined"!=typeof navigator?navigator:null,b="undefined"!=typeof document?document:null,w=v&&v.userAgent||"",k=/Edge\/(\d+)/.exec(w),x=/MSIE \d/.exec(w),S=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(w),M=!!(x||S||k),C=x?document.documentMode:S?+S[1]:k?+k[1]:0,O=!M&&/gecko\/(\d+)/i.test(w);O&&(/Firefox\/(\d+)/.exec(w)||[0,0])[1];let T=!M&&/Chrome\/(\d+)/.exec(w),N=!!T,E=T?+T[1]:0,A=!M&&!!v&&/Apple Computer/.test(v.vendor),D=A&&(/Mobile\/\w+/.test(w)||!!v&&v.maxTouchPoints>2),I=D||!!v&&/Mac/.test(v.platform),R=!!v&&/Win/.test(v.platform),P=/Android \d/.test(w),z=!!b&&"webkitFontSmoothing"in b.documentElement.style,B=z?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function F(e,t){return"number"==typeof e?e:e[t]}function L(e,t,n){let r=e.someProp("scrollThreshold")||0,i=e.someProp("scrollMargin")||5,o=e.dom.ownerDocument;for(let s=n||e.dom;s;s=l(s)){if(1!=s.nodeType)continue;let e=s,n=e==o.body,l=n?function(e){let t=e.defaultView&&e.defaultView.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:e.documentElement.clientWidth,top:0,bottom:e.documentElement.clientHeight}}(o):function(e){let t=e.getBoundingClientRect(),n=t.width/e.offsetWidth||1,r=t.height/e.offsetHeight||1;return{left:t.left,right:t.left+e.clientWidth*n,top:t.top,bottom:t.top+e.clientHeight*r}}(e),a=0,h=0;if(t.top<l.top+F(r,"top")?h=-(l.top-t.top+F(i,"top")):t.bottom>l.bottom-F(r,"bottom")&&(h=t.bottom-t.top>l.bottom-l.top?t.top+F(i,"top")-l.top:t.bottom-l.bottom+F(i,"bottom")),t.left<l.left+F(r,"left")?a=-(l.left-t.left+F(i,"left")):t.right>l.right-F(r,"right")&&(a=t.right-l.right+F(i,"right")),a||h){if(n)o.defaultView.scrollBy(a,h);else{let n=e.scrollLeft,r=e.scrollTop;h&&(e.scrollTop+=h),a&&(e.scrollLeft+=a);let i=e.scrollLeft-n,o=e.scrollTop-r;t={left:t.left-i,top:t.top-o,right:t.right-i,bottom:t.bottom-o}}}if(n||/^(fixed|sticky)$/.test(getComputedStyle(s).position))break}}function $(e){let t=[],n=e.ownerDocument;for(let r=e;r&&(t.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),e!=n);r=l(r));return t}function V(e,t){for(let n=0;n<e.length;n++){let{dom:r,top:i,left:o}=e[n];r.scrollTop!=i+t&&(r.scrollTop=i+t),r.scrollLeft!=o&&(r.scrollLeft=o)}}let J=null;function K(e,t){return e.left>=t.left-1&&e.left<=t.right+1&&e.top>=t.top-1&&e.top<=t.bottom+1}function j(e){return e.top<e.bottom||e.left<e.right}function H(e,t){let n=e.getClientRects();if(n.length){let e=n[t<0?0:n.length-1];if(j(e))return e}return Array.prototype.find.call(n,j)||e.getBoundingClientRect()}let W=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function q(e,t,n){let{node:r,offset:i,atom:o}=e.docView.domFromPos(t,n<0?-1:1),s=z||O;if(3==r.nodeType){if(s&&(W.test(r.nodeValue)||(n<0?!i:i==r.nodeValue.length))){let e=H(h(r,i,i),n);if(O&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let t=H(h(r,i-1,i-1),-1);if(t.top==e.top){let n=H(h(r,i,i+1),-1);if(n.top!=e.top)return _(n,n.left<t.left)}}return e}{let e=i,t=i,o=n<0?1:-1;return n<0&&!i?(t++,o=-1):n>=0&&i==r.nodeValue.length?(e--,o=1):n<0?e--:t++,_(H(h(r,e,t),o),o<0)}}if(!e.state.doc.resolve(t-(o||0)).parent.inlineContent){if(null==o&&i&&(n<0||i==f(r))){let e=r.childNodes[i-1];if(1==e.nodeType)return U(e.getBoundingClientRect(),!1)}if(null==o&&i<f(r)){let e=r.childNodes[i];if(1==e.nodeType)return U(e.getBoundingClientRect(),!0)}return U(r.getBoundingClientRect(),n>=0)}if(null==o&&i&&(n<0||i==f(r))){let e=r.childNodes[i-1],t=3==e.nodeType?h(e,f(e)-(s?0:1)):1!=e.nodeType||"BR"==e.nodeName&&e.nextSibling?null:e;if(t)return _(H(t,1),!1)}if(null==o&&i<f(r)){let e=r.childNodes[i];for(;e.pmViewDesc&&e.pmViewDesc.ignoreForCoords;)e=e.nextSibling;let t=e?3==e.nodeType?h(e,0,s?0:1):1==e.nodeType?e:null:null;if(t)return _(H(t,-1),!0)}return _(H(3==r.nodeType?h(r):r,-n),n>=0)}function _(e,t){if(0==e.width)return e;let n=t?e.left:e.right;return{top:e.top,bottom:e.bottom,left:n,right:n}}function U(e,t){if(0==e.height)return e;let n=t?e.top:e.bottom;return{top:n,bottom:n,left:e.left,right:e.right}}function G(e,t,n){let r=e.state,i=e.root.activeElement;r!=t&&e.updateState(t),i!=e.dom&&e.focus();try{return n()}finally{r!=t&&e.updateState(r),i!=e.dom&&i&&i.focus()}}let X=/[\u0590-\u08ac]/,Y=null,Z=null,Q=!1;class ee{constructor(e,t,n,r){this.parent=e,this.children=t,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let r=this.children[t];if(r==e)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){let r;if(this.contentDOM&&this.contentDOM.contains(1==e.nodeType?e:e.parentNode)){if(n<0){let n,r;if(e==this.contentDOM)n=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.previousSibling}for(;n&&!((r=n.pmViewDesc)&&r.parent==this);)n=n.previousSibling;return n?this.posBeforeChild(r)+r.size:this.posAtStart}{let n,r;if(e==this.contentDOM)n=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.nextSibling}for(;n&&!((r=n.pmViewDesc)&&r.parent==this);)n=n.nextSibling;return n?this.posBeforeChild(r):this.posAtEnd}}if(e==this.dom&&this.contentDOM)r=t>s(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==t)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!1;break}if(t.previousSibling)break}if(null==r&&t==e.childNodes.length)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!0;break}if(t.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,r=e;r;r=r.parentNode){let i=this.getDesc(r),o;if(i&&(!t||i.node)){if(!n||!(o=i.nodeDOM)||(1==o.nodeType?o.contains(1==e.nodeType?e:e.parentNode):o==e))return i;n=!1}}}getDesc(e){let t=e.pmViewDesc;for(let e=t;e;e=e.parent)if(e==this)return t}posFromDOM(e,t,n){for(let r=e;r;r=r.parentNode){let i=this.getDesc(r);if(i)return i.localPosFromDOM(e,t,n)}return -1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let r=this.children[t],i=n+r.size;if(n==e&&i!=n){for(;!r.border&&r.children.length;)for(let e=0;e<r.children.length;e++){let t=r.children[e];if(t.size){r=t;break}}return r}if(e<i)return r.descAt(e-n-r.border);n=i}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,r=0;for(let t=0;n<this.children.length;n++){let i=this.children[n],o=t+i.size;if(o>e||i instanceof el){r=e-t;break}t=o}if(r)return this.children[n].domFromPos(r-this.children[n].border,t);for(let e;n&&!(e=this.children[n-1]).size&&e instanceof et&&e.side>=0;n--);if(t<=0){let e,r=!0;for(;(e=n?this.children[n-1]:null)&&e.dom.parentNode!=this.contentDOM;n--,r=!1);return e&&t&&r&&!e.border&&!e.domAtom?e.domFromPos(e.size,t):{node:this.contentDOM,offset:e?s(e.dom)+1:0}}{let e,r=!0;for(;(e=n<this.children.length?this.children[n]:null)&&e.dom.parentNode!=this.contentDOM;n++,r=!1);return e&&r&&!e.border&&!e.domAtom?e.domFromPos(0,t):{node:this.contentDOM,offset:e?s(e.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(0==this.children.length)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,i=-1;for(let o=n,l=0;;l++){let n=this.children[l],a=o+n.size;if(-1==r&&e<=a){let i=o+n.border;if(e>=i&&t<=a-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(e,t,i);e=o;for(let t=l;t>0;t--){let n=this.children[t-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){r=s(n.dom)+1;break}e-=n.size}-1==r&&(r=0)}if(r>-1&&(a>t||l==this.children.length-1)){t=a;for(let e=l+1;e<this.children.length;e++){let n=this.children[e];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){i=s(n.dom);break}t+=n.size}-1==i&&(i=this.contentDOM.childNodes.length);break}o=a}return{node:this.contentDOM,from:e,to:t,fromOffset:r,toOffset:i}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return 0==t.size||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(1!=t.nodeType||n==t.childNodes.length)throw RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,r=!1){let i=Math.min(e,t),o=Math.max(e,t);for(let s=0,l=0;s<this.children.length;s++){let a=this.children[s],h=l+a.size;if(i>l&&o<h)return a.setSelection(e-l-a.border,t-l-a.border,n,r);l=h}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),h=n.root.getSelection(),d=n.domSelectionRange(),p=!1;if((O||A)&&e==t){let{node:e,offset:t}=l;if(3==e.nodeType){if((p=!!(t&&"\n"==e.nodeValue[t-1]))&&t==e.nodeValue.length)for(let t=e,n;t;t=t.parentNode){if(n=t.nextSibling){"BR"==n.nodeName&&(l=a={node:n.parentNode,offset:s(n)+1});break}let e=t.pmViewDesc;if(e&&e.node&&e.node.isBlock)break}}else{let n=e.childNodes[t-1];p=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(O&&d.focusNode&&d.focusNode!=a.node&&1==d.focusNode.nodeType){let e=d.focusNode.childNodes[d.focusOffset];e&&"false"==e.contentEditable&&(r=!0)}if(!(r||p&&A)&&c(l.node,l.offset,d.anchorNode,d.anchorOffset)&&c(a.node,a.offset,d.focusNode,d.focusOffset))return;let u=!1;if((h.extend||e==t)&&!p){h.collapse(l.node,l.offset);try{e!=t&&h.extend(a.node,a.offset),u=!0}catch(e){}}if(!u){if(e>t){let e=l;l=a,a=e}let n=document.createRange();n.setEnd(a.node,a.offset),n.setStart(l.node,l.offset),h.removeAllRanges(),h.addRange(n)}}ignoreMutation(e){return!this.contentDOM&&"selection"!=e.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,r=0;r<this.children.length;r++){let i=this.children[r],o=n+i.size;if(n==o?e<=o&&t>=n:e<o&&t>n){let r=n+i.border,s=o-i.border;if(e>=r&&t<=s){this.dirty=e==n||t==o?2:1,e==r&&t==s&&(i.contentLost||i.dom.parentNode!=this.contentDOM)?i.dirty=3:i.markDirty(e-r,t-r);return}i.dirty=i.dom!=i.contentDOM||i.dom.parentNode!=this.contentDOM||i.children.length?3:2}n=o}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=1==e?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class et extends ee{constructor(e,t,n,r){let i,o=t.type.toDOM;if("function"==typeof o&&(o=o(n,()=>i?i.parent?i.parent.posBeforeChild(i):void 0:r)),!t.type.spec.raw){if(1!=o.nodeType){let e=document.createElement("span");e.appendChild(o),o=e}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,i=this}matchesWidget(e){return 0==this.dirty&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return"selection"!=e.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class en extends ee{constructor(e,t,n,r){super(e,[],t,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return"characterData"===e.type&&e.target.nodeValue==e.oldValue}}class er extends ee{constructor(e,t,n,r,i){super(e,[],n,r),this.mark=t,this.spec=i}static create(e,t,n,r){let o=r.nodeViews[t.type.name],s=o&&o(t,r,n);return s&&s.dom||(s=i.ZF.renderSpec(document,t.type.spec.toDOM(t,n),null,t.attrs)),new er(e,t,s.dom,s.contentDOM||s.dom,s)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return 3!=this.dirty&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),0!=this.dirty){let e=this.parent;for(;!e.node;)e=e.parent;e.dirty<this.dirty&&(e.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let r=er.create(this.parent,this.mark,!0,n),i=this.children,o=this.size;t<o&&(i=ev(i,t,o,n)),e>0&&(i=ev(i,0,e,n));for(let e=0;e<i.length;e++)i[e].parent=r;return r.children=i,r}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class ei extends ee{constructor(e,t,n,r,i,o,s,l,a){super(e,[],i,o),this.node=t,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=s}static create(e,t,n,r,o,s){let l=o.nodeViews[t.type.name],a,h=l&&l(t,o,()=>a?a.parent?a.parent.posBeforeChild(a):void 0:s,n,r),d=h&&h.dom,c=h&&h.contentDOM;if(t.isText){if(d){if(3!=d.nodeType)throw RangeError("Text must be rendered as a DOM text node")}else d=document.createTextNode(t.text)}else if(!d){let e=i.ZF.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs);({dom:d,contentDOM:c}=e)}c||t.isText||"BR"==d.nodeName||(d.hasAttribute("contenteditable")||(d.contentEditable="false"),t.type.spec.draggable&&(d.draggable=!0));let p=d;return(d=eu(d,n,t),h)?a=new ea(e,t,n,r,d,c||null,p,h,o,s+1):t.isText?new es(e,t,n,r,d,p,o):new ei(e,t,n,r,d,c||null,p,o,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(e.preserveWhitespace="full"),this.contentDOM){if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>i.FK.empty)}else e.contentElement=this.contentDOM}else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return 0==this.dirty&&e.eq(this.node)&&ef(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,r=t,o=e.composing?this.localCompositionInfo(e,t):null,s=o&&o.pos>-1?o:null,l=o&&o.pos<0,a=new eg(this,s&&s.node,e);(function(e,t,n,r){let i=t.locals(e),o=0;if(0==i.length){for(let n=0;n<e.childCount;n++){let s=e.child(n);r(s,i,t.forChild(o,s),n),o+=s.nodeSize}return}let s=0,l=[],a=null;for(let h=0;;){let d,c,p,u;for(;s<i.length&&i[s].to==o;){let e=i[s++];e.widget&&(d?(c||(c=[d])).push(e):d=e)}if(d){if(c){c.sort(ey);for(let e=0;e<c.length;e++)n(c[e],h,!!a)}else n(d,h,!!a)}if(a)u=-1,p=a,a=null;else if(h<e.childCount)u=h,p=e.child(h++);else break;for(let e=0;e<l.length;e++)l[e].to<=o&&l.splice(e--,1);for(;s<i.length&&i[s].from<=o&&i[s].to>o;)l.push(i[s++]);let f=o+p.nodeSize;if(p.isText){let e=f;s<i.length&&i[s].from<e&&(e=i[s].from);for(let t=0;t<l.length;t++)l[t].to<e&&(e=l[t].to);e<f&&(a=p.cut(e-o),p=p.cut(0,e-o),f=e,u=-1)}else for(;s<i.length&&i[s].to<f;)s++;let m=p.isInline&&!p.isLeaf?l.filter(e=>!e.inline):l.slice();r(p,m,t.forChild(o,p),u),o=f}})(this.node,this.innerDeco,(t,o,s)=>{t.spec.marks?a.syncToMarks(t.spec.marks,n,e):t.type.side>=0&&!s&&a.syncToMarks(o==this.node.childCount?i.CU.none:this.node.child(o).marks,n,e),a.placeWidget(t,e,r)},(t,i,s,h)=>{let d;a.syncToMarks(t.marks,n,e),a.findNodeMatch(t,i,s,h)||l&&e.state.selection.from>r&&e.state.selection.to<r+t.nodeSize&&(d=a.findIndexWithChild(o.node))>-1&&a.updateNodeAt(t,i,s,d,e)||a.updateNextNode(t,i,s,e,h,r)||a.addNode(t,i,s,e,r),r+=t.nodeSize}),a.syncToMarks([],n,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||2==this.dirty)&&(s&&this.protectLocalComposition(e,s),function e(t,n,r){let i=t.firstChild,o=!1;for(let s=0;s<n.length;s++){let l=n[s],a=l.dom;if(a.parentNode==t){for(;a!=i;)i=em(i),o=!0;i=i.nextSibling}else o=!0,t.insertBefore(a,i);if(l instanceof er){let n=i?i.previousSibling:t.lastChild;e(l.contentDOM,l.children,r),i=n?n.nextSibling:t.firstChild}}for(;i;)i=em(i),o=!0;o&&r.trackWrites==t&&(r.trackWrites=null)}(this.contentDOM,this.children,e),D&&function(e){if("UL"==e.nodeName||"OL"==e.nodeName){let t=e.style.cssText;e.style.cssText=t+"; list-style: square !important",window.getComputedStyle(e).listStyle,e.style.cssText=t}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:i}=e.state.selection;if(!(e.state.selection instanceof r.U3)||n<t||i>t+this.node.content.size)return null;let o=e.input.compositionNode;if(!o||!this.dom.contains(o.parentNode))return null;if(!this.node.inlineContent)return{node:o,pos:-1,text:""};{let e=o.nodeValue,r=function(e,t,n,r){for(let i=0,o=0;i<e.childCount&&o<=r;){let s=e.child(i++),l=o;if(o+=s.nodeSize,!s.isText)continue;let a=s.text;for(;i<e.childCount;){let t=e.child(i++);if(o+=t.nodeSize,!t.isText)break;a+=t.text}if(o>=n){if(o>=r&&a.slice(r-t.length-l,r-l)==t)return r-t.length;let e=l<r?a.lastIndexOf(t,r-l-1):-1;if(e>=0&&e+t.length+l>=n)return l+e;if(n==r&&a.length>=r+t.length-l&&a.slice(r-l,r-l+t.length)==t)return r}}return -1}(this.node.content,e,n-t,i-t);return r<0?null:{node:o,pos:r,text:e}}}protectLocalComposition(e,{node:t,pos:n,text:r}){if(this.getDesc(t))return;let i=t;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let o=new en(this,i,t,r);e.input.compositionNodes.push(o),this.children=ev(this.children,n,n+r.length,e,o)}update(e,t,n,r){return!!(3!=this.dirty&&e.sameMarkup(this.node))&&(this.updateInner(e,t,n,r),!0)}updateInner(e,t,n,r){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(ef(e,this.outerDeco))return;let t=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=ep(this.dom,this.nodeDOM,ec(this.outerDeco,this.node,t),ec(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function eo(e,t,n,r,i){eu(r,t,e);let o=new ei(void 0,e,t,n,r,r,r,i,0);return o.contentDOM&&o.updateChildren(i,0),o}class es extends ei{constructor(e,t,n,r,i,o,s){super(e,t,n,r,i,null,o,s,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,r){return!!(3!=this.dirty&&(0==this.dirty||this.inParent())&&e.sameMarkup(this.node))&&(this.updateOuterDeco(t),(0!=this.dirty||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return"characterData"!=e.type&&"selection"!=e.type}slice(e,t,n){let r=this.node.cut(e,t),i=document.createTextNode(r.text);return new es(this.parent,r,this.outerDeco,this.innerDeco,i,i,n)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(0==e||t==this.nodeDOM.nodeValue.length)&&(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class el extends ee{parseRule(){return{ignore:!0}}matchesHack(e){return 0==this.dirty&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class ea extends ei{constructor(e,t,n,r,i,o,s,l,a,h){super(e,t,n,r,i,o,s,a,h),this.spec=l}update(e,t,n,r){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let i=this.spec.update(e,t,n);return i&&this.updateInner(e,t,n,r),i}return(!!this.contentDOM||!!e.isLeaf)&&super.update(e,t,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,r){this.spec.setSelection?this.spec.setSelection(e,t,n.root):super.setSelection(e,t,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}let eh=function(e){e&&(this.nodeName=e)};eh.prototype=Object.create(null);let ed=[new eh];function ec(e,t,n){if(0==e.length)return ed;let r=n?ed[0]:new eh,i=[r];for(let o=0;o<e.length;o++){let s=e[o].type.attrs;if(s)for(let e in s.nodeName&&i.push(r=new eh(s.nodeName)),s){let o=s[e];null!=o&&(n&&1==i.length&&i.push(r=new eh(t.isInline?"span":"div")),"class"==e?r.class=(r.class?r.class+" ":"")+o:"style"==e?r.style=(r.style?r.style+";":"")+o:"nodeName"!=e&&(r[e]=o))}}return i}function ep(e,t,n,r){if(n==ed&&r==ed)return t;let i=t;for(let t=0;t<r.length;t++){let o=r[t],s=n[t];if(t){let t;s&&s.nodeName==o.nodeName&&i!=e&&(t=i.parentNode)&&t.nodeName.toLowerCase()==o.nodeName||((t=document.createElement(o.nodeName)).pmIsDeco=!0,t.appendChild(i),s=ed[0]),i=t}(function(e,t,n){for(let r in t)"class"==r||"style"==r||"nodeName"==r||r in n||e.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=t[r]&&e.setAttribute(r,n[r]);if(t.class!=n.class){let r=t.class?t.class.split(" ").filter(Boolean):[],i=n.class?n.class.split(" ").filter(Boolean):[];for(let t=0;t<r.length;t++)-1==i.indexOf(r[t])&&e.classList.remove(r[t]);for(let t=0;t<i.length;t++)-1==r.indexOf(i[t])&&e.classList.add(i[t]);0==e.classList.length&&e.removeAttribute("class")}if(t.style!=n.style){if(t.style){let n=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,r;for(;r=n.exec(t.style);)e.style.removeProperty(r[1])}n.style&&(e.style.cssText+=n.style)}})(i,s||ed[0],o)}return i}function eu(e,t,n){return ep(e,e,ed,ec(t,n,1!=e.nodeType))}function ef(e,t){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].type.eq(t[n].type))return!1;return!0}function em(e){let t=e.nextSibling;return e.parentNode.removeChild(e),t}class eg{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(e,t){let n=t,r=n.children.length,i=e.childCount,o=new Map,s=[];r:for(;i>0;){let l;for(;;)if(r){let e=n.children[r-1];if(e instanceof er)n=e,r=e.children.length;else{l=e,r--;break}}else if(n==t)break r;else r=n.parent.children.indexOf(n),n=n.parent;let a=l.node;if(a){if(a!=e.child(i-1))break;--i,o.set(l,i),s.push(l)}}return{index:i,matched:o,matches:s.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let r=0,i=this.stack.length>>1,o=Math.min(i,e.length);for(;r<o&&(r==i-1?this.top:this.stack[r+1<<1]).matchesMark(e[r])&&!1!==e[r].type.spec.spanning;)r++;for(;r<i;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<e.length;){this.stack.push(this.top,this.index+1);let r=-1;for(let t=this.index;t<Math.min(this.index+3,this.top.children.length);t++){let n=this.top.children[t];if(n.matchesMark(e[i])&&!this.isLocked(n.dom)){r=t;break}}if(r>-1)r>this.index&&(this.changed=!0,this.destroyBetween(this.index,r)),this.top=this.top.children[this.index];else{let r=er.create(this.top,e[i],t,n);this.top.children.splice(this.index,0,r),this.top=r,this.changed=!0}this.index=0,i++}}findNodeMatch(e,t,n,r){let i=-1,o;if(r>=this.preMatch.index&&(o=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,n))i=this.top.children.indexOf(o,this.index);else for(let r=this.index,o=Math.min(this.top.children.length,r+5);r<o;r++){let o=this.top.children[r];if(o.matchesNode(e,t,n)&&!this.preMatch.matched.has(o)){i=r;break}}return!(i<0)&&(this.destroyBetween(this.index,i),this.index++,!0)}updateNodeAt(e,t,n,r,i){let o=this.top.children[r];return 3==o.dirty&&o.dom==o.contentDOM&&(o.dirty=2),!!o.update(e,t,n,i)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return -1;if(t==this.top.contentDOM){let t=e.pmViewDesc;if(t){for(let e=this.index;e<this.top.children.length;e++)if(this.top.children[e]==t)return e}return -1}e=t}}updateNextNode(e,t,n,r,i,o){for(let s=this.index;s<this.top.children.length;s++){let l=this.top.children[s];if(l instanceof ei){let a=this.preMatch.matched.get(l);if(null!=a&&a!=i)return!1;let h=l.dom,d,c=this.isLocked(h)&&!(e.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==e.text&&3!=l.dirty&&ef(t,l.outerDeco));if(!c&&l.update(e,t,n,r))return this.destroyBetween(this.index,s),l.dom!=h&&(this.changed=!0),this.index++,!0;if(!c&&(d=this.recreateWrapper(l,e,t,n,r,o)))return this.destroyBetween(this.index,s),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=2,d.updateChildren(r,o+1),d.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,r,i,o){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!ef(n,e.outerDeco)||!r.eq(e.innerDeco))return null;let s=ei.create(this.top,t,n,r,i,o);if(s.contentDOM)for(let t of(s.children=e.children,e.children=[],s.children))t.parent=s;return e.destroy(),s}addNode(e,t,n,r,i){let o=ei.create(this.top,e,t,n,r,i);o.contentDOM&&o.updateChildren(r,i+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(e,t,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(r&&r.matchesWidget(e)&&(e==r.widget||!r.widget.type.toDOM.parentNode))this.index++;else{let r=new et(this.top,e,t,n);this.top.children.splice(this.index++,0,r),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof er;)e=(t=e).children[t.children.length-1];(!e||!(e instanceof es)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((A||N)&&e&&"false"==e.dom.contentEditable&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);"IMG"==e&&(n.className="ProseMirror-separator",n.alt=""),"BR"==e&&(n.className="ProseMirror-trailingBreak");let r=new el(this.top,[],n,null);t!=this.top?t.children.push(r):t.children.splice(this.index++,0,r),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||1==e.nodeType&&e.contains(this.lock.parentNode))}}function ey(e,t){return e.type.side-t.type.side}function ev(e,t,n,r,i){let o=[];for(let s=0,l=0;s<e.length;s++){let a=e[s],h=l,d=l+=a.size;h>=n||d<=t?o.push(a):(h<t&&o.push(a.slice(0,t-h,r)),i&&(o.push(i),i=void 0),d>n&&o.push(a.slice(n-h,a.size,r)))}return o}function eb(e,t=null){let n=e.domSelectionRange(),i=e.state.doc;if(!n.focusNode)return null;let o=e.docView.nearestDesc(n.focusNode),l=o&&0==o.size,a=e.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(a<0)return null;let h=i.resolve(a),d,c;if(g(n)){for(d=a;o&&!o.node;)o=o.parent;let e=o.node;if(o&&e.isAtom&&r.nh.isSelectable(e)&&o.parent&&!(e.isInline&&function(e,t,n){for(let r=0==t,i=t==f(e);r||i;){if(e==n)return!0;let t=s(e);if(!(e=e.parentNode))return!1;r=r&&0==t,i=i&&t==f(e)}}(n.focusNode,n.focusOffset,o.dom))){let e=o.posBefore;c=new r.nh(a==e?h:i.resolve(e))}}else{if(n instanceof e.dom.ownerDocument.defaultView.Selection&&n.rangeCount>1){let t=a,r=a;for(let i=0;i<n.rangeCount;i++){let o=n.getRangeAt(i);t=Math.min(t,e.docView.posFromDOM(o.startContainer,o.startOffset,1)),r=Math.max(r,e.docView.posFromDOM(o.endContainer,o.endOffset,-1))}if(t<0)return null;[d,a]=r==e.state.selection.anchor?[r,t]:[t,r],h=i.resolve(a)}else d=e.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(d<0)return null}let p=i.resolve(d);if(!c){let n="pointer"==t||e.state.selection.head<h.pos&&!l?1:-1;c=eN(e,p,h,n)}return c}function ew(e){return e.editable?e.hasFocus():eA(e)&&document.activeElement&&document.activeElement.contains(e.dom)}function ek(e,t=!1){let n=e.state.selection;if(eO(e,n),ew(e)){if(!t&&e.input.mouseDown&&e.input.mouseDown.allowDefault&&N){let t=e.domSelectionRange(),n=e.domObserver.currentSelection;if(t.anchorNode&&n.anchorNode&&c(t.anchorNode,t.anchorOffset,n.anchorNode,n.anchorOffset)){e.input.mouseDown.delayedSelectionSync=!0,e.domObserver.setCurSelection();return}}if(e.domObserver.disconnectSelection(),e.cursorWrapper)(function(e){let t=e.domSelection(),n=document.createRange();if(!t)return;let r=e.cursorWrapper.dom,i="IMG"==r.nodeName;i?n.setStart(r.parentNode,s(r)+1):n.setStart(r,0),n.collapse(!0),t.removeAllRanges(),t.addRange(n),!i&&!e.state.selection.visible&&M&&C<=11&&(r.disabled=!0,r.disabled=!1)})(e);else{let i,o,s,l,{anchor:a,head:h}=n,d,c;!ex||n instanceof r.U3||(n.$from.parent.inlineContent||(d=eS(e,n.from)),n.empty||n.$from.parent.inlineContent||(c=eS(e,n.to))),e.docView.setSelection(a,h,e,t),ex&&(d&&eC(d),c&&eC(c)),n.visible?e.dom.classList.remove("ProseMirror-hideselection"):(e.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&((i=e.dom.ownerDocument).removeEventListener("selectionchange",e.input.hideSelectionGuard),s=(o=e.domSelectionRange()).anchorNode,l=o.anchorOffset,i.addEventListener("selectionchange",e.input.hideSelectionGuard=()=>{(o.anchorNode!=s||o.anchorOffset!=l)&&(i.removeEventListener("selectionchange",e.input.hideSelectionGuard),setTimeout(()=>{(!ew(e)||e.state.selection.visible)&&e.dom.classList.remove("ProseMirror-hideselection")},20))})))}e.domObserver.setCurSelection(),e.domObserver.connectSelection()}}let ex=A||N&&E<63;function eS(e,t){let{node:n,offset:r}=e.docView.domFromPos(t,0),i=r<n.childNodes.length?n.childNodes[r]:null,o=r?n.childNodes[r-1]:null;if(A&&i&&"false"==i.contentEditable)return eM(i);if((!i||"false"==i.contentEditable)&&(!o||"false"==o.contentEditable)){if(i)return eM(i);if(o)return eM(o)}}function eM(e){return e.contentEditable="true",A&&e.draggable&&(e.draggable=!1,e.wasDraggable=!0),e}function eC(e){e.contentEditable="false",e.wasDraggable&&(e.draggable=!0,e.wasDraggable=null)}function eO(e,t){if(t instanceof r.nh){let n=e.docView.descAt(t.from);n!=e.lastSelectedViewDesc&&(eT(e),n&&n.selectNode(),e.lastSelectedViewDesc=n)}else eT(e)}function eT(e){e.lastSelectedViewDesc&&(e.lastSelectedViewDesc.parent&&e.lastSelectedViewDesc.deselectNode(),e.lastSelectedViewDesc=void 0)}function eN(e,t,n,i){return e.someProp("createSelectionBetween",r=>r(e,t,n))||r.U3.between(t,n,i)}function eE(e){return(!e.editable||!!e.hasFocus())&&eA(e)}function eA(e){let t=e.domSelectionRange();if(!t.anchorNode)return!1;try{return e.dom.contains(3==t.anchorNode.nodeType?t.anchorNode.parentNode:t.anchorNode)&&(e.editable||e.dom.contains(3==t.focusNode.nodeType?t.focusNode.parentNode:t.focusNode))}catch(e){return!1}}function eD(e,t){let{$anchor:n,$head:i}=e.selection,o=t>0?n.max(i):n.min(i),s=o.parent.inlineContent?o.depth?e.doc.resolve(t>0?o.after():o.before()):null:o;return s&&r.LN.findFrom(s,t)}function eI(e,t){return e.dispatch(e.state.tr.setSelection(t).scrollIntoView()),!0}function eR(e,t,n){let i=e.state.selection;if(i instanceof r.U3){if(n.indexOf("s")>-1){let{$head:n}=i,o=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter;if(!o||o.isText||!o.isLeaf)return!1;let s=e.state.doc.resolve(n.pos+o.nodeSize*(t<0?-1:1));return eI(e,new r.U3(i.$anchor,s))}if(!i.empty)return!1;if(e.endOfTextblock(t>0?"forward":"backward")){let n=eD(e.state,t);return!!n&&n instanceof r.nh&&eI(e,n)}if(!(I&&n.indexOf("m")>-1)){let n=i.$head,o=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter,s;if(!o||o.isText)return!1;let l=t<0?n.pos-o.nodeSize:n.pos;return!!(o.isAtom||(s=e.docView.descAt(l))&&!s.contentDOM)&&(r.nh.isSelectable(o)?eI(e,new r.nh(t<0?e.state.doc.resolve(n.pos-o.nodeSize):n)):!!z&&eI(e,new r.U3(e.state.doc.resolve(t<0?l:l+o.nodeSize))))}}else{if(i instanceof r.nh&&i.node.isInline)return eI(e,new r.U3(t>0?i.$to:i.$from));let n=eD(e.state,t);return!!n&&eI(e,n)}}function eP(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function ez(e,t){let n=e.pmViewDesc;return n&&0==n.size&&(t<0||e.nextSibling||"BR"!=e.nodeName)}function eB(e,t){return t<0?function(e){let t=e.domSelectionRange(),n=t.focusNode,r=t.focusOffset;if(!n)return;let i,o,l=!1;for(O&&1==n.nodeType&&r<eP(n)&&ez(n.childNodes[r],-1)&&(l=!0);;)if(r>0){if(1!=n.nodeType)break;{let e=n.childNodes[r-1];if(ez(e,-1))i=n,o=--r;else if(3==e.nodeType)r=(n=e).nodeValue.length;else break}}else if(eF(n))break;else{let t=n.previousSibling;for(;t&&ez(t,-1);)i=n.parentNode,o=s(t),t=t.previousSibling;if(t)r=eP(n=t);else{if((n=n.parentNode)==e.dom)break;r=0}}l?eL(e,n,r):i&&eL(e,i,o)}(e):function(e){let t,n,r=e.domSelectionRange(),i=r.focusNode,o=r.focusOffset;if(!i)return;let l=eP(i);for(;;)if(o<l){if(1!=i.nodeType)break;if(ez(i.childNodes[o],1))t=i,n=++o;else break}else if(eF(i))break;else{let r=i.nextSibling;for(;r&&ez(r,1);)t=r.parentNode,n=s(r)+1,r=r.nextSibling;if(r)o=0,l=eP(i=r);else{if((i=i.parentNode)==e.dom)break;o=l=0}}t&&eL(e,t,n)}(e)}function eF(e){let t=e.pmViewDesc;return t&&t.node&&t.node.isBlock}function eL(e,t,n){if(3!=t.nodeType){let e,r;(r=function(e,t){for(;e&&t==e.childNodes.length&&!m(e);)t=s(e)+1,e=e.parentNode;for(;e&&t<e.childNodes.length;){let n=e.childNodes[t];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=n,t=0}}(t,n))?(t=r,n=0):(e=function(e,t){for(;e&&!t&&!m(e);)t=s(e),e=e.parentNode;for(;e&&t;){let n=e.childNodes[t-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=(e=n).childNodes.length}}(t,n))&&(t=e,n=e.nodeValue.length)}let r=e.domSelection();if(!r)return;if(g(r)){let e=document.createRange();e.setEnd(t,n),e.setStart(t,n),r.removeAllRanges(),r.addRange(e)}else r.extend&&r.extend(t,n);e.domObserver.setCurSelection();let{state:i}=e;setTimeout(()=>{e.state==i&&ek(e)},50)}function e$(e,t){let n=e.state.doc.resolve(t);if(!(N||R)&&n.parent.inlineContent){let r=e.coordsAtPos(t);if(t>n.start()){let n=e.coordsAtPos(t-1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left<r.left?"ltr":"rtl"}if(t<n.end()){let n=e.coordsAtPos(t+1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(e.dom).direction?"rtl":"ltr"}function eV(e,t,n){let i=e.state.selection;if(i instanceof r.U3&&!i.empty||n.indexOf("s")>-1||I&&n.indexOf("m")>-1)return!1;let{$from:o,$to:s}=i;if(!o.parent.inlineContent||e.endOfTextblock(t<0?"up":"down")){let n=eD(e.state,t);if(n&&n instanceof r.nh)return eI(e,n)}if(!o.parent.inlineContent){let n=t<0?o:s,l=i instanceof r.i5?r.LN.near(n,t):r.LN.findFrom(n,t);return!!l&&eI(e,l)}return!1}function eJ(e,t){if(!(e.state.selection instanceof r.U3))return!0;let{$head:n,$anchor:i,empty:o}=e.state.selection;if(!n.sameParent(i))return!0;if(!o)return!1;if(e.endOfTextblock(t>0?"forward":"backward"))return!0;let s=!n.textOffset&&(t<0?n.nodeBefore:n.nodeAfter);if(s&&!s.isText){let r=e.state.tr;return t<0?r.delete(n.pos-s.nodeSize,n.pos):r.delete(n.pos,n.pos+s.nodeSize),e.dispatch(r),!0}return!1}function eK(e,t,n){e.domObserver.stop(),t.contentEditable=n,e.domObserver.start()}function ej(e,t){e.someProp("transformCopied",n=>{t=n(t,e)});let n=[],{content:r,openStart:o,openEnd:s}=t;for(;o>1&&s>1&&1==r.childCount&&1==r.firstChild.childCount;){o--,s--;let e=r.firstChild;n.push(e.type.name,e.attrs!=e.type.defaultAttrs?e.attrs:null),r=e.content}let l=e.someProp("clipboardSerializer")||i.ZF.fromSchema(e.state.schema),a=eY(),h=a.createElement("div");h.appendChild(l.serializeFragment(r,{document:a}));let d=h.firstChild,c,p=0;for(;d&&1==d.nodeType&&(c=eG[d.nodeName.toLowerCase()]);){for(let e=c.length-1;e>=0;e--){let t=a.createElement(c[e]);for(;h.firstChild;)t.appendChild(h.firstChild);h.appendChild(t),p++}d=h.firstChild}return d&&1==d.nodeType&&d.setAttribute("data-pm-slice",`${o} ${s}${p?` -${p}`:""} ${JSON.stringify(n)}`),{dom:h,text:e.someProp("clipboardTextSerializer",n=>n(t,e))||t.content.textBetween(0,t.content.size,"\n\n"),slice:t}}function eH(e,t,n,r,o){let s,l,a=o.parent.type.spec.code;if(!n&&!t)return null;let h=t&&(r||a||!n);if(h){if(e.someProp("transformPastedText",n=>{t=n(t,a||r,e)}),a)return t?new i.Ji(i.FK.from(e.state.schema.text(t.replace(/\r\n?/g,"\n"))),0,0):i.Ji.empty;let n=e.someProp("clipboardTextParser",n=>n(t,o,r,e));if(n)l=n;else{let n=o.marks(),{schema:r}=e.state,l=i.ZF.fromSchema(r);s=document.createElement("div"),t.split(/(?:\r\n?|\n)+/).forEach(e=>{let t=s.appendChild(document.createElement("p"));e&&t.appendChild(l.serializeNode(r.text(e,n)))})}}else e.someProp("transformPastedHTML",t=>{n=t(n,e)}),s=function(e){var t;let n,r=/^(\s*<meta [^>]*>)*/.exec(e);r&&(e=e.slice(r[0].length));let i=eY().createElement("div"),o=/<([a-z][^>\s]+)/i.exec(e),s;if((s=o&&eG[o[1].toLowerCase()])&&(e=s.map(e=>"<"+e+">").join("")+e+s.map(e=>"</"+e+">").reverse().join("")),i.innerHTML=(t=e,(n=window.trustedTypes)?(eZ||(eZ=n.createPolicy("ProseMirrorClipboard",{createHTML:e=>e})),eZ.createHTML(t)):t),s)for(let e=0;e<s.length;e++)i=i.querySelector(s[e])||i;return i}(n),z&&function(e){let t=e.querySelectorAll(N?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<t.length;n++){let r=t[n];1==r.childNodes.length&&"\xa0"==r.textContent&&r.parentNode&&r.parentNode.replaceChild(e.ownerDocument.createTextNode(" "),r)}}(s);let d=s&&s.querySelector("[data-pm-slice]"),c=d&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(d.getAttribute("data-pm-slice")||"");if(c&&c[3])for(let e=+c[3];e>0;e--){let e=s.firstChild;for(;e&&1!=e.nodeType;)e=e.nextSibling;if(!e)break;s=e}if(l||(l=(e.someProp("clipboardParser")||e.someProp("domParser")||i.S4.fromSchema(e.state.schema)).parseSlice(s,{preserveWhitespace:!!(h||c),context:o,ruleFromNode:e=>"BR"!=e.nodeName||e.nextSibling||!e.parentNode||eW.test(e.parentNode.nodeName)?null:{ignore:!0}})),c)l=function(e,t){if(!e.size)return e;let n=e.content.firstChild.type.schema,r;try{r=JSON.parse(t)}catch(t){return e}let{content:o,openStart:s,openEnd:l}=e;for(let e=r.length-2;e>=0;e-=2){let t=n.nodes[r[e]];if(!t||t.hasRequiredAttrs())break;o=i.FK.from(t.create(r[e+1],o)),s++,l++}return new i.Ji(o,s,l)}(eU(l,+c[1],+c[2]),c[4]);else if((l=i.Ji.maxOpen(function(e,t){if(e.childCount<2)return e;for(let n=t.depth;n>=0;n--){let r=t.node(n).contentMatchAt(t.index(n)),o,s=[];if(e.forEach(e=>{if(!s)return;let t=r.findWrapping(e.type),n;if(!t)return s=null;if(n=s.length&&o.length&&function e(t,n,r,o,s){if(s<t.length&&s<n.length&&t[s]==n[s]){let l=e(t,n,r,o.lastChild,s+1);if(l)return o.copy(o.content.replaceChild(o.childCount-1,l));if(o.contentMatchAt(o.childCount).matchType(s==t.length-1?r.type:t[s+1]))return o.copy(o.content.append(i.FK.from(eq(r,t,s+1))))}}(t,o,e,s[s.length-1],0))s[s.length-1]=n;else{s.length&&(s[s.length-1]=function e(t,n){if(0==n)return t;let r=t.content.replaceChild(t.childCount-1,e(t.lastChild,n-1)),o=t.contentMatchAt(t.childCount).fillBefore(i.FK.empty,!0);return t.copy(r.append(o))}(s[s.length-1],o.length));let n=eq(e,t);s.push(n),r=r.matchType(n.type),o=t}}),s)return i.FK.from(s)}return e}(l.content,o),!0)).openStart||l.openEnd){let e=0,t=0;for(let t=l.content.firstChild;e<l.openStart&&!t.type.spec.isolating;e++,t=t.firstChild);for(let e=l.content.lastChild;t<l.openEnd&&!e.type.spec.isolating;t++,e=e.lastChild);l=eU(l,e,t)}return e.someProp("transformPasted",t=>{l=t(l,e)}),l}let eW=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function eq(e,t,n=0){for(let r=t.length-1;r>=n;r--)e=t[r].create(null,i.FK.from(e));return e}function e_(e,t,n,r,o,s){let l=t<0?e.firstChild:e.lastChild,a=l.content;return e.childCount>1&&(s=0),o<r-1&&(a=e_(a,t,n,r,o+1,s)),o>=n&&(a=t<0?l.contentMatchAt(0).fillBefore(a,s<=o).append(a):a.append(l.contentMatchAt(l.childCount).fillBefore(i.FK.empty,!0))),e.replaceChild(t<0?0:e.childCount-1,l.copy(a))}function eU(e,t,n){return t<e.openStart&&(e=new i.Ji(e_(e.content,-1,t,e.openStart,0,e.openEnd),t,e.openEnd)),n<e.openEnd&&(e=new i.Ji(e_(e.content,1,n,e.openEnd,0,0),e.openStart,n)),e}let eG={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},eX=null;function eY(){return eX||(eX=document.implementation.createHTMLDocument("title"))}let eZ=null,eQ={},e0={},e1={touchstart:!0,touchmove:!0};class e2{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function e3(e,t){e.input.lastSelectionOrigin=t,e.input.lastSelectionTime=Date.now()}function e5(e){e.someProp("handleDOMEvents",t=>{for(let n in t)e.input.eventHandlers[n]||e.dom.addEventListener(n,e.input.eventHandlers[n]=t=>e8(e,t))})}function e8(e,t){return e.someProp("handleDOMEvents",n=>{let r=n[t.type];return!!r&&(r(e,t)||t.defaultPrevented)})}function e4(e){return{left:e.clientX,top:e.clientY}}function e6(e,t,n,r,i){if(-1==r)return!1;let o=e.state.doc.resolve(r);for(let r=o.depth+1;r>0;r--)if(e.someProp(t,t=>r>o.depth?t(e,n,o.nodeAfter,o.before(r),i,!0):t(e,n,o.node(r),o.before(r),i,!1)))return!0;return!1}function e9(e,t,n){if(e.focused||e.focus(),e.state.selection.eq(t))return;let r=e.state.tr.setSelection(t);"pointer"==n&&r.setMeta("pointer",!0),e.dispatch(r)}e0.keydown=(e,t)=>{if(e.input.shiftKey=16==t.keyCode||t.shiftKey,!tt(e,t)&&(e.input.lastKeyCode=t.keyCode,e.input.lastKeyCodeTime=Date.now(),!P||!N||13!=t.keyCode)){if(229!=t.keyCode&&e.domObserver.forceFlush(),!D||13!=t.keyCode||t.ctrlKey||t.altKey||t.metaKey)e.someProp("handleKeyDown",n=>n(e,t))||function(e,t){let n;let r=t.keyCode,i=(n="",t.ctrlKey&&(n+="c"),t.metaKey&&(n+="m"),t.altKey&&(n+="a"),t.shiftKey&&(n+="s"),n);if(8==r||I&&72==r&&"c"==i)return eJ(e,-1)||eB(e,-1);if(46==r&&!t.shiftKey||I&&68==r&&"c"==i)return eJ(e,1)||eB(e,1);if(13==r||27==r)return!0;if(37==r||I&&66==r&&"c"==i){let t=37==r?"ltr"==e$(e,e.state.selection.from)?-1:1:-1;return eR(e,t,i)||eB(e,t)}if(39==r||I&&70==r&&"c"==i){let t=39==r?"ltr"==e$(e,e.state.selection.from)?1:-1:1;return eR(e,t,i)||eB(e,t)}if(38==r||I&&80==r&&"c"==i)return eV(e,-1,i)||eB(e,-1);if(40==r||I&&78==r&&"c"==i)return function(e){if(!A||e.state.selection.$head.parentOffset>0)return!1;let{focusNode:t,focusOffset:n}=e.domSelectionRange();if(t&&1==t.nodeType&&0==n&&t.firstChild&&"false"==t.firstChild.contentEditable){let n=t.firstChild;eK(e,n,"true"),setTimeout(()=>eK(e,n,"false"),20)}return!1}(e)||eV(e,1,i)||eB(e,1);else if(i==(I?"m":"c")&&(66==r||73==r||89==r||90==r))return!0;return!1}(e,t)?t.preventDefault():e3(e,"key");else{let t=Date.now();e.input.lastIOSEnter=t,e.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{e.input.lastIOSEnter==t&&(e.someProp("handleKeyDown",t=>t(e,y(13,"Enter"))),e.input.lastIOSEnter=0)},200)}}},e0.keyup=(e,t)=>{16==t.keyCode&&(e.input.shiftKey=!1)},e0.keypress=(e,t)=>{if(tt(e,t)||!t.charCode||t.ctrlKey&&!t.altKey||I&&t.metaKey)return;if(e.someProp("handleKeyPress",n=>n(e,t))){t.preventDefault();return}let n=e.state.selection;if(!(n instanceof r.U3)||!n.$from.sameParent(n.$to)){let r=String.fromCharCode(t.charCode);/[\r\n]/.test(r)||e.someProp("handleTextInput",t=>t(e,n.$from.pos,n.$to.pos,r))||e.dispatch(e.state.tr.insertText(r).scrollIntoView()),t.preventDefault()}};let e7=I?"metaKey":"ctrlKey";eQ.mousedown=(e,t)=>{e.input.shiftKey=t.shiftKey;let n=to(e),i=Date.now(),o="singleClick";i-e.input.lastClick.time<500&&function(e,t){let n=t.x-e.clientX,r=t.y-e.clientY;return n*n+r*r<100}(t,e.input.lastClick)&&!t[e7]&&("singleClick"==e.input.lastClick.type?o="doubleClick":"doubleClick"==e.input.lastClick.type&&(o="tripleClick")),e.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:o};let s=e.posAtCoords(e4(t));s&&("singleClick"==o?(e.input.mouseDown&&e.input.mouseDown.done(),e.input.mouseDown=new te(e,s,t,!!n)):("doubleClick"==o?function(e,t,n,r){return e6(e,"handleDoubleClickOn",t,n,r)||e.someProp("handleDoubleClick",n=>n(e,t,r))}:function(e,t,n,i){return e6(e,"handleTripleClickOn",t,n,i)||e.someProp("handleTripleClick",n=>n(e,t,i))||function(e,t,n){if(0!=n.button)return!1;let i=e.state.doc;if(-1==t)return!!i.inlineContent&&(e9(e,r.U3.create(i,0,i.content.size),"pointer"),!0);let o=i.resolve(t);for(let t=o.depth+1;t>0;t--){let n=t>o.depth?o.nodeAfter:o.node(t),s=o.before(t);if(n.inlineContent)e9(e,r.U3.create(i,s+1,s+1+n.content.size),"pointer");else{if(!r.nh.isSelectable(n))continue;e9(e,r.nh.create(i,s),"pointer")}return!0}}(e,n,i)})(e,s.pos,s.inside,t)?t.preventDefault():e3(e,"pointer"))};class te{constructor(e,t,n,i){let o,s;if(this.view=e,this.pos=t,this.event=n,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[e7],this.allowDefault=n.shiftKey,t.inside>-1)o=e.state.doc.nodeAt(t.inside),s=t.inside;else{let n=e.state.doc.resolve(t.pos);o=n.parent,s=n.depth?n.before():0}let l=i?null:n.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&1==a.dom.nodeType?a.dom:null;let{selection:h}=e.state;(0==n.button&&o.type.spec.draggable&&!1!==o.type.spec.selectable||h instanceof r.nh&&h.from<=s&&h.to>s)&&(this.mightDrag={node:o,pos:s,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&O&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),e3(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>ek(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;if(this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(e4(e))),this.updateAllowDefault(e),this.allowDefault||!t)e3(this.view,"pointer");else{var n,i,o,s;(n=this.view,i=t.pos,o=t.inside,s=this.selectNode,e6(n,"handleClickOn",i,o,e)||n.someProp("handleClick",t=>t(n,i,e))||(s?function(e,t){if(-1==t)return!1;let n=e.state.selection,i,o;n instanceof r.nh&&(i=n.node);let s=e.state.doc.resolve(t);for(let e=s.depth+1;e>0;e--){let t=e>s.depth?s.nodeAfter:s.node(e);if(r.nh.isSelectable(t)){o=i&&n.$from.depth>0&&e>=n.$from.depth&&s.before(n.$from.depth+1)==n.$from.pos?s.before(n.$from.depth):s.before(e);break}}return null!=o&&(e9(e,r.nh.create(e.state.doc,o),"pointer"),!0)}(n,o):function(e,t){if(-1==t)return!1;let n=e.state.doc.resolve(t),i=n.nodeAfter;return!!(i&&i.isAtom&&r.nh.isSelectable(i))&&(e9(e,new r.nh(n),"pointer"),!0)}(n,o)))?e.preventDefault():0==e.button&&(this.flushed||A&&this.mightDrag&&!this.mightDrag.node.isAtom||N&&!this.view.state.selection.visible&&2>=Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to)))?(e9(this.view,r.LN.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):e3(this.view,"pointer")}}move(e){this.updateAllowDefault(e),e3(this.view,"pointer"),0==e.buttons&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function tt(e,t){return!!e.composing||!!(A&&500>Math.abs(t.timeStamp-e.input.compositionEndedAt))&&(e.input.compositionEndedAt=-2e8,!0)}eQ.touchstart=e=>{e.input.lastTouch=Date.now(),to(e),e3(e,"pointer")},eQ.touchmove=e=>{e.input.lastTouch=Date.now(),e3(e,"pointer")},eQ.contextmenu=e=>to(e);let tn=P?5e3:-1;function tr(e,t){clearTimeout(e.input.composingTimeout),t>-1&&(e.input.composingTimeout=setTimeout(()=>to(e),t))}function ti(e){let t;for(e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=((t=document.createEvent("Event")).initEvent("event",!0,!0),t.timeStamp));e.input.compositionNodes.length>0;)e.input.compositionNodes.pop().markParentsDirty()}function to(e,t=!1){if(!P||!(e.domObserver.flushingSoon>=0)){if(e.domObserver.forceFlush(),ti(e),t||e.docView&&e.docView.dirty){let n=eb(e);return n&&!n.eq(e.state.selection)?e.dispatch(e.state.tr.setSelection(n)):(e.markCursor||t)&&!e.state.selection.empty?e.dispatch(e.state.tr.deleteSelection()):e.updateState(e.state),!0}return!1}}e0.compositionstart=e0.compositionupdate=e=>{if(!e.composing){e.domObserver.flush();let{state:t}=e,n=t.selection.$to;if(t.selection instanceof r.U3&&(t.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some(e=>!1===e.type.spec.inclusive)))e.markCursor=e.state.storedMarks||n.marks(),to(e,!0),e.markCursor=null;else if(to(e,!t.selection.empty),O&&t.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let t=e.domSelectionRange();for(let n=t.focusNode,r=t.focusOffset;n&&1==n.nodeType&&0!=r;){let t=r<0?n.lastChild:n.childNodes[r-1];if(!t)break;if(3==t.nodeType){let n=e.domSelection();n&&n.collapse(t,t.nodeValue.length);break}n=t,r=-1}}e.input.composing=!0}tr(e,tn)},e0.compositionend=(e,t)=>{e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=t.timeStamp,e.input.compositionPendingChanges=e.domObserver.pendingRecords().length?e.input.compositionID:0,e.input.compositionNode=null,e.input.compositionPendingChanges&&Promise.resolve().then(()=>e.domObserver.flush()),e.input.compositionID++,tr(e,20))};let ts=M&&C<15||D&&B<604;function tl(e,t,n,r,o){let s=eH(e,t,n,r,e.state.selection.$from);if(e.someProp("handlePaste",t=>t(e,o,s||i.Ji.empty)))return!0;if(!s)return!1;let l=0==s.openStart&&0==s.openEnd&&1==s.content.childCount?s.content.firstChild:null,a=l?e.state.tr.replaceSelectionWith(l,r):e.state.tr.replaceSelection(s);return e.dispatch(a.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function ta(e){let t=e.getData("text/plain")||e.getData("Text");if(t)return t;let n=e.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}eQ.copy=e0.cut=(e,t)=>{let n=e.state.selection,r="cut"==t.type;if(n.empty)return;let i=ts?null:t.clipboardData,{dom:o,text:s}=ej(e,n.content());i?(t.preventDefault(),i.clearData(),i.setData("text/html",o.innerHTML),i.setData("text/plain",s)):function(e,t){if(!e.dom.parentNode)return;let n=e.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(t),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(t),e.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n),e.focus()},50)}(e,o),r&&e.dispatch(e.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},e0.paste=(e,t)=>{if(e.composing&&!P)return;let n=ts?null:t.clipboardData,r=e.input.shiftKey&&45!=e.input.lastKeyCode;n&&tl(e,ta(n),n.getData("text/html"),r,t)?t.preventDefault():function(e,t){if(!e.dom.parentNode)return;let n=e.input.shiftKey||e.state.selection.$from.parent.type.spec.code,r=e.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=e.input.shiftKey&&45!=e.input.lastKeyCode;setTimeout(()=>{e.focus(),r.parentNode&&r.parentNode.removeChild(r),n?tl(e,r.value,null,i,t):tl(e,r.textContent,r.innerHTML,i,t)},50)}(e,t)};class th{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}let td=I?"altKey":"ctrlKey";for(let e in eQ.dragstart=(e,t)=>{let n;let i=e.input.mouseDown;if(i&&i.done(),!t.dataTransfer)return;let o=e.state.selection,s=o.empty?null:e.posAtCoords(e4(t));if(s&&s.pos>=o.from&&s.pos<=(o instanceof r.nh?o.to-1:o.to));else if(i&&i.mightDrag)n=r.nh.create(e.state.doc,i.mightDrag.pos);else if(t.target&&1==t.target.nodeType){let i=e.docView.nearestDesc(t.target,!0);i&&i.node.type.spec.draggable&&i!=e.docView&&(n=r.nh.create(e.state.doc,i.posBefore))}let l=(n||e.state.selection).content(),{dom:a,text:h,slice:d}=ej(e,l);t.dataTransfer.files.length&&N&&!(E>120)||t.dataTransfer.clearData(),t.dataTransfer.setData(ts?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",ts||t.dataTransfer.setData("text/plain",h),e.dragging=new th(d,!t[td],n)},eQ.dragend=e=>{let t=e.dragging;window.setTimeout(()=>{e.dragging==t&&(e.dragging=null)},50)},e0.dragover=e0.dragenter=(e,t)=>t.preventDefault(),e0.drop=(e,t)=>{let n=e.dragging;if(e.dragging=null,!t.dataTransfer)return;let s=e.posAtCoords(e4(t));if(!s)return;let l=e.state.doc.resolve(s.pos),a=n&&n.slice;a?e.someProp("transformPasted",t=>{a=t(a,e)}):a=eH(e,ta(t.dataTransfer),ts?null:t.dataTransfer.getData("text/html"),!1,l);let h=!!(n&&!t[td]);if(e.someProp("handleDrop",n=>n(e,t,a||i.Ji.empty,h))){t.preventDefault();return}if(!a)return;t.preventDefault();let d=a?(0,o.Um)(e.state.doc,l.pos,a):l.pos;null==d&&(d=l.pos);let c=e.state.tr;if(h){let{node:e}=n;e?e.replace(c):c.deleteSelection()}let p=c.mapping.map(d),u=0==a.openStart&&0==a.openEnd&&1==a.content.childCount,f=c.doc;if(u?c.replaceRangeWith(p,p,a.content.firstChild):c.replaceRange(p,p,a),c.doc.eq(f))return;let m=c.doc.resolve(p);if(u&&r.nh.isSelectable(a.content.firstChild)&&m.nodeAfter&&m.nodeAfter.sameMarkup(a.content.firstChild))c.setSelection(new r.nh(m));else{let t=c.mapping.map(d);c.mapping.maps[c.mapping.maps.length-1].forEach((e,n,r,i)=>t=i),c.setSelection(eN(e,m,c.doc.resolve(t)))}e.focus(),e.dispatch(c.setMeta("uiEvent","drop"))},eQ.focus=e=>{e.input.lastFocus=Date.now(),e.focused||(e.domObserver.stop(),e.dom.classList.add("ProseMirror-focused"),e.domObserver.start(),e.focused=!0,setTimeout(()=>{e.docView&&e.hasFocus()&&!e.domObserver.currentSelection.eq(e.domSelectionRange())&&ek(e)},20))},eQ.blur=(e,t)=>{e.focused&&(e.domObserver.stop(),e.dom.classList.remove("ProseMirror-focused"),e.domObserver.start(),t.relatedTarget&&e.dom.contains(t.relatedTarget)&&e.domObserver.currentSelection.clear(),e.focused=!1)},eQ.beforeinput=(e,t)=>{if(N&&P&&"deleteContentBackward"==t.inputType){e.domObserver.flushSoon();let{domChangeCount:t}=e.input;setTimeout(()=>{if(e.input.domChangeCount!=t||(e.dom.blur(),e.focus(),e.someProp("handleKeyDown",t=>t(e,y(8,"Backspace")))))return;let{$cursor:n}=e.state.selection;n&&n.pos>0&&e.dispatch(e.state.tr.delete(n.pos-1,n.pos).scrollIntoView())},50)}},e0)eQ[e]=e0[e];function tc(e,t){if(e==t)return!0;for(let n in e)if(e[n]!==t[n])return!1;for(let n in t)if(!(n in e))return!1;return!0}class tp{constructor(e,t){this.toDOM=e,this.spec=t||ty,this.side=this.spec.side||0}map(e,t,n,r){let{pos:i,deleted:o}=e.mapResult(t.from+r,this.side<0?-1:1);return o?null:new tm(i-n,i-n,this)}valid(){return!0}eq(e){return this==e||e instanceof tp&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&tc(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class tu{constructor(e,t){this.attrs=e,this.spec=t||ty}map(e,t,n,r){let i=e.map(t.from+r,this.spec.inclusiveStart?-1:1)-n,o=e.map(t.to+r,this.spec.inclusiveEnd?1:-1)-n;return i>=o?null:new tm(i,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof tu&&tc(this.attrs,e.attrs)&&tc(this.spec,e.spec)}static is(e){return e.type instanceof tu}destroy(){}}class tf{constructor(e,t){this.attrs=e,this.spec=t||ty}map(e,t,n,r){let i=e.mapResult(t.from+r,1);if(i.deleted)return null;let o=e.mapResult(t.to+r,-1);return o.deleted||o.pos<=i.pos?null:new tm(i.pos-n,o.pos-n,this)}valid(e,t){let{index:n,offset:r}=e.content.findIndex(t.from),i;return r==t.from&&!(i=e.child(n)).isText&&r+i.nodeSize==t.to}eq(e){return this==e||e instanceof tf&&tc(this.attrs,e.attrs)&&tc(this.spec,e.spec)}destroy(){}}class tm{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new tm(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new tm(e,e,new tp(t,n))}static inline(e,t,n,r){return new tm(e,t,new tu(n,r))}static node(e,t,n,r){return new tm(e,t,new tf(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof tu}get widget(){return this.type instanceof tp}}let tg=[],ty={};class tv{constructor(e,t){this.local=e.length?e:tg,this.children=t.length?t:tg}static create(e,t){return t.length?tM(t,e,0,ty):tb}find(e,t,n){let r=[];return this.findInner(null==e?0:e,null==t?1e9:t,r,0,n),r}findInner(e,t,n,r,i){for(let o=0;o<this.local.length;o++){let s=this.local[o];s.from<=t&&s.to>=e&&(!i||i(s.spec))&&n.push(s.copy(s.from+r,s.to+r))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let s=this.children[o]+1;this.children[o+2].findInner(e-s,t-s,n,r+s,i)}}map(e,t,n){return this==tb||0==e.maps.length?this:this.mapInner(e,t,0,0,n||ty)}mapInner(e,t,n,r,i){let o;for(let s=0;s<this.local.length;s++){let l=this.local[s].map(e,n,r);l&&l.type.valid(t,l)?(o||(o=[])).push(l):i.onRemove&&i.onRemove(this.local[s].spec)}return this.children.length?function(e,t,n,r,i,o,s){let l=e.slice();for(let e=0,t=o;e<n.maps.length;e++){let r=0;n.maps[e].forEach((e,n,i,o)=>{let s=o-i-(n-e);for(let i=0;i<l.length;i+=3){let o=l[i+1];if(o<0||e>o+t-r)continue;let a=l[i]+t-r;n>=a?l[i+1]=e<=a?-2:-1:e>=t&&s&&(l[i]+=s,l[i+1]+=s)}r+=s}),t=n.maps[e].map(t,-1)}let a=!1;for(let t=0;t<l.length;t+=3)if(l[t+1]<0){if(-2==l[t+1]){a=!0,l[t+1]=-1;continue}let h=n.map(e[t]+o),d=h-i;if(d<0||d>=r.content.size){a=!0;continue}let c=n.map(e[t+1]+o,-1)-i,{index:p,offset:u}=r.content.findIndex(d),f=r.maybeChild(p);if(f&&u==d&&u+f.nodeSize==c){let r=l[t+2].mapInner(n,f,h+1,e[t]+o+1,s);r!=tb?(l[t]=d,l[t+1]=c,l[t+2]=r):(l[t+1]=-2,a=!0)}else a=!0}if(a){let a=tM(function(e,t,n,r,i,o,s){for(let l=0;l<e.length;l+=3)-1==e[l+1]&&function e(t,o){for(let e=0;e<t.local.length;e++){let l=t.local[e].map(r,i,o);l?n.push(l):s.onRemove&&s.onRemove(t.local[e].spec)}for(let n=0;n<t.children.length;n+=3)e(t.children[n+2],t.children[n]+o+1)}(e[l+2],t[l]+o+1);return n}(l,e,t,n,i,o,s),r,0,s);t=a.local;for(let e=0;e<l.length;e+=3)l[e+1]<0&&(l.splice(e,3),e-=3);for(let e=0,t=0;e<a.children.length;e+=3){let n=a.children[e];for(;t<l.length&&l[t]<n;)t+=3;l.splice(t,0,a.children[e],a.children[e+1],a.children[e+2])}}return new tv(t.sort(tC),l)}(this.children,o||[],e,t,n,r,i):o?new tv(o.sort(tC),tg):tb}add(e,t){return t.length?this==tb?tv.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let r,i=0;e.forEach((e,o)=>{let s=o+n,l;if(l=tx(t,e,s)){for(r||(r=this.children.slice());i<r.length&&r[i]<o;)i+=3;r[i]==o?r[i+2]=r[i+2].addInner(e,l,s+1):r.splice(i,0,o,o+e.nodeSize,tM(l,e,s+1,ty)),i+=3}});let o=tk(i?tS(t):t,-n);for(let t=0;t<o.length;t++)o[t].type.valid(e,o[t])||o.splice(t--,1);return new tv(o.length?this.local.concat(o).sort(tC):this.local,r||this.children)}remove(e){return 0==e.length||this==tb?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,r=this.local;for(let r=0;r<n.length;r+=3){let i;let o=n[r]+t,s=n[r+1]+t;for(let t=0,n;t<e.length;t++)(n=e[t])&&n.from>o&&n.to<s&&(e[t]=null,(i||(i=[])).push(n));if(!i)continue;n==this.children&&(n=this.children.slice());let l=n[r+2].removeInner(i,o+1);l!=tb?n[r+2]=l:(n.splice(r,3),r-=3)}if(r.length){for(let n=0,i;n<e.length;n++)if(i=e[n])for(let e=0;e<r.length;e++)r[e].eq(i,t)&&(r==this.local&&(r=this.local.slice()),r.splice(e--,1))}return n==this.children&&r==this.local?this:r.length||n.length?new tv(r,n):tb}forChild(e,t){let n,r;if(this==tb)return this;if(t.isLeaf)return tv.empty;for(let t=0;t<this.children.length;t+=3)if(this.children[t]>=e){this.children[t]==e&&(n=this.children[t+2]);break}let i=e+1,o=i+t.content.size;for(let e=0;e<this.local.length;e++){let t=this.local[e];if(t.from<o&&t.to>i&&t.type instanceof tu){let e=Math.max(i,t.from)-i,n=Math.min(o,t.to)-i;e<n&&(r||(r=[])).push(t.copy(e,n))}}if(r){let e=new tv(r.sort(tC),tg);return n?new tw([e,n]):e}return n||tb}eq(e){if(this==e)return!0;if(!(e instanceof tv)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return tO(this.localsInner(e))}localsInner(e){if(this==tb)return tg;if(e.inlineContent||!this.local.some(tu.is))return this.local;let t=[];for(let e=0;e<this.local.length;e++)this.local[e].type instanceof tu||t.push(this.local[e]);return t}forEachSet(e){e(this)}}tv.empty=new tv([],[]),tv.removeOverlap=tO;let tb=tv.empty;class tw{constructor(e){this.members=e}map(e,t){let n=this.members.map(n=>n.map(e,t,ty));return tw.from(n)}forChild(e,t){if(t.isLeaf)return tv.empty;let n=[];for(let r=0;r<this.members.length;r++){let i=this.members[r].forChild(e,t);i!=tb&&(i instanceof tw?n=n.concat(i.members):n.push(i))}return tw.from(n)}eq(e){if(!(e instanceof tw)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let r=0;r<this.members.length;r++){let i=this.members[r].localsInner(e);if(i.length){if(t){n&&(t=t.slice(),n=!1);for(let e=0;e<i.length;e++)t.push(i[e])}else t=i}}return t?tO(n?t:t.sort(tC)):tg}static from(e){switch(e.length){case 0:return tb;case 1:return e[0];default:return new tw(e.every(e=>e instanceof tv)?e:e.reduce((e,t)=>e.concat(t instanceof tv?t:t.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function tk(e,t){if(!t||!e.length)return e;let n=[];for(let r=0;r<e.length;r++){let i=e[r];n.push(new tm(i.from+t,i.to+t,i.type))}return n}function tx(e,t,n){if(t.isLeaf)return null;let r=n+t.nodeSize,i=null;for(let t=0,o;t<e.length;t++)(o=e[t])&&o.from>n&&o.to<r&&((i||(i=[])).push(o),e[t]=null);return i}function tS(e){let t=[];for(let n=0;n<e.length;n++)null!=e[n]&&t.push(e[n]);return t}function tM(e,t,n,r){let i=[],o=!1;t.forEach((t,s)=>{let l=tx(e,t,s+n);if(l){o=!0;let e=tM(l,t,n+s+1,r);e!=tb&&i.push(s,s+t.nodeSize,e)}});let s=tk(o?tS(e):e,-n).sort(tC);for(let e=0;e<s.length;e++)s[e].type.valid(t,s[e])||(r.onRemove&&r.onRemove(s[e].spec),s.splice(e--,1));return s.length||i.length?new tv(s,i):tb}function tC(e,t){return e.from-t.from||e.to-t.to}function tO(e){let t=e;for(let n=0;n<t.length-1;n++){let r=t[n];if(r.from!=r.to)for(let i=n+1;i<t.length;i++){let o=t[i];if(o.from==r.from){o.to!=r.to&&(t==e&&(t=e.slice()),t[i]=o.copy(o.from,r.to),tT(t,i+1,o.copy(r.to,o.to)));continue}o.from<r.to&&(t==e&&(t=e.slice()),t[n]=r.copy(r.from,o.from),tT(t,i,r.copy(o.from,r.to)));break}}return t}function tT(e,t,n){for(;t<e.length&&tC(n,e[t])>0;)t++;e.splice(t,0,n)}function tN(e){let t=[];return e.someProp("decorations",n=>{let r=n(e.state);r&&r!=tb&&t.push(r)}),e.cursorWrapper&&t.push(tv.create(e.state.doc,[e.cursorWrapper.deco])),tw.from(t)}let tE={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},tA=M&&C<=11;class tD{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class tI{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new tD,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(e=>{for(let t=0;t<e.length;t++)this.queue.push(e[t]);M&&C<=11&&e.some(e=>"childList"==e.type&&e.removedNodes.length||"characterData"==e.type&&e.oldValue.length>e.target.nodeValue.length)?this.flushSoon():this.flush()}),tA&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,tE)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(eE(this.view)){if(this.suppressingSelectionUpdates)return ek(this.view);if(M&&C<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&c(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,n;for(let n=e.focusNode;n;n=l(n))t.add(n);for(let r=e.anchorNode;r;r=l(r))if(t.has(r)){n=r;break}let r=n&&this.view.docView.nearestDesc(n);if(r&&r.ignoreMutation({type:"selection",target:3==n.nodeType?n.parentNode:n}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let n=e.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&eE(e)&&!this.ignoreSelectionChange(n),o=-1,s=-1,l=!1,a=[];if(e.editable)for(let e=0;e<t.length;e++){let n=this.registerMutation(t[e],a);n&&(o=o<0?n.from:Math.min(n.from,o),s=s<0?n.to:Math.max(n.to,s),n.typeOver&&(l=!0))}if(O&&a.length){let t=a.filter(e=>"BR"==e.nodeName);if(2==t.length){let[e,n]=t;e.parentNode&&e.parentNode.parentNode==n.parentNode?n.remove():e.remove()}else{let{focusNode:n}=this.currentSelection;for(let r of t){let t=r.parentNode;t&&"LI"==t.nodeName&&(!n||function(e,t){for(let n=t.parentNode;n&&n!=e.dom;n=n.parentNode){let t=e.docView.nearestDesc(n,!0);if(t&&t.node.isBlock)return n}return null}(e,n)!=t)&&r.remove()}}}let h=null;o<0&&i&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&g(n)&&(h=eb(e))&&h.eq(r.LN.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,ek(e),this.currentSelection.set(n),e.scrollToSelection()):(o>-1||i)&&(o>-1&&(e.docView.markDirty(o,s),tR.has(e)||(tR.set(e,null),-1===["normal","nowrap","pre-line"].indexOf(getComputedStyle(e.dom).whiteSpace))||(e.requiresGeckoHackNode=O,tP||(console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),tP=!0))),this.handleDOMChange(o,s,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||ek(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if("attributes"==e.type&&(n==this.view.docView||"contenteditable"==e.attributeName||"style"==e.attributeName&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if("childList"==e.type){for(let n=0;n<e.addedNodes.length;n++){let r=e.addedNodes[n];t.push(r),3==r.nodeType&&(this.lastChangedTextNode=r)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let r=e.previousSibling,i=e.nextSibling;if(M&&C<=11&&e.addedNodes.length)for(let t=0;t<e.addedNodes.length;t++){let{previousSibling:n,nextSibling:o}=e.addedNodes[t];(!n||0>Array.prototype.indexOf.call(e.addedNodes,n))&&(r=n),(!o||0>Array.prototype.indexOf.call(e.addedNodes,o))&&(i=o)}let o=r&&r.parentNode==e.target?s(r)+1:0,l=n.localPosFromDOM(e.target,o,-1),a=i&&i.parentNode==e.target?s(i):e.target.childNodes.length;return{from:l,to:n.localPosFromDOM(e.target,a,1)}}return"attributes"==e.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=e.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let tR=new WeakMap,tP=!1;function tz(e,t){let n=t.startContainer,r=t.startOffset,i=t.endContainer,o=t.endOffset,s=e.domAtPos(e.state.selection.anchor);return c(s.node,s.offset,i,o)&&([n,r,i,o]=[i,o,n,r]),{anchorNode:n,anchorOffset:r,focusNode:i,focusOffset:o}}function tB(e){let t=e.pmViewDesc;if(t)return t.parseRule();if("BR"==e.nodeName&&e.parentNode){if(A&&/^(ul|ol)$/i.test(e.parentNode.nodeName)){let e=document.createElement("div");return e.appendChild(document.createElement("li")),{skip:e}}if(e.parentNode.lastChild==e||A&&/^(tr|table)$/i.test(e.parentNode.nodeName))return{ignore:!0}}else if("IMG"==e.nodeName&&e.getAttribute("mark-placeholder"))return{ignore:!0};return null}let tF=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function tL(e,t,n){return Math.max(n.anchor,n.head)>t.content.size?null:eN(e,t.resolve(n.anchor),t.resolve(n.head))}function t$(e,t,n){let r=e.depth,i=t?e.end():e.pos;for(;r>0&&(t||e.indexAfter(r)==e.node(r).childCount);)r--,i++,t=!1;if(n){let t=e.node(r).maybeChild(e.indexAfter(r));for(;t&&!t.isLeaf;)t=t.firstChild,i++}return i}function tV(e){if(2!=e.length)return!1;let t=e.charCodeAt(0),n=e.charCodeAt(1);return t>=56320&&t<=57343&&n>=55296&&n<=56319}class tJ{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new e2,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(tq),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):"function"==typeof e?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=tH(this),tj(this),this.nodeViews=tW(this),this.docView=eo(this.state.doc,tK(this),tN(this),this.dom,this),this.domObserver=new tI(this,(e,t,n,o)=>(function(e,t,n,o,s){let l,a,h,d,c,p,u=e.input.compositionPendingChanges||(e.composing?e.input.compositionID:0);if(e.input.compositionPendingChanges=0,t<0){let t=e.input.lastSelectionTime>Date.now()-50?e.input.lastSelectionOrigin:null,n=eb(e,t);if(n&&!e.state.selection.eq(n)){if(N&&P&&13===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime&&e.someProp("handleKeyDown",t=>t(e,y(13,"Enter"))))return;let r=e.state.tr.setSelection(n);"pointer"==t?r.setMeta("pointer",!0):"key"==t&&r.scrollIntoView(),u&&r.setMeta("composition",u),e.dispatch(r)}return}let f=e.state.doc.resolve(t),m=f.sharedDepth(n);t=f.before(m+1),n=e.state.doc.resolve(n).after(m+1);let v=e.state.selection,b=function(e,t,n){let r,{node:o,fromOffset:s,toOffset:l,from:a,to:h}=e.docView.parseRange(t,n),d=e.domSelectionRange(),c=d.anchorNode;if(c&&e.dom.contains(1==c.nodeType?c:c.parentNode)&&(r=[{node:c,offset:d.anchorOffset}],g(d)||r.push({node:d.focusNode,offset:d.focusOffset})),N&&8===e.input.lastKeyCode)for(let e=l;e>s;e--){let t=o.childNodes[e-1],n=t.pmViewDesc;if("BR"==t.nodeName&&!n){l=e;break}if(!n||n.size)break}let p=e.state.doc,u=e.someProp("domParser")||i.S4.fromSchema(e.state.schema),f=p.resolve(a),m=null,y=u.parse(o,{topNode:f.parent,topMatch:f.parent.contentMatchAt(f.index()),topOpen:!0,from:s,to:l,preserveWhitespace:"pre"!=f.parent.type.whitespace||"full",findPositions:r,ruleFromNode:tB,context:f});if(r&&null!=r[0].pos){let e=r[0].pos,t=r[1]&&r[1].pos;null==t&&(t=e),m={anchor:e+a,head:t+a}}return{doc:y,sel:m,from:a,to:h}}(e,t,n),w=e.state.doc,k=w.slice(b.from,b.to);8===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime?(l=e.state.selection.to,a="end"):(l=e.state.selection.from,a="start"),e.input.lastKeyCode=null;let x=function(e,t,n,r,i){let o=e.findDiffStart(t,n);if(null==o)return null;let{a:s,b:l}=e.findDiffEnd(t,n+e.size,n+t.size);if("end"==i){let e=Math.max(0,o-Math.min(s,l));r-=s+e-o}if(s<o&&e.size<t.size){let e=r<=o&&r>=s?o-r:0;(o-=e)&&o<t.size&&tV(t.textBetween(o-1,o+1))&&(o+=e?1:-1),l=o+(l-s),s=o}else if(l<o){let t=r<=o&&r>=l?o-r:0;(o-=t)&&o<e.size&&tV(e.textBetween(o-1,o+1))&&(o+=t?1:-1),s=o+(s-l),l=o}return{start:o,endA:s,endB:l}}(k.content,b.doc.content,b.from,l,a);if(x&&e.input.domChangeCount++,(D&&e.input.lastIOSEnter>Date.now()-225||P)&&s.some(e=>1==e.nodeType&&!tF.test(e.nodeName))&&(!x||x.endA>=x.endB)&&e.someProp("handleKeyDown",t=>t(e,y(13,"Enter")))){e.input.lastIOSEnter=0;return}if(!x){if(o&&v instanceof r.U3&&!v.empty&&v.$head.sameParent(v.$anchor)&&!e.composing&&!(b.sel&&b.sel.anchor!=b.sel.head))x={start:v.from,endA:v.to,endB:v.to};else{if(b.sel){let t=tL(e,e.state.doc,b.sel);if(t&&!t.eq(e.state.selection)){let n=e.state.tr.setSelection(t);u&&n.setMeta("composition",u),e.dispatch(n)}}return}}e.state.selection.from<e.state.selection.to&&x.start==x.endB&&e.state.selection instanceof r.U3&&(x.start>e.state.selection.from&&x.start<=e.state.selection.from+2&&e.state.selection.from>=b.from?x.start=e.state.selection.from:x.endA<e.state.selection.to&&x.endA>=e.state.selection.to-2&&e.state.selection.to<=b.to&&(x.endB+=e.state.selection.to-x.endA,x.endA=e.state.selection.to)),M&&C<=11&&x.endB==x.start+1&&x.endA==x.start&&x.start>b.from&&" \xa0"==b.doc.textBetween(x.start-b.from-1,x.start-b.from+1)&&(x.start--,x.endA--,x.endB--);let S=b.doc.resolveNoCache(x.start-b.from),O=b.doc.resolveNoCache(x.endB-b.from),T=w.resolve(x.start),E=S.sameParent(O)&&S.parent.inlineContent&&T.end()>=x.endA;if((D&&e.input.lastIOSEnter>Date.now()-225&&(!E||s.some(e=>"DIV"==e.nodeName||"P"==e.nodeName))||!E&&S.pos<b.doc.content.size&&!S.sameParent(O)&&(h=r.LN.findFrom(b.doc.resolve(S.pos+1),1,!0))&&h.head==O.pos)&&e.someProp("handleKeyDown",t=>t(e,y(13,"Enter")))){e.input.lastIOSEnter=0;return}if(e.state.selection.anchor>x.start&&function(e,t,n,r,i){if(n-t<=i.pos-r.pos||t$(r,!0,!1)<i.pos)return!1;let o=e.resolve(t);if(!r.parent.isTextblock){let e=o.nodeAfter;return null!=e&&n==t+e.nodeSize}if(o.parentOffset<o.parent.content.size||!o.parent.isTextblock)return!1;let s=e.resolve(t$(o,!0,!0));return!(!s.parent.isTextblock||s.pos>n||t$(s,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(s.parent.content)}(w,x.start,x.endA,S,O)&&e.someProp("handleKeyDown",t=>t(e,y(8,"Backspace")))){P&&N&&e.domObserver.suppressSelectionUpdates();return}N&&x.endB==x.start&&(e.input.lastChromeDelete=Date.now()),P&&!E&&S.start()!=O.start()&&0==O.parentOffset&&S.depth==O.depth&&b.sel&&b.sel.anchor==b.sel.head&&b.sel.head==x.endA&&(x.endB-=2,O=b.doc.resolveNoCache(x.endB-b.from),setTimeout(()=>{e.someProp("handleKeyDown",function(t){return t(e,y(13,"Enter"))})},20));let A=x.start,I=x.endA;if(E){if(S.pos==O.pos)M&&C<=11&&0==S.parentOffset&&(e.domObserver.suppressSelectionUpdates(),setTimeout(()=>ek(e),20)),d=e.state.tr.delete(A,I),c=w.resolve(x.start).marksAcross(w.resolve(x.endA));else if(x.endA==x.endB&&(p=function(e,t){let n=e.firstChild.marks,r=t.firstChild.marks,o=n,s=r,l,a,h;for(let e=0;e<r.length;e++)o=r[e].removeFromSet(o);for(let e=0;e<n.length;e++)s=n[e].removeFromSet(s);if(1==o.length&&0==s.length)a=o[0],l="add",h=e=>e.mark(a.addToSet(e.marks));else{if(0!=o.length||1!=s.length)return null;a=s[0],l="remove",h=e=>e.mark(a.removeFromSet(e.marks))}let d=[];for(let e=0;e<t.childCount;e++)d.push(h(t.child(e)));if(i.FK.from(d).eq(e))return{mark:a,type:l}}(S.parent.content.cut(S.parentOffset,O.parentOffset),T.parent.content.cut(T.parentOffset,x.endA-T.start()))))d=e.state.tr,"add"==p.type?d.addMark(A,I,p.mark):d.removeMark(A,I,p.mark);else if(S.parent.child(S.index()).isText&&S.index()==O.index()-(O.textOffset?0:1)){let t=S.parent.textBetween(S.parentOffset,O.parentOffset);if(e.someProp("handleTextInput",n=>n(e,A,I,t)))return;d=e.state.tr.insertText(t,A,I)}}if(d||(d=e.state.tr.replace(A,I,b.doc.slice(x.start-b.from,x.endB-b.from))),b.sel){let t=tL(e,d.doc,b.sel);t&&!(N&&e.composing&&t.empty&&(x.start!=x.endB||e.input.lastChromeDelete<Date.now()-100)&&(t.head==A||t.head==d.mapping.map(I)-1)||M&&t.empty&&t.head==A)&&d.setSelection(t)}c&&d.ensureMarks(c),u&&d.setMeta("composition",u),e.dispatch(d.scrollIntoView())})(this,e,t,n,o)),this.domObserver.start(),function(e){for(let t in eQ){let n=eQ[t];e.dom.addEventListener(t,e.input.eventHandlers[t]=t=>{!function(e,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let n=t.target;n!=e.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(t))return!1;return!0}(e,t)||e8(e,t)||!e.editable&&t.type in e0||n(e,t)},e1[t]?{passive:!0}:void 0)}A&&e.dom.addEventListener("input",()=>null),e5(e)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;for(let t in this._props={},e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&e5(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(tq),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let e in this._props)t[e]=this._props[e];for(let n in t.state=this.state,e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n,r,i;let o=this.state,l=!1,a=!1;e.storedMarks&&this.composing&&(ti(this),a=!0),this.state=e;let h=o.plugins!=e.plugins||this._props.plugins!=t.plugins;if(h||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let e=tW(this);(function(e,t){let n=0,r=0;for(let r in e){if(e[r]!=t[r])return!0;n++}for(let e in t)r++;return n!=r})(e,this.nodeViews)&&(this.nodeViews=e,l=!0)}(h||t.handleDOMEvents!=this._props.handleDOMEvents)&&e5(this),this.editable=tH(this),tj(this);let d=tN(this),p=tK(this),u=o.plugins==e.plugins||o.doc.eq(e.doc)?e.scrollToSelection>o.scrollToSelection?"to selection":"preserve":"reset",g=l||!this.docView.matchesNode(e.doc,p,d);(g||!e.selection.eq(o.selection))&&(a=!0);let y="preserve"==u&&a&&null==this.dom.style.overflowAnchor&&function(e){let t,n,r=e.dom.getBoundingClientRect(),i=Math.max(0,r.top);for(let o=(r.left+r.right)/2,s=i+1;s<Math.min(innerHeight,r.bottom);s+=5){let r=e.root.elementFromPoint(o,s);if(!r||r==e.dom||!e.dom.contains(r))continue;let l=r.getBoundingClientRect();if(l.top>=i-20){t=r,n=l.top;break}}return{refDOM:t,refTop:n,stack:$(e.dom)}}(this);if(a){let t,n,a;this.domObserver.stop();let h=g&&(M||N)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&(r=o.selection,i=e.selection,a=Math.min(r.$anchor.sharedDepth(r.head),i.$anchor.sharedDepth(i.head)),r.$anchor.start(a)!=i.$anchor.start(a));if(g){let t=N?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=function(e){let t=e.domSelectionRange();if(!t.focusNode)return null;let n=function(e,t){for(;;){if(3==e.nodeType&&t)return e;if(1==e.nodeType&&t>0){if("false"==e.contentEditable)return null;t=f(e=e.childNodes[t-1])}else{if(!e.parentNode||m(e))return null;t=s(e),e=e.parentNode}}}(t.focusNode,t.focusOffset),r=function(e,t){for(;;){if(3==e.nodeType&&t<e.nodeValue.length)return e;if(1==e.nodeType&&t<e.childNodes.length){if("false"==e.contentEditable)return null;e=e.childNodes[t],t=0}else{if(!e.parentNode||m(e))return null;t=s(e)+1,e=e.parentNode}}}(t.focusNode,t.focusOffset);if(n&&r&&n!=r){let t=r.pmViewDesc,i=e.domObserver.lastChangedTextNode;if(n==i||r==i)return i;if(!t||!t.isText(r.nodeValue))return r;if(e.input.compositionNode==r){let e=n.pmViewDesc;if(!(!e||!e.isText(n.nodeValue)))return r}}return n||r}(this)),(l||!this.docView.update(e.doc,p,d,this))&&(this.docView.updateOuterDeco(p),this.docView.destroy(),this.docView=eo(e.doc,p,d,this.dom,this)),t&&!this.trackWrites&&(h=!0)}h||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&(t=this.docView.domFromPos(this.state.selection.anchor,0),n=this.domSelectionRange(),c(t.node,t.offset,n.anchorNode,n.anchorOffset)))?ek(this,h):(eO(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),(null===(n=this.dragging)||void 0===n?void 0:n.node)&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),"reset"==u?this.dom.scrollTop=0:"to selection"==u?this.scrollToSelection():y&&function({refDOM:e,refTop:t,stack:n}){let r=e?e.getBoundingClientRect().top:0;V(n,0==r?0:r-t)}(y)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(e&&this.dom.contains(1==e.nodeType?e:e.parentNode)){if(this.someProp("handleScrollToSelection",e=>e(this)));else if(this.state.selection instanceof r.nh){let t=this.docView.domAfterPos(this.state.selection.from);1==t.nodeType&&L(this,t.getBoundingClientRect(),e)}else L(this,this.coordsAtPos(this.state.selection.head,1),e)}}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let e=0;e<this.directPlugins.length;e++){let t=this.directPlugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}for(let e=0;e<this.state.plugins.length;e++){let t=this.state.plugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,i=-1;if(this.state.doc.nodeAt(n.from)==n.node)i=n.from;else{let e=n.from+(this.state.doc.content.size-t.doc.content.size);(e>0&&this.state.doc.nodeAt(e))==n.node&&(i=e)}this.dragging=new th(e.slice,e.move,i<0?void 0:r.nh.create(this.state.doc,i))}someProp(e,t){let n=this._props&&this._props[e],r;if(null!=n&&(r=t?t(n):n))return r;for(let n=0;n<this.directPlugins.length;n++){let i=this.directPlugins[n].props[e];if(null!=i&&(r=t?t(i):i))return r}let i=this.state.plugins;if(i)for(let n=0;n<i.length;n++){let o=i[n].props[e];if(null!=o&&(r=t?t(o):o))return r}}hasFocus(){if(M){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if("false"==e.contentEditable)return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if(J)return e.focus(J);let t=$(e);e.focus(null==J?{get preventScroll(){return J={preventScroll:!0},!0}}:void 0),J||(J=!1,V(t,0))}(this.dom),ek(this),this.domObserver.start()}get root(){let e=this._root;if(null==e){for(let e=this.dom.parentNode;e;e=e.parentNode)if(9==e.nodeType||11==e.nodeType&&e.host)return e.getSelection||(Object.getPrototypeOf(e).getSelection=()=>e.ownerDocument.getSelection()),this._root=e}return e||document}updateRoot(){this._root=null}posAtCoords(e){return function(e,t){var n;let r,i,o=e.dom.ownerDocument,s,a=0,d=function(e,t,n){if(e.caretPositionFromPoint)try{let r=e.caretPositionFromPoint(t,n);if(r)return{node:r.offsetNode,offset:Math.min(f(r.offsetNode),r.offset)}}catch(e){}if(e.caretRangeFromPoint){let r=e.caretRangeFromPoint(t,n);if(r)return{node:r.startContainer,offset:Math.min(f(r.startContainer),r.startOffset)}}}(o,t.left,t.top);d&&({node:s,offset:a}=d);let c=(e.root.elementFromPoint?e.root:o).elementFromPoint(t.left,t.top);if(!c||!e.dom.contains(1!=c.nodeType?c.parentNode:c)){let n=e.dom.getBoundingClientRect();if(!K(t,n)||!(c=function e(t,n,r){let i=t.childNodes.length;if(i&&r.top<r.bottom)for(let o=Math.max(0,Math.min(i-1,Math.floor(i*(n.top-r.top)/(r.bottom-r.top))-2)),s=o;;){let r=t.childNodes[s];if(1==r.nodeType){let t=r.getClientRects();for(let i=0;i<t.length;i++){let o=t[i];if(K(n,o))return e(r,n,o)}}if((s=(s+1)%i)==o)break}return t}(e.dom,t,n)))return null}if(A)for(let e=c;s&&e;e=l(e))e.draggable&&(s=void 0);if(c=(r=(n=c).parentNode)&&/^li$/i.test(r.nodeName)&&t.left<n.getBoundingClientRect().left?r:n,s){let n;if(O&&1==s.nodeType&&(a=Math.min(a,s.childNodes.length))<s.childNodes.length){let e=s.childNodes[a],n;"IMG"==e.nodeName&&(n=e.getBoundingClientRect()).right<=t.left&&n.bottom>t.top&&a++}z&&a&&1==s.nodeType&&1==(n=s.childNodes[a-1]).nodeType&&"false"==n.contentEditable&&n.getBoundingClientRect().top>=t.top&&a--,s==e.dom&&a==s.childNodes.length-1&&1==s.lastChild.nodeType&&t.top>s.lastChild.getBoundingClientRect().bottom?i=e.state.doc.content.size:(0==a||1!=s.nodeType||"BR"!=s.childNodes[a-1].nodeName)&&(i=function(e,t,n,r){let i=-1;for(let n=t,o=!1;n!=e.dom;){let t=e.docView.nearestDesc(n,!0),s;if(!t)return null;if(1==t.dom.nodeType&&(t.node.isBlock&&t.parent||!t.contentDOM)&&((s=t.dom.getBoundingClientRect()).width||s.height)&&(t.node.isBlock&&t.parent&&(!o&&s.left>r.left||s.top>r.top?i=t.posBefore:(!o&&s.right<r.left||s.bottom<r.top)&&(i=t.posAfter),o=!0),!t.contentDOM&&i<0&&!t.node.isText))return(t.node.isBlock?r.top<(s.top+s.bottom)/2:r.left<(s.left+s.right)/2)?t.posBefore:t.posAfter;n=t.dom.parentNode}return i>-1?i:e.docView.posFromDOM(t,n,-1)}(e,s,a,t))}null==i&&(i=function(e,t,n){let{node:r,offset:i}=function e(t,n){let r,i,o,s=2e8,l,a=0,d=n.top,c=n.top;for(let e=t.firstChild,p=0;e;e=e.nextSibling,p++){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=h(e).getClientRects()}for(let h=0;h<t.length;h++){let u=t[h];if(u.top<=d&&u.bottom>=c){d=Math.max(u.bottom,d),c=Math.min(u.top,c);let t=u.left>n.left?u.left-n.left:u.right<n.left?n.left-u.right:0;if(t<s){o=e,s=t,l=t&&3==o.nodeType?{left:u.right<n.left?u.right:u.left,top:n.top}:n,1==e.nodeType&&t&&(a=p+(n.left>=(u.left+u.right)/2?1:0));continue}}else u.top>n.top&&!r&&u.left<=n.left&&u.right>=n.left&&(r=e,i={left:Math.max(u.left,Math.min(u.right,n.left)),top:u.top});!o&&(n.left>=u.right&&n.top>=u.top||n.left>=u.left&&n.top>=u.bottom)&&(a=p+1)}}return(!o&&r&&(o=r,l=i,s=0),o&&3==o.nodeType)?function(e,t){let n=e.nodeValue.length,r=document.createRange();for(let i=0;i<n;i++){r.setEnd(e,i+1),r.setStart(e,i);let n=H(r,1);if(n.top!=n.bottom&&K(t,n))return{node:e,offset:i+(t.left>=(n.left+n.right)/2?1:0)}}return{node:e,offset:0}}(o,l):!o||s&&1==o.nodeType?{node:t,offset:a}:e(o,l)}(t,n),o=-1;if(1==r.nodeType&&!r.firstChild){let e=r.getBoundingClientRect();o=e.left!=e.right&&n.left>(e.left+e.right)/2?1:-1}return e.docView.posFromDOM(r,i,o)}(e,c,t));let p=e.docView.nearestDesc(c,!0);return{pos:i,inside:p?p.posAtStart-p.border:-1}}(this,e)}coordsAtPos(e,t=1){return q(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let r=this.docView.posFromDOM(e,t,n);if(null==r)throw RangeError("DOM position not inside the editor");return r}endOfTextblock(e,t){var n,r,i;let o,s;return n=this,r=t||this.state,i=e,Y==r&&Z==i?Q:(Y=r,Z=i,Q="up"==i||"down"==i?(o=r.selection,s="up"==i?o.$from:o.$to,G(n,r,()=>{let{node:e}=n.docView.domFromPos(s.pos,"up"==i?-1:1);for(;;){let t=n.docView.nearestDesc(e,!0);if(!t)break;if(t.node.isBlock){e=t.contentDOM||t.dom;break}e=t.dom.parentNode}let t=q(n,s.pos,1);for(let n=e.firstChild;n;n=n.nextSibling){let e;if(1==n.nodeType)e=n.getClientRects();else{if(3!=n.nodeType)continue;e=h(n,0,n.nodeValue.length).getClientRects()}for(let n=0;n<e.length;n++){let r=e[n];if(r.bottom>r.top+1&&("up"==i?t.top-r.top>(r.bottom-t.top)*2:r.bottom-t.bottom>(t.bottom-r.top)*2))return!1}}return!0})):function(e,t,n){let{$head:r}=t.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,o=i==r.parent.content.size,s=e.domSelection();return s?X.test(r.parent.textContent)&&s.modify?G(e,t,()=>{let{focusNode:t,focusOffset:i,anchorNode:o,anchorOffset:l}=e.domSelectionRange(),a=s.caretBidiLevel;s.modify("move",n,"character");let h=r.depth?e.docView.domAfterPos(r.before()):e.dom,{focusNode:d,focusOffset:c}=e.domSelectionRange(),p=d&&!h.contains(1==d.nodeType?d:d.parentNode)||t==d&&i==c;try{s.collapse(o,l),t&&(t!=o||i!=l)&&s.extend&&s.extend(t,i)}catch(e){}return null!=a&&(s.caretBidiLevel=a),p}):"left"==n||"backward"==n?!i:o:r.pos==r.start()||r.pos==r.end()}(n,r,i))}pasteHTML(e,t){return tl(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return tl(this,e,null,!0,t||new ClipboardEvent("paste"))}destroy(){this.docView&&(function(e){for(let t in e.domObserver.stop(),e.input.eventHandlers)e.dom.removeEventListener(t,e.input.eventHandlers[t]);clearTimeout(e.input.composingTimeout),clearTimeout(e.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],tN(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,d())}get isDestroyed(){return null==this.docView}dispatchEvent(e){e8(this,e)||!eQ[e.type]||!this.editable&&e.type in e0||eQ[e.type](this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return e?A&&11===this.root.nodeType&&function(e){let t=e.activeElement;for(;t&&t.shadowRoot;)t=t.shadowRoot.activeElement;return t}(this.dom.ownerDocument)==this.dom&&function(e,t){let n;if(t.getComposedRanges){let n=t.getComposedRanges(e.root)[0];if(n)return tz(e,n)}function r(e){e.preventDefault(),e.stopImmediatePropagation(),n=e.getTargetRanges()[0]}return e.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),e.dom.removeEventListener("beforeinput",r,!0),n?tz(e,n):null}(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function tK(e){let t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(e.editable),e.someProp("attributes",n=>{if("function"==typeof n&&(n=n(e.state)),n)for(let e in n)"class"==e?t.class+=" "+n[e]:"style"==e?t.style=(t.style?t.style+";":"")+n[e]:t[e]||"contenteditable"==e||"nodeName"==e||(t[e]=String(n[e]))}),t.translate||(t.translate="no"),[tm.node(0,e.state.doc.content.size,t)]}function tj(e){if(e.markCursor){let t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),e.cursorWrapper={dom:t,deco:tm.widget(e.state.selection.from,t,{raw:!0,marks:e.markCursor})}}else e.cursorWrapper=null}function tH(e){return!e.someProp("editable",t=>!1===t(e.state))}function tW(e){let t=Object.create(null);function n(e){for(let n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n])}return e.someProp("nodeViews",n),e.someProp("markViews",n),t}function tq(e){if(e.spec.state||e.spec.filterTransaction||e.spec.appendTransaction)throw RangeError("Plugins passed directly to the view must not have a state component")}}};