(()=>{var e={};e.id=1161,e.ids=[1161],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},39164:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var o=t(70260),s=t(28203),a=t(25155),n=t.n(a),i=t(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l=["",{children:["(dashboard)",{children:["downloadables",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,74371)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\new\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/downloadables/new/page",pathname:"/downloadables/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},80564:(e,r,t)=>{Promise.resolve().then(t.bind(t,74371))},33300:(e,r,t)=>{Promise.resolve().then(t.bind(t,32111))},32111:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var o=t(45512),s=t(58009),a=t(79334),n=t(95826);function i(){let e=(0,a.useRouter)(),[r,t]=(0,s.useState)(!1),i=async(r,o)=>{if(!o){alert("Please select a file to upload");return}t(!0);try{let t=new FormData;t.append("file",o),t.append("folder","downloadable_files");let s=await fetch("/api/upload/cloud",{method:"POST",body:t});if(!s.ok)throw Error("File upload failed");let{url:a}=await s.json();if((await fetch("/api/downloadables",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...r,file_url:a,file_size:o.size,file_type:o.type})})).ok)e.push("/downloadables");else throw Error("Failed to create file record")}catch(e){console.error("Error creating downloadable file:",e),alert("Error saving file. Please try again.")}finally{t(!1)}};return(0,o.jsxs)("div",{className:"p-6 ",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Add New Resource File"}),(0,o.jsx)("div",{className:"bg-white rounded-lg border p-6",children:(0,o.jsx)(n.A,{onSubmit:i,isLoading:r})})]})}},74371:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\downloadables\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\new\\page.tsx","default")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[638,8403,7834,9267,4873,4708],()=>t(39164));module.exports=o})();