(()=>{var e={};e.id=6808,e.ids=[6808],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},53952:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>o,routeModule:()=>m,tree:()=>c});var t=a(70260),i=a(28203),r=a(25155),l=a.n(r),n=a(67292),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);a.d(s,d);let c=["",{children:["(dashboard)",{children:["fyp-applications",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,23506)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\fyp-applications\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\fyp-applications\\[id]\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/fyp-applications/[id]/page",pathname:"/fyp-applications/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},81795:(e,s,a)=>{Promise.resolve().then(a.bind(a,23506))},47451:(e,s,a)=>{Promise.resolve().then(a.bind(a,68238))},68238:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var t=a(45512),i=a(58009),r=a(79334),l=a(87021),n=a(97643),d=a(77252),c=a(48859),o=a(54069),p=a(71894),m=a(42092),x=a(93346),h=a(43161),u=a(73325),j=a(28790),v=a(71398);function f(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),[a,f]=(0,i.useState)(null),[g,y]=(0,i.useState)(!0),[b,N]=(0,i.useState)(!1),[w,_]=(0,i.useState)(""),[P,A]=(0,i.useState)(""),[k,R]=(0,i.useState)(""),D=async()=>{try{let s=await fetch(`/api/fyp-applications/${e?.id}`),a=await s.json();s.ok&&(f(a.application),_(a.application.status),A(a.application.review_notes||""),R(a.application.assigned_supervisor||""))}catch(e){console.error("Error fetching application:",e)}finally{y(!1)}},S=async()=>{if(a){N(!0);try{(await fetch(`/api/fyp-applications/${e?.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:w,review_notes:P,assigned_supervisor:k})})).ok&&(await D(),alert("Application updated successfully!"))}catch(e){console.error("Error updating application:",e),alert("Error updating application")}finally{N(!1)}}};return g?(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Loading application details..."})}):a?(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>s.back(),children:[(0,t.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Final Year Project Application"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Application ID: ",a.application_id]})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(e=>{let s={pending:{variant:"secondary",label:"Pending"},reviewed:{variant:"outline",label:"Reviewed"},accepted:{variant:"default",label:"Accepted"},rejected:{variant:"destructive",label:"Rejected"},"in-progress":{variant:"default",label:"In Progress"},completed:{variant:"default",label:"Completed"}},a=s[e]||s.pending;return(0,t.jsx)(d.E,{variant:a.variant,children:a.label})})(a.status)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Student Information"})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,t.jsx)("p",{className:"font-medium",children:a.student_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,t.jsx)("p",{className:"font-medium",children:a.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Phone"}),(0,t.jsx)("p",{className:"font-medium",children:a.phone})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Student ID"}),(0,t.jsx)("p",{className:"font-medium",children:a.student_id||"Not provided"})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Academic Information"})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"University"}),(0,t.jsx)("p",{className:"font-medium",children:a.university})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Course of Study"}),(0,t.jsx)("p",{className:"font-medium",children:a.course_of_study})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Year of Study"}),(0,t.jsx)("p",{className:"font-medium",children:a.year_of_study})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Project Information"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Project Title"}),(0,t.jsx)("p",{className:"font-medium",children:a.project_title||"Not provided"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Project Description"}),(0,t.jsx)("p",{className:"text-sm",children:a.project_description||"Not provided"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Project Type"}),(0,t.jsx)("p",{className:"font-medium capitalize",children:a.project_type})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Research Area"}),(0,t.jsx)("p",{className:"font-medium",children:a.research_area||"Not specified"})]})]}),a.methodology&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Methodology"}),(0,t.jsx)("p",{className:"text-sm",children:a.methodology})]}),a.expected_outcomes&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Expected Outcomes"}),(0,t.jsx)("p",{className:"text-sm",children:a.expected_outcomes})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Timeline & Resources"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Timeline (Weeks)"}),(0,t.jsx)("p",{className:"font-medium",children:a.timeline_weeks||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Project Deadline"}),(0,t.jsx)("p",{className:"font-medium",children:a.project_deadline?new Date(a.project_deadline).toLocaleDateString():"Not specified"})]})]}),a.required_resources&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Required Resources"}),(0,t.jsx)("p",{className:"text-sm",children:a.required_resources})]}),a.technical_requirements&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Technical Requirements"}),(0,t.jsx)("p",{className:"text-sm",children:a.technical_requirements})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Application Management"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Status"}),(0,t.jsxs)(o.l6,{value:w,onValueChange:_,children:[(0,t.jsx)(o.bq,{children:(0,t.jsx)(o.yv,{})}),(0,t.jsxs)(o.gC,{children:[(0,t.jsx)(o.eb,{value:"pending",children:"Pending"}),(0,t.jsx)(o.eb,{value:"reviewed",children:"Reviewed"}),(0,t.jsx)(o.eb,{value:"accepted",children:"Accepted"}),(0,t.jsx)(o.eb,{value:"rejected",children:"Rejected"}),(0,t.jsx)(o.eb,{value:"in-progress",children:"In Progress"}),(0,t.jsx)(o.eb,{value:"completed",children:"Completed"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Assigned Supervisor"}),(0,t.jsx)("input",{type:"text",value:k,onChange:e=>R(e.target.value),className:"w-full mt-1 px-3 py-2 border border-gray-300 rounded-md",placeholder:"Enter supervisor name"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Review Notes"}),(0,t.jsx)(c.T,{value:P,onChange:e=>A(e.target.value),placeholder:"Add review notes...",rows:4})]}),(0,t.jsxs)(l.$,{onClick:S,disabled:b,className:"w-full",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2"}),b?"Updating...":"Update Application"]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Application Details"})}),(0,t.jsxs)(n.Wu,{className:"space-y-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Applied Date"}),(0,t.jsx)("p",{className:"text-sm",children:new Date(a.application_date).toLocaleDateString()})]}),a.review_date&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Review Date"}),(0,t.jsx)("p",{className:"text-sm",children:new Date(a.review_date).toLocaleDateString()})]}),a.supervisor_name&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Current Supervisor"}),(0,t.jsx)("p",{className:"text-sm",children:a.supervisor_name})]})]})]})]})]})]}):(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center text-red-600",children:"Application not found"})})}},23506:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\fyp-applications\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\fyp-applications\\[id]\\page.tsx","default")},28790:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(94825).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},42092:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(94825).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[638,8403,7834,9267,4873,6562,3015,6516],()=>a(53952));module.exports=t})();