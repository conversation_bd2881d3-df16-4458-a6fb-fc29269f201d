exports.id=6516,exports.ids=[6516],exports.modules={94168:(e,t,r)=>{Promise.resolve().then(r.bind(r,60189)),Promise.resolve().then(r.bind(r,22234)),Promise.resolve().then(r.bind(r,56814))},3896:(e,t,r)=>{Promise.resolve().then(r.bind(r,52401)),Promise.resolve().then(r.bind(r,58783)),Promise.resolve().then(r.bind(r,91542))},98167:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},51311:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},52401:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(45512),a=r(58009),n=r(79334),o=r(91542);let i=({children:e})=>{let t=(0,n.useRouter)(),[r,i]=(0,a.useState)(null);return((0,a.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/admin/auth/check",{method:"GET",credentials:"include",cache:"no-store"});if(!e.ok)throw Error("Not authenticated");let t=await e.json();console.log("Auth check response:",t),i(!0)}catch(e){console.error("Authentication check failed:",e),i(!1),o.o.error("Please log in to access the dashboard"),t.push("/")}})()},[t]),null===r)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"})}):r?(0,s.jsx)(s.Fragment,{children:e}):null}},58783:(e,t,r)=>{"use strict";r.d(t,{default:()=>P});var s=r(45512),a=r(58009),n=r(28531),o=r.n(n),i=r(38440),l=r(1422),d=r(57631),c=r(35603),u=r(43161),f=r(62673),m=r(28650),p=r(52975),h=r(39327),b=r(55817),x=r(53100),v=r(93346),g=r(6472),y=r(51255),w=r(35120),j=r(48320),N=r(79334),k=r(91542);let A=({onToggle:e})=>(0,s.jsxs)("div",{className:"md:hidden bg-white border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30",children:[(0,s.jsx)("button",{type:"button",onClick:e,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Toggle navigation menu",title:"Toggle navigation menu",children:(0,s.jsx)(j.A,{className:"w-6 h-6"})}),(0,s.jsx)("h1",{className:"font-semibold text-lg",children:"Brissp Dashboard"}),(0,s.jsx)("div",{className:"w-10"})," "]}),C=({isOpen:e=!1,onToggle:t})=>{let r=(0,N.useRouter)(),[n,j]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=()=>{j(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,a.useEffect)(()=>{let r=r=>{if(n&&e&&t){let e=document.getElementById("mobile-sidebar");e&&!e.contains(r.target)&&t()}};if(n&&e)return document.addEventListener("mousedown",r),()=>document.removeEventListener("mousedown",r)},[n,e,t]),(0,a.useEffect)(()=>(n&&e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[n,e]);let A=async()=>{try{(await fetch("/api/admin/auth/logout",{method:"POST",credentials:"include"})).ok?(k.o.success("Logged out successfully"),r.push("/")):k.o.error("Failed to log out")}catch(e){console.error("Logout error:",e),k.o.error("An error occurred during logout")}},C=()=>{n&&t&&t()},P=[{href:"/panel",icon:i.A,label:"Dashboard"},{href:"/applications",icon:l.A,label:"Course Applications"},{href:"/pitch-deck-applications",icon:d.A,label:"Pitch Deck Applications"},{href:"/internship-applications",icon:c.A,label:"Internship Applications"},{href:"/fyp-applications",icon:u.A,label:"FYP Applications"},{href:"/innovation-lab-applications",icon:f.A,label:"Innovation Lab Applications"},{href:"/results",icon:m.A,label:"Results"},{href:"/downloadables",icon:p.A,label:"Resources"},{href:"/courses",icon:h.A,label:"Courses"},{href:"/curriculum",icon:b.A,label:"Curriculum"},{href:"/notice-board",icon:x.A,label:"Notice Board"},{href:"/graduates",icon:v.A,label:"Graduates"},{href:"/password-management",icon:g.A,label:"Password Management"}];return(0,s.jsxs)(s.Fragment,{children:[n&&e&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:t}),(0,s.jsxs)("div",{id:"mobile-sidebar",className:`
          ${n?`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`:"fixed inset-y-0 left-0 w-64"}
          border-r bg-white h-screen flex flex-col
        `,children:[(0,s.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,s.jsx)(o(),{href:"/panel",className:"flex items-center space-x-2",onClick:C,children:(0,s.jsx)("span",{className:"font-semibold text-lg",children:"Brissp Dash"})}),n&&(0,s.jsx)("button",{type:"button",onClick:t,className:"p-2 rounded-lg hover:bg-gray-100 md:hidden","aria-label":"Close navigation menu",title:"Close navigation menu",children:(0,s.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,s.jsx)("nav",{className:"p-4 space-y-2 flex-grow overflow-y-auto",children:P.map(e=>{let t=e.icon;return(0,s.jsxs)(o(),{href:e.href,onClick:C,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200",children:[(0,s.jsx)(t,{className:"w-5 h-5 flex-shrink-0"}),(0,s.jsx)("span",{className:"truncate",children:e.label})]},e.href)})}),(0,s.jsx)("div",{className:"p-4 border-t mt-auto",children:(0,s.jsxs)("button",{type:"button",onClick:A,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full transition-colors duration-200",children:[(0,s.jsx)(w.A,{className:"w-5 h-5 flex-shrink-0"}),(0,s.jsx)("span",{children:"Logout"})]})})]})]})},P=({children:e})=>{let[t,r]=(0,a.useState)(!1),[n,o]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;o(e),e||r(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let i=()=>{r(!t)};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(A,{onToggle:i}),(0,s.jsx)(C,{isOpen:t,onToggle:i}),(0,s.jsx)("div",{className:`
        transition-all duration-300 ease-in-out
        ${n?"ml-0":"ml-64"}
      `,children:(0,s.jsx)("main",{className:"min-h-screen",children:e})})]})}},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var s=r(45512);r(58009);var a=r(21643),n=r(59462);let o=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,n.cn)(o({variant:t}),e),...r})}},87021:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(45512),a=r(58009),n=r(12705),o=r(21643),i=r(59462);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...o},d)=>{let c=a?n.DX:"button";return(0,s.jsx)(c,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:d,...o})});d.displayName="Button"},97643:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>u});var s=r(45512),a=r(58009),n=r(59462);let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let u=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));u.displayName="CardFooter"},54069:(e,t,r)=>{"use strict";r.d(t,{bq:()=>f,eb:()=>b,gC:()=>h,l6:()=>c,yv:()=>u});var s=r(45512),a=r(58009),n=r(96096),o=r(7833),i=r(36624),l=r(24999),d=r(59462);let c=n.bL;n.YJ;let u=n.WT,f=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(n.l9,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,(0,s.jsx)(n.In,{asChild:!0,children:(0,s.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]}));f.displayName=n.l9.displayName;let m=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.PP,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})}));m.displayName=n.PP.displayName;let p=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.wn,{ref:r,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"h-4 w-4"})}));p.displayName=n.wn.displayName;let h=a.forwardRef(({className:e,children:t,position:r="popper",...a},o)=>(0,s.jsx)(n.ZL,{children:(0,s.jsxs)(n.UC,{ref:o,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...a,children:[(0,s.jsx)(m,{}),(0,s.jsx)(n.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(p,{})]})}));h.displayName=n.UC.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.JU,{ref:r,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=n.JU.displayName;let b=a.forwardRef(({className:e,children:t,...r},a)=>(0,s.jsxs)(n.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...r,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.VF,{children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})}),(0,s.jsx)(n.p4,{children:t})]}));b.displayName=n.q7.displayName,a.forwardRef(({className:e,...t},r)=>(0,s.jsx)(n.wv,{ref:r,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.wv.displayName},48859:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var s=r(45512),a=r(58009),n=r(59462);let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));o.displayName="Textarea"},59462:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(82281),a=r(94805);function n(...e){return(0,a.QP)((0,s.$)(e))}},71975:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>u});var s=r(62740),a=r(41583),n=r.n(a),o=r(76499),i=r.n(o);r(2012);var l=r(22234),d=r(60189),c=r(56814);let u={title:"Brissp Admin",description:"Brissp Academy Admin Dashboard"};function f({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsxs)("body",{className:`${n().variable} ${i().variable} antialiased`,children:[(0,s.jsx)(d.default,{children:(0,s.jsx)(l.default,{children:e})}),(0,s.jsx)(c.Toaster,{})]})})}},60189:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\AuthGuard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\AuthGuard.tsx","default")},22234:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\ResponsiveLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\ResponsiveLayout.tsx","default")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},2012:()=>{},71894:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},73325:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71398:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])}};