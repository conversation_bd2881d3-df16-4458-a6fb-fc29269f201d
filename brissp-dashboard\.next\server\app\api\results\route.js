(()=>{var e={};e.id=7372,e.ids=[7372],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},65796:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>_,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{GET:()=>p,POST:()=>c});var o=s(42706),u=s(28203),i=s(45994),n=s(39187),a=s(62545);async function p(e){try{let{searchParams:r}=new URL(e.url),s=r.get("userId"),t=r.get("courseId"),o=r.get("assessmentType"),u=r.get("startDate"),i=r.get("endDate"),p=r.get("isPassed"),c=parseInt(r.get("limit")||"100"),l=parseInt(r.get("offset")||"0");console.log("Fetching results for courseId:",t);let d=`
      SELECT r.*, u.first_name, u.last_name
      FROM results r
      LEFT JOIN users u ON r.user_id = u.user_id
      WHERE 1=1
    `,x=[];s&&(d+=" AND r.user_id = ?",x.push(s)),t&&(d+=" AND r.course_id = ?",x.push(t)),o&&(d+=" AND r.assessment_type = ?",x.push(o)),u&&(d+=" AND r.result_date >= ?",x.push(u)),i&&(d+=" AND r.result_date <= ?",x.push(i)),null!=p&&(d+=" AND r.is_passed = ?",x.push("true"===p?1:0)),d+=" ORDER BY r.result_date DESC LIMIT ? OFFSET ?",x.push(c,l),console.log("Executing query:",d),console.log("With parameters:",x);let[m]=await a.A.query(d,x);console.log("Query results:",m);let _=`
      SELECT COUNT(*) as total
      FROM results r
      WHERE 1=1
    `,g=[...x];g.pop(),g.pop();let[q]=await a.A.query(_,g),v=q[0].total;return n.NextResponse.json({results:m,pagination:{total:v,limit:c,offset:l,hasMore:l+c<v}})}catch(e){return console.error(e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function c(e){try{let{user_id:r,course_id:s,enrollment_id:t,assessment_type:o,assessment_title:u,score:i,max_score:p,result_date:c,comments:l,is_passed:d}=await e.json();if(!r||!s||!t||!o||!u||!i||!c)return n.NextResponse.json({error:"Missing required fields"},{status:400});let[x]=await a.A.query(`INSERT INTO results (
        user_id, course_id, enrollment_id, assessment_type, assessment_title,
        score, max_score, result_date, comments, is_passed
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,[r,s,t,o,u,i,p||100,c,l||null,d||!1]);return n.NextResponse.json({id:x.insertId},{status:201})}catch(e){return console.error(e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/results/route",pathname:"/api/results",filename:"route",bundlePath:"app/api/results/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\results\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:m}=l;function _(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},62545:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60820),o=s(29021),u=s.n(o),i=s(33873),n=s.n(i);let a=t.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:u().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,5452,820],()=>s(65796));module.exports=t})();