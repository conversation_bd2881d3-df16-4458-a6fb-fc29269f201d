exports.id=2449,exports.ids=[2449],exports.modules={94168:(e,t,r)=>{Promise.resolve().then(r.bind(r,60189)),Promise.resolve().then(r.bind(r,22234)),Promise.resolve().then(r.bind(r,56814))},3896:(e,t,r)=>{Promise.resolve().then(r.bind(r,52401)),Promise.resolve().then(r.bind(r,58783)),Promise.resolve().then(r.bind(r,91542))},98167:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},51311:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},52401:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(45512),o=r(58009),n=r(79334),a=r(91542);let i=({children:e})=>{let t=(0,n.useRouter)(),[r,i]=(0,o.useState)(null);return((0,o.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/admin/auth/check",{method:"GET",credentials:"include",cache:"no-store"});if(!e.ok)throw Error("Not authenticated");let t=await e.json();console.log("Auth check response:",t),i(!0)}catch(e){console.error("Authentication check failed:",e),i(!1),a.o.error("Please log in to access the dashboard"),t.push("/")}})()},[t]),null===r)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"})}):r?(0,s.jsx)(s.Fragment,{children:e}):null}},58783:(e,t,r)=>{"use strict";r.d(t,{default:()=>C});var s=r(45512),o=r(58009),n=r(28531),a=r.n(n),i=r(38440),l=r(1422),d=r(57631),c=r(35603),u=r(43161),m=r(62673),h=r(28650),f=r(52975),b=r(39327),p=r(55817),v=r(53100),x=r(93346),g=r(6472),y=r(51255),w=r(35120),j=r(48320),N=r(79334),A=r(91542);let k=({onToggle:e})=>(0,s.jsxs)("div",{className:"md:hidden bg-white border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30",children:[(0,s.jsx)("button",{type:"button",onClick:e,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Toggle navigation menu",title:"Toggle navigation menu",children:(0,s.jsx)(j.A,{className:"w-6 h-6"})}),(0,s.jsx)("h1",{className:"font-semibold text-lg",children:"Brissp Dashboard"}),(0,s.jsx)("div",{className:"w-10"})," "]}),P=({isOpen:e=!1,onToggle:t})=>{let r=(0,N.useRouter)(),[n,j]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=()=>{j(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,o.useEffect)(()=>{let r=r=>{if(n&&e&&t){let e=document.getElementById("mobile-sidebar");e&&!e.contains(r.target)&&t()}};if(n&&e)return document.addEventListener("mousedown",r),()=>document.removeEventListener("mousedown",r)},[n,e,t]),(0,o.useEffect)(()=>(n&&e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[n,e]);let k=async()=>{try{(await fetch("/api/admin/auth/logout",{method:"POST",credentials:"include"})).ok?(A.o.success("Logged out successfully"),r.push("/")):A.o.error("Failed to log out")}catch(e){console.error("Logout error:",e),A.o.error("An error occurred during logout")}},P=()=>{n&&t&&t()},C=[{href:"/panel",icon:i.A,label:"Dashboard"},{href:"/applications",icon:l.A,label:"Course Applications"},{href:"/pitch-deck-applications",icon:d.A,label:"Pitch Deck Applications"},{href:"/internship-applications",icon:c.A,label:"Internship Applications"},{href:"/fyp-applications",icon:u.A,label:"FYP Applications"},{href:"/innovation-lab-applications",icon:m.A,label:"Innovation Lab Applications"},{href:"/results",icon:h.A,label:"Results"},{href:"/downloadables",icon:f.A,label:"Resources"},{href:"/courses",icon:b.A,label:"Courses"},{href:"/curriculum",icon:p.A,label:"Curriculum"},{href:"/notice-board",icon:v.A,label:"Notice Board"},{href:"/graduates",icon:x.A,label:"Graduates"},{href:"/password-management",icon:g.A,label:"Password Management"}];return(0,s.jsxs)(s.Fragment,{children:[n&&e&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:t}),(0,s.jsxs)("div",{id:"mobile-sidebar",className:`
          ${n?`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`:"fixed inset-y-0 left-0 w-64"}
          border-r bg-white h-screen flex flex-col
        `,children:[(0,s.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,s.jsx)(a(),{href:"/panel",className:"flex items-center space-x-2",onClick:P,children:(0,s.jsx)("span",{className:"font-semibold text-lg",children:"Brissp Dash"})}),n&&(0,s.jsx)("button",{type:"button",onClick:t,className:"p-2 rounded-lg hover:bg-gray-100 md:hidden","aria-label":"Close navigation menu",title:"Close navigation menu",children:(0,s.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,s.jsx)("nav",{className:"p-4 space-y-2 flex-grow overflow-y-auto",children:C.map(e=>{let t=e.icon;return(0,s.jsxs)(a(),{href:e.href,onClick:P,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200",children:[(0,s.jsx)(t,{className:"w-5 h-5 flex-shrink-0"}),(0,s.jsx)("span",{className:"truncate",children:e.label})]},e.href)})}),(0,s.jsx)("div",{className:"p-4 border-t mt-auto",children:(0,s.jsxs)("button",{type:"button",onClick:k,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full transition-colors duration-200",children:[(0,s.jsx)(w.A,{className:"w-5 h-5 flex-shrink-0"}),(0,s.jsx)("span",{children:"Logout"})]})})]})]})},C=({children:e})=>{let[t,r]=(0,o.useState)(!1),[n,a]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;a(e),e||r(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let i=()=>{r(!t)};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(k,{onToggle:i}),(0,s.jsx)(P,{isOpen:t,onToggle:i}),(0,s.jsx)("div",{className:`
        transition-all duration-300 ease-in-out
        ${n?"ml-0":"ml-64"}
      `,children:(0,s.jsx)("main",{className:"min-h-screen",children:e})})]})}},87021:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(45512),o=r(58009),n=r(12705),a=r(21643),i=r(59462);let l=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef(({className:e,variant:t,size:r,asChild:o=!1,...a},d)=>{let c=o?n.DX:"button";return(0,s.jsx)(c,{className:(0,i.cn)(l({variant:t,size:r,className:e})),ref:d,...a})});d.displayName="Button"},13393:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,BF:()=>l,Hj:()=>d,XI:()=>a,nA:()=>u,nd:()=>c});var s=r(45512),o=r(58009),n=r(59462);let a=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:r,className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})}));a.displayName="Table";let i=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("thead",{ref:r,className:(0,n.cn)("[&_tr]:border-b",e),...t}));i.displayName="TableHeader";let l=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tbody",{ref:r,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t}));l.displayName="TableBody",o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tfoot",{ref:r,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let d=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("tr",{ref:r,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));d.displayName="TableRow";let c=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("th",{ref:r,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableHead";let u=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("td",{ref:r,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));u.displayName="TableCell",o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("caption",{ref:r,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"},59462:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(82281),o=r(94805);function n(...e){return(0,o.QP)((0,s.$)(e))}},71975:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var s=r(62740),o=r(41583),n=r.n(o),a=r(76499),i=r.n(a);r(2012);var l=r(22234),d=r(60189),c=r(56814);let u={title:"Brissp Admin",description:"Brissp Academy Admin Dashboard"};function m({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsxs)("body",{className:`${n().variable} ${i().variable} antialiased`,children:[(0,s.jsx)(d.default,{children:(0,s.jsx)(l.default,{children:e})}),(0,s.jsx)(c.Toaster,{})]})})}},60189:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\AuthGuard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\AuthGuard.tsx","default")},22234:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\ResponsiveLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\ResponsiveLayout.tsx","default")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(88077);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},2012:()=>{}};