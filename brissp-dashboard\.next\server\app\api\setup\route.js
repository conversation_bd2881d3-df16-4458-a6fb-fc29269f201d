(()=>{var e={};e.id=8641,e.ids=[8641],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},10978:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>v,routeModule:()=>l,serverHooks:()=>q,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{GET:()=>c,POST:()=>p});var i=s(42706),o=s(28203),u=s(45994),n=s(39187),a=s(62545);async function p(){return d()}async function c(){return d()}async function d(){try{let e="<EMAIL>",r="admin123",[s]=await a.A.query("SELECT admin_id FROM admin_users WHERE email = ?",[e]);if(s.length>0)return await a.A.query("UPDATE admin_users SET password = ? WHERE email = ?",[r,e]),n.NextResponse.json({message:"Admin password updated successfully",credentials:{email:e,password:r}});return await a.A.query("INSERT INTO admin_users (email, password, full_name, is_super_admin) VALUES (?, ?, ?, ?)",[e,r,"Super Admin",!0]),n.NextResponse.json({message:"Admin user created successfully",credentials:{email:e,password:r}})}catch(e){return console.error("Error creating admin user:",e),n.NextResponse.json({error:"An error occurred while creating admin user: "+e.message},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/setup/route",pathname:"/api/setup",filename:"route",bundlePath:"app/api/setup/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\setup\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:q}=l;function v(){return(0,u.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},62545:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60820),i=s(29021),o=s.n(i),u=s(33873),n=s.n(u);let a=t.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,5452,820],()=>s(10978));module.exports=t})();