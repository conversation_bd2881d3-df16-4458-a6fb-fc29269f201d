(()=>{var e={};e.id=2502,e.ids=[2502],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},1779:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>_,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{POST:()=>p});var i=t(42706),n=t(28203),a=t(45994),o=t(39187),u=t(62545),d=t(45369);async function p(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return o.NextResponse.json({error:"Email and password are required"},{status:400});let[s]=await u.A.query("SELECT * FROM admin_users WHERE email = ? AND password = ?",[r,t]);if(0===s.length)return o.NextResponse.json({error:"Invalid credentials"},{status:401});let i=s[0];await u.A.query("UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE admin_id = ?",[i.admin_id]);let n=(0,d.HU)({admin_id:i.admin_id,email:i.email,full_name:i.full_name,is_super_admin:i.is_super_admin}),a=o.NextResponse.json({message:"Login successful",user:{admin_id:i.admin_id,email:i.email,full_name:i.full_name,is_super_admin:i.is_super_admin}});return a.cookies.set({name:"admin_token",value:n,httpOnly:!0,secure:!0,maxAge:86400,path:"/",sameSite:"lax"}),a}catch(e){return console.error("Login error:",e),o.NextResponse.json({error:"An error occurred during login"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/auth/login/route",pathname:"/api/admin/auth/login",filename:"route",bundlePath:"app/api/admin/auth/login/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\admin\\auth\\login\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:_}=c;function x(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},96487:()=>{},78335:()=>{},45369:(e,r,t)=>{"use strict";t.d(r,{B$:()=>x,Er:()=>c,HU:()=>l,dD:()=>v,nG:()=>_,nr:()=>m});var s=t(5486),i=t.n(s),n=t(43008),a=t.n(n),o=t(44512),u=t(39187),d=t(62545);let p=process.env.JWT_SECRET||"your-secret-key-change-this-in-production";async function c(e){return i().hash(e,10)}function l(e){return a().sign({id:e.admin_id,email:e.email,is_super_admin:e.is_super_admin},p,{expiresIn:"24h"})}function m(e){try{return a().verify(e,p)}catch{return null}}async function _(){let e=(0,o.UL)(),r=(await e).get("admin_token")?.value;if(!r)return null;let t=m(r);if(!t)return null;try{let[e]=await d.A.query("SELECT admin_id, email, full_name, is_super_admin FROM admin_users WHERE admin_id = ?",[t.id]);if(0===e.length)return null;return e[0]}catch(e){return console.error("Error fetching current admin:",e),null}}async function x(e){let r=e.cookies.get("admin_token")?.value;return r?m(r)?null:u.NextResponse.json({error:"Invalid token"},{status:401}):u.NextResponse.json({error:"Unauthorized"},{status:401})}async function v(e){let r=e.cookies.get("admin_token")?.value;if(!r)return u.NextResponse.json({error:"Unauthorized"},{status:401});let t=m(r);return t&&t.is_super_admin?null:u.NextResponse.json({error:"Forbidden: Requires super admin privileges"},{status:403})}},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(60820),i=t(29021),n=t.n(i),a=t(33873),o=t.n(a);let u=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:n().readFileSync(o().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820,8935],()=>t(1779));module.exports=s})();