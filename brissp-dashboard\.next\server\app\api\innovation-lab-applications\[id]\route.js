(()=>{var e={};e.id=4911,e.ids=[4911],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},54969:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{DELETE:()=>l,GET:()=>u,PUT:()=>c});var i=t(42706),o=t(28203),n=t(45994),a=t(39187),p=t(62545);async function u(e,{params:r}){try{let e=await r,t=parseInt(e.id);if(isNaN(t))return a.NextResponse.json({error:"Invalid application ID"},{status:400});let[s]=await p.A.query("SELECT * FROM innovation_lab_applications WHERE application_id = ?",[t]);if(0===s.length)return a.NextResponse.json({error:"Application not found"},{status:404});return a.NextResponse.json({application:s[0]})}catch(e){return console.error("Error fetching innovation lab application:",e),a.NextResponse.json({error:"An error occurred while fetching the application"},{status:500})}}async function c(e,{params:r}){try{let t=await r,s=parseInt(t.id),i=await e.json();if(isNaN(s))return a.NextResponse.json({error:"Invalid application ID"},{status:400});let o=`
      UPDATE innovation_lab_applications 
      SET status = ?, review_notes = ?, review_date = NOW(), updated_at = NOW()
      WHERE application_id = ?
    `,[n]=await p.A.query(o,[i.status,i.review_notes||null,s]);if(0===n.affectedRows)return a.NextResponse.json({error:"Application not found"},{status:404});return a.NextResponse.json({message:"Application updated successfully"})}catch(e){return console.error("Error updating innovation lab application:",e),a.NextResponse.json({error:"An error occurred while updating the application"},{status:500})}}async function l(e,{params:r}){try{let e=await r,t=parseInt(e.id);if(isNaN(t))return a.NextResponse.json({error:"Invalid application ID"},{status:400});let[s]=await p.A.query("DELETE FROM innovation_lab_applications WHERE application_id = ?",[t]);if(0===s.affectedRows)return a.NextResponse.json({error:"Application not found"},{status:404});return a.NextResponse.json({message:"Application deleted successfully"})}catch(e){return console.error("Error deleting innovation lab application:",e),a.NextResponse.json({error:"An error occurred while deleting the application"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/innovation-lab-applications/[id]/route",pathname:"/api/innovation-lab-applications/[id]",filename:"route",bundlePath:"app/api/innovation-lab-applications/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\innovation-lab-applications\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:f}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>p});var s=t(60820),i=t(29021),o=t.n(i),n=t(33873),a=t.n(n);let p=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(a().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(54969));module.exports=s})();