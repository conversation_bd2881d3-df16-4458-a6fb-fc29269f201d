(()=>{var e={};e.id=3648,e.ids=[3648],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},21295:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>E,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var a=t(42706),i=t(28203),o=t(45994),n=t(39187),u=t(62545);async function p(){try{let[e]=await u.A.query(`SELECT COUNT(*) as count FROM information_schema.tables
       WHERE table_schema = DATABASE() AND table_name = 'internship_applications'`);if(0===e[0].count){let[e]=await u.A.query(`SELECT table_name FROM information_schema.tables
         WHERE table_schema = DATABASE()
         ORDER BY table_name`);return n.NextResponse.json({error:"Table internship_applications does not exist",availableTables:e.map(e=>e.table_name),message:"Table not found"})}let[r]=await u.A.query("DESCRIBE internship_applications"),[t]=await u.A.query(`SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
       FROM information_schema.COLUMNS
       WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 'internship_applications'
       ORDER BY ORDINAL_POSITION`),s=null,a=0;try{let[e]=await u.A.query("SELECT COUNT(*) as count FROM internship_applications");if((a=e[0].count)>0){let[e]=await u.A.query("SELECT * FROM internship_applications LIMIT 1");s=e[0]||null}}catch(e){console.log("Error getting sample records:",e)}return n.NextResponse.json({tableExists:!0,recordCount:a,tableStructure:r,allColumns:t,sampleRecord:s,message:"Table structure retrieved successfully"})}catch(e){return console.error("Database debug error:",e),n.NextResponse.json({error:"Failed to get table structure",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/debug-table/route",pathname:"/api/debug-table",filename:"route",bundlePath:"app/api/debug-table/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\debug-table\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:E}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(60820),a=t(29021),i=t.n(a),o=t(33873),n=t.n(o);let u=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(21295));module.exports=s})();