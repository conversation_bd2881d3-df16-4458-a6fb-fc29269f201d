(()=>{var e={};e.id=3855,e.ids=[3855],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},12360:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var t=s(70260),n=s(28203),o=s(25155),a=s.n(o),i=s(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(r,d);let l=["",{children:["(dashboard)",{children:["courses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,45765)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/courses/page",pathname:"/courses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},78587:(e,r,s)=>{Promise.resolve().then(s.bind(s,45765))},31731:(e,r,s)=>{Promise.resolve().then(s.bind(s,95825))},95825:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>x});var t=s(45512),n=s(58009),o=s(87021),a=s(13393),i=s(93346);let d=(0,s(94825).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);var l=s(95758),c=s(79041),u=s(28531),p=s.n(u),h=s(77252);function x(){let[e,r]=(0,n.useState)([]),[s,u]=(0,n.useState)([]),[x,b]=(0,n.useState)("all"),m=async()=>{try{let e=await fetch("/api/courses"),s=await e.json();r(s)}catch(e){console.error("Error fetching courses:",e)}},f=async e=>{if(confirm("Are you sure you want to delete this course?"))try{(await fetch(`/api/courses/${e}`,{method:"DELETE"})).ok&&m()}catch(e){console.error("Error deleting course:",e)}},v=e=>"refresher"===e?(0,t.jsx)(h.E,{variant:"secondary",className:"bg-blue-100 text-blue-800",children:"Refresher"}):(0,t.jsx)(h.E,{variant:"outline",children:e});return(0,t.jsxs)("div",{className:"p-6 ",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Courses Management"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(p(),{href:"/courses/new",children:(0,t.jsxs)(o.$,{children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Add Regular Course"]})}),(0,t.jsx)(p(),{href:"/courses/refresher/new",children:(0,t.jsxs)(o.$,{variant:"outline",children:[(0,t.jsx)(d,{className:"w-4 h-4 mr-2"}),"Add Refresher Course"]})})]})]}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsxs)("nav",{className:"-mb-px flex space-x-8",children:[(0,t.jsxs)("button",{type:"button",onClick:()=>b("all"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"all"===x?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["All Courses (",e.length,")"]}),(0,t.jsxs)("button",{type:"button",onClick:()=>b("regular"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"regular"===x?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Regular Courses (",e.filter(e=>"refresher"!==e.program_type).length,")"]}),(0,t.jsxs)("button",{type:"button",onClick:()=>b("refresher"),className:`py-2 px-1 border-b-2 font-medium text-sm ${"refresher"===x?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Refresher Courses (",e.filter(e=>"refresher"===e.program_type).length,")"]})]})})}),(0,t.jsx)("div",{className:"bg-white rounded-lg border",children:(0,t.jsxs)(a.XI,{children:[(0,t.jsx)(a.A0,{children:(0,t.jsxs)(a.Hj,{children:[(0,t.jsx)(a.nd,{children:"Course Code"}),(0,t.jsx)(a.nd,{children:"Title"}),(0,t.jsx)(a.nd,{children:"Department"}),(0,t.jsx)(a.nd,{children:"Program Type"}),(0,t.jsx)(a.nd,{children:"Duration"}),(0,t.jsx)(a.nd,{children:"Price"}),(0,t.jsx)(a.nd,{children:"Actions"})]})}),(0,t.jsx)(a.BF,{children:s.map(e=>(0,t.jsxs)(a.Hj,{children:[(0,t.jsx)(a.nA,{children:e.course_code}),(0,t.jsx)(a.nA,{children:e.title}),(0,t.jsx)(a.nA,{children:e.department}),(0,t.jsx)(a.nA,{children:v(e.program_type)}),(0,t.jsxs)(a.nA,{children:[e.duration_months," months"]}),(0,t.jsxs)(a.nA,{children:["$",e.price]}),(0,t.jsxs)(a.nA,{className:"flex space-x-2",children:[(0,t.jsx)(p(),{href:"refresher"===e.program_type?`/courses/refresher/${e.course_id}/edit`:`/courses/${e.course_id}/edit`,children:(0,t.jsx)(o.$,{variant:"outline",size:"sm",children:(0,t.jsx)(l.A,{className:"w-4 h-4"})})}),(0,t.jsx)(o.$,{variant:"destructive",size:"sm",onClick:()=>f(e.course_id),children:(0,t.jsx)(c.A,{className:"w-4 h-4"})})]})]},e.course_id))})]})})]})}},77252:(e,r,s)=>{"use strict";s.d(r,{E:()=>i});var t=s(45512);s(58009);var n=s(21643),o=s(59462);let a=(0,n.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i({className:e,variant:r,...s}){return(0,t.jsx)("div",{className:(0,o.cn)(a({variant:r}),e),...s})}},45765:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\courses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\page.tsx","default")},95758:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(94825).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},79041:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,8403,7834,9267,4873,2449],()=>s(12360));module.exports=t})();