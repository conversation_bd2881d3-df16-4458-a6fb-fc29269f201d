(()=>{var e={};e.id=1075,e.ids=[1075],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},61420:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>_,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>f,POST:()=>m});var i=t(42706),a=t(28203),o=t(45994),l=t(39187),n=t(79748),u=t(33873),c=t(62545);class d{constructor(e="public/uploads"){this.uploadsDir=e}async findOrphanedFiles(){let e=[];try{let r=await this.getReferencedFileUrls(),t=new Set(r.map(e=>this.extractFilenameFromUrl(e)).filter(Boolean));for(let r of(await (0,n.readdir)(this.uploadsDir,{withFileTypes:!0})))if(r.isDirectory()){let s=(0,u.join)(this.uploadsDir,r.name);for(let i of(await (0,n.readdir)(s)))if(!t.has(i)){let t=(0,u.join)(s,i),a=await (0,n.stat)(t);e.push({path:t,filename:i,folder:r.name,size:a.size,lastModified:a.mtime})}}}catch(e){console.error("Error finding orphaned files:",e)}return e}async cleanupOrphanedFiles(e=7){let r={deletedFiles:[],errors:[],totalSize:0};try{let t=await this.findOrphanedFiles(),s=new Date;for(let i of(s.setDate(s.getDate()-e),t))if(i.lastModified<s)try{await (0,n.unlink)(i.path),r.deletedFiles.push(i.filename),r.totalSize+=i.size}catch(e){r.errors.push(`Failed to delete ${i.filename}: ${e}`)}}catch(e){r.errors.push(`Cleanup failed: ${e}`)}return r}async getReferencedFileUrls(){let e=[];try{let[r]=await c.A.query("SELECT image_url FROM courses WHERE image_url IS NOT NULL");e.push(...r.map(e=>e.image_url));let[t]=await c.A.query("SELECT file_url FROM downloadable_files WHERE file_url IS NOT NULL");e.push(...t.map(e=>e.file_url));let[s]=await c.A.query("SELECT certificate_url, image_url FROM graduates WHERE certificate_url IS NOT NULL OR image_url IS NOT NULL");s.forEach(r=>{r.certificate_url&&e.push(r.certificate_url),r.image_url&&e.push(r.image_url)})}catch(e){console.error("Error getting referenced file URLs:",e)}return e.filter(Boolean)}extractFilenameFromUrl(e){try{if(e.includes("/uploads/")){let r=e.split("/");return r[r.length-1]}let r=new URL(e).pathname;return r.substring(r.lastIndexOf("/")+1)}catch{return null}}async getStorageStats(){let e={totalFiles:0,totalSize:0,folderStats:{}};try{for(let r of(await (0,n.readdir)(this.uploadsDir,{withFileTypes:!0})))if(r.isDirectory()){let t=(0,u.join)(this.uploadsDir,r.name),s=await (0,n.readdir)(t),i=0;for(let e of s){let r=(0,u.join)(t,e),s=await (0,n.stat)(r);i+=s.size}e.folderStats[r.name]={files:s.length,size:i},e.totalFiles+=s.length,e.totalSize+=i}}catch(e){console.error("Error getting storage stats:",e)}return e}async migrateFileUrls(){let e={migrated:0,errors:[]};try{let[r]=await c.A.query("SELECT course_id, image_url FROM courses WHERE image_url IS NOT NULL");for(let t of r)if(this.isExternalUrl(t.image_url))try{let r=this.convertToLocalUrl(t.image_url);await c.A.query("UPDATE courses SET image_url = ? WHERE course_id = ?",[r,t.course_id]),e.migrated++}catch(r){e.errors.push(`Failed to migrate course ${t.course_id}: ${r}`)}let[t]=await c.A.query("SELECT file_id, file_url FROM downloadable_files WHERE file_url IS NOT NULL");for(let r of t)if(this.isExternalUrl(r.file_url))try{let t=this.convertToLocalUrl(r.file_url);await c.A.query("UPDATE downloadable_files SET file_url = ? WHERE file_id = ?",[t,r.file_id]),e.migrated++}catch(t){e.errors.push(`Failed to migrate file ${r.file_id}: ${t}`)}}catch(r){e.errors.push(`Migration failed: ${r}`)}return e}isExternalUrl(e){return e.includes("imagekit.io")||e.includes("cloudinary.com")||e.includes("amazonaws.com")||e.startsWith("http")&&!e.includes(process.env.NEXT_PUBLIC_DASHBOARD_URL||"localhost")}convertToLocalUrl(e){let r=this.extractFilenameFromUrl(e);if(!r)return e;let t="migrated";return e.includes("course")&&(t="courses"),e.includes("downloadable")&&(t="downloadable_files"),e.includes("graduate")&&(t="graduates"),`/uploads/${t}/${r}`}}let p=new d;async function f(e){try{let{searchParams:r}=new URL(e.url);switch(r.get("action")){case"stats":let t=await p.getStorageStats();return l.NextResponse.json(t);case"orphaned":let s=await p.findOrphanedFiles();return l.NextResponse.json(s);default:return l.NextResponse.json({error:"Invalid action. Use: stats, orphaned"},{status:400})}}catch(e){return console.error("File management error:",e),l.NextResponse.json({error:"Failed to perform file management operation"},{status:500})}}async function m(e){try{let{action:r,options:t}=await e.json();switch(r){case"cleanup":let s=t?.olderThanDays||7,i=await p.cleanupOrphanedFiles(s);return l.NextResponse.json(i);case"migrate":let a=await p.migrateFileUrls();return l.NextResponse.json(a);default:return l.NextResponse.json({error:"Invalid action. Use: cleanup, migrate"},{status:400})}}catch(e){return console.error("File management error:",e),l.NextResponse.json({error:"Failed to perform file management operation"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/files/manage/route",pathname:"/api/files/manage",filename:"route",bundlePath:"app/api/files/manage/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\files\\manage\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:x,serverHooks:_}=h;function w(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(60820),i=t(29021),a=t.n(i),o=t(33873),l=t.n(o);let n=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:a().readFileSync(l().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(61420));module.exports=s})();