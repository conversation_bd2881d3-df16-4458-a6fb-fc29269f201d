(()=>{var e={};e.id=1587,e.ids=[1587],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},3690:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>p});var o=t(42706),i=t(28203),u=t(45994),n=t(39187),a=t(62545);async function c(){try{let[e]=await a.A.query("SELECT * FROM curriculum");return n.NextResponse.json(e)}catch(e){return console.error(e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function p(e){try{let{course_id:r,week_number:t,topic:s,content:o,learning_objectives:i}=await e.json(),[u]=await a.A.query(`INSERT INTO curriculum (course_id, week_number, topic, content, learning_objectives)
       VALUES (?, ?, ?, ?, ?)`,[r,t,s,o,i]);return n.NextResponse.json({id:u.insertId},{status:201})}catch(e){return console.error(e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/carriculum/route",pathname:"/api/carriculum",filename:"route",bundlePath:"app/api/carriculum/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\carriculum\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:x,serverHooks:v}=d;function m(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60820),o=t(29021),i=t.n(o),u=t(33873),n=t.n(u);let a=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(3690));module.exports=s})();