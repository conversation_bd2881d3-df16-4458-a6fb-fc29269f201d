(()=>{var e={};e.id=327,e.ids=[327],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},76020:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var a=t(70260),s=t(28203),o=t(25155),i=t.n(o),d=t(67292),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(r,n);let l=["",{children:["(dashboard)",{children:["downloadables",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,84801)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\[id]\\edit\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/downloadables/[id]/edit/page",pathname:"/downloadables/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},54409:(e,r,t)=>{Promise.resolve().then(t.bind(t,84801))},96265:(e,r,t)=>{Promise.resolve().then(t.bind(t,97357))},97357:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(45512),s=t(58009),o=t(95826),i=t(79334);let d=()=>{let e=(0,i.useParams)(),r=e?.id,[t,d]=(0,s.useState)(null),[n,l]=(0,s.useState)(!0),[p,c]=(0,s.useState)(null);(0,s.useEffect)(()=>{r&&(async()=>{try{let e=await fetch(`/api/downloadables/${r}`);if(!e.ok)throw Error(`Error: ${e.status}`);let t=await e.json();d(t)}catch(e){console.error("Failed to fetch file:",e),c("Failed to load file data. Please try again.")}finally{l(!1)}})()},[r]);let u=async(e,t)=>{l(!0),c(null);try{let a=new FormData;a.append("file_name",e.file_name||""),a.append("file_url",e.file_url||""),a.append("description",e.description||""),a.append("course_id",e.course_id?.toString()||""),t&&a.append("file",t);let s=await fetch(`/api/downloadables/${r}`,{method:"PUT",body:a});if(!s.ok)throw Error(`Error: ${s.status}`);window.location.href="/downloadables"}catch(e){console.error("Failed to update file:",e),c("Failed to update file. Please try again."),l(!1)}};return n?(0,a.jsx)("div",{className:"p-6",children:"Loading..."}):p?(0,a.jsx)("div",{className:"p-6 text-red-500",children:p}):(0,a.jsxs)("div",{className:"p-6 ",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Edit Downloadable File"}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[t&&(0,a.jsx)(o.A,{initialData:t,onSubmit:u,isLoading:n}),!t&&!n&&(0,a.jsx)("div",{className:"text-red-500",children:"File not found"})]})]})}},84801:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\downloadables\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\[id]\\edit\\page.tsx","default")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[638,8403,7834,9267,4873,4708],()=>t(76020));module.exports=a})();