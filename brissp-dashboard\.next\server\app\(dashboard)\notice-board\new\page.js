(()=>{var e={};e.id=6515,e.ids=[6515],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},59114:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>c,routeModule:()=>u,tree:()=>p});var o=t(70260),s=t(28203),n=t(25155),i=t.n(n),a=t(67292),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p=["",{children:["(dashboard)",{children:["notice-board",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71135)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\new\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},u=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/notice-board/new/page",pathname:"/notice-board/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},28368:(e,r,t)=>{Promise.resolve().then(t.bind(t,71135))},63216:(e,r,t)=>{Promise.resolve().then(t.bind(t,77347))},77347:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var o=t(45512),s=t(58009),n=t(79334),i=t(35564);function a(){let e=(0,n.useRouter)(),[r,t]=(0,s.useState)(!1),a=async r=>{t(!0);try{(await fetch("/api/notices",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).ok&&e.push("/notice-board")}catch(e){console.error("Error creating notice:",e)}finally{t(!1)}};return(0,o.jsxs)("div",{className:"p-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Create New Notice"}),(0,o.jsx)("div",{className:"bg-white rounded-lg border p-6",children:(0,o.jsx)(i.A,{onSubmit:a,isLoading:r})})]})}},71135:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\notice-board\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\new\\page.tsx","default")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[638,8403,7834,9267,4873,1066],()=>t(59114));module.exports=o})();