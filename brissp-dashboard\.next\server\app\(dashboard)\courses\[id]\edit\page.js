(()=>{var e={};e.id=5548,e.ids=[5548],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},97184:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=t(70260),i=t(28203),n=t(25155),a=t.n(n),l=t(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let d=["",{children:["(dashboard)",{children:["courses",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,91290)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\[id]\\edit\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/courses/[id]/edit/page",pathname:"/courses/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},20048:(e,s,t)=>{Promise.resolve().then(t.bind(t,91290))},13424:(e,s,t)=>{Promise.resolve().then(t.bind(t,1342))},1342:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l});var r=t(45512),i=t(58009),n=t(77243),a=t(79334);let l=()=>{let e=(0,a.useParams)(),s=e?.id,[t,l]=(0,i.useState)(null),[o,d]=(0,i.useState)(!0);(0,i.useEffect)(()=>{s&&(async()=>{let e=await fetch(`/api/courses/${s}`);l(await e.json()),d(!1)})()},[s]);let c=async(e,t)=>{d(!0);let r=new FormData;r.append("data",JSON.stringify(e)),t&&r.append("image",t),await fetch(`/api/courses/${s}`,{method:"PUT",body:r}),window.location.href="/courses"};return o?(0,r.jsx)("div",{children:"Loading..."}):(0,r.jsxs)("div",{className:"p-6 ",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Edit Course"}),(0,r.jsx)("div",{className:"bg-white rounded-lg border p-6",children:t&&(0,r.jsx)(n.A,{initialData:t,onSubmit:c,isLoading:o})})]})}},77243:(e,s,t)=>{"use strict";t.d(s,{A:()=>d});var r=t(45512),i=t(58009),n=t(87021),a=t(25409),l=t(47920),o=t(27042);function d({initialData:e,onSubmit:s,isLoading:t}){let[d,c]=(0,i.useState)(e||{title:"",course_code:"",description:"",duration_months:0,price:0,department:"",category:"adults",program_type:"",num_lectures:0,skill_level:"beginner",languages:"",class_days:""}),[u,m]=(0,i.useState)(null),p=(0,l.hG)({extensions:[o.A],content:d.description,onUpdate:({editor:e})=>{c(s=>({...s,description:e.getHTML()}))}}),h=e=>{let{name:s,value:t}=e.target;c(e=>({...e,[s]:t}))};return(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s(d,u||void 0)},className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Course Title"}),(0,r.jsx)(a.p,{name:"title",value:d.title,onChange:h,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Course Code"}),(0,r.jsx)(a.p,{name:"course_code",value:d.course_code,onChange:h,required:!0})]}),(0,r.jsxs)("div",{className:"col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Description"}),(0,r.jsxs)("div",{className:"border rounded-md p-2",children:[(0,r.jsxs)("div",{className:"border-b p-2 mb-2 flex gap-2",children:[(0,r.jsx)("button",{type:"button",onClick:()=>p?.chain().focus().toggleBold().run(),className:`p-2 ${p?.isActive("bold")?"bg-gray-200":""}`,children:"Bold"}),(0,r.jsx)("button",{type:"button",onClick:()=>p?.chain().focus().toggleItalic().run(),className:`p-2 ${p?.isActive("italic")?"bg-gray-200":""}`,children:"Italic"}),(0,r.jsx)("button",{type:"button",onClick:()=>p?.chain().focus().toggleBulletList().run(),className:`p-2 ${p?.isActive("bulletList")?"bg-gray-200":""}`,children:"Bullet List"})]}),(0,r.jsx)(l.$Z,{editor:p,className:"min-h-[200px] prose max-w-none"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Duration (months)"}),(0,r.jsx)(a.p,{type:"number",name:"duration_months",value:d.duration_months,onChange:h,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Price"}),(0,r.jsx)(a.p,{type:"number",name:"price",value:d.price,onChange:h,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Department"}),(0,r.jsxs)("select",{title:"department",name:"department",value:d.department,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,r.jsx)("option",{value:"beginner",children:"Auto Engineering"}),(0,r.jsx)("option",{value:"intermediate",children:"Computer Science"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Category"}),(0,r.jsxs)("select",{title:"category",name:"category",value:d.category,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,r.jsx)("option",{value:"adults",children:"Adults"}),(0,r.jsx)("option",{value:"kids",children:"Kids"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Program Type"}),(0,r.jsxs)("select",{title:"program_type",name:"program_type",value:d.program_type,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,r.jsx)("option",{value:"",children:"Select program type"}),(0,r.jsx)("option",{value:"fulltime",children:"Fulltime"}),(0,r.jsx)("option",{value:"adults-online",children:"Adults online"}),(0,r.jsx)("option",{value:"kids-online",children:"Kids online"}),(0,r.jsx)("option",{value:"distance",children:"Distance"}),(0,r.jsx)("option",{value:"part-time",children:"Part time"}),(0,r.jsx)("option",{value:"weekend",children:"Weekend"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:'For refresher courses, use the dedicated "Add Refresher Course" button'})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Number of Lectures"}),(0,r.jsx)(a.p,{type:"number",name:"num_lectures",value:d.num_lectures,onChange:h,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Skill Level"}),(0,r.jsxs)("select",{title:"skill_level",name:"skill_level",value:d.skill_level,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,r.jsx)("option",{value:"all",children:"All"}),(0,r.jsx)("option",{value:"beginner",children:"Beginner"}),(0,r.jsx)("option",{value:"intermediate",children:"Intermediate"}),(0,r.jsx)("option",{value:"advanced",children:"Advanced"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Languages"}),(0,r.jsx)(a.p,{name:"languages",value:d.languages,onChange:h,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Class Days"}),(0,r.jsx)(a.p,{name:"class_days",value:d.class_days,onChange:h,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Upload Image"}),(0,r.jsx)(a.p,{type:"file",accept:"image/*",onChange:e=>{m(e.target.files?.[0]||null)}})]})]}),(0,r.jsx)("div",{className:"flex justify-end space-x-4",children:(0,r.jsx)(n.$,{type:"submit",disabled:t,children:t?"Saving...":"Save Course"})})]})}},91290:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\courses\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\[id]\\edit\\page.tsx","default")}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,8403,7834,9267,4873,9847,8273],()=>t(97184));module.exports=r})();