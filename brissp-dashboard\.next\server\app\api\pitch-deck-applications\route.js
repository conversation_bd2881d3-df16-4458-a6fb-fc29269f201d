(()=>{var e={};e.id=8270,e.ids=[8270],e.modules={28303:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=28303,e.exports=t},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},69827:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>u,PUT:()=>d});var i=r(42706),a=r(28203),o=r(45994),n=r(39187),p=r(62545);async function c(e){try{let{searchParams:t}=new URL(e.url),r=t.get("status"),s=parseInt(t.get("page")||"1"),i=parseInt(t.get("limit")||"10"),a=(s-1)*i,o=`
      SELECT * FROM pitch_deck_applications
    `,c=[];r&&(o+=" WHERE status = ?",c.push(r)),o+=" ORDER BY application_date DESC LIMIT ? OFFSET ?",c.push(i,a);let[u]=await p.A.query(o,c),d="SELECT COUNT(*) as total FROM pitch_deck_applications",l=[];r&&(d+=" WHERE status = ?",l.push(r));let[x]=await p.A.query(d,l),_=x[0].total;return n.NextResponse.json({applications:u,pagination:{page:s,limit:i,total:_,totalPages:Math.ceil(_/i)}})}catch(e){return console.error("Error fetching pitch deck applications:",e),n.NextResponse.json({error:"Failed to fetch applications"},{status:500})}}async function u(e){try{let{applicant_name:t,email:r,phone:s,company_name:i,industry:a,funding_stage:o,funding_amount:c,business_description:u,target_audience:d,current_traction:l,team_size:x,previous_funding:_,pitch_deadline:h,specific_requirements:f,preferred_start_date:g,budget_range:m,referral_source:q}=await e.json();if(!t||!r||!s||!o||!u||!m)return n.NextResponse.json({error:"Missing required fields"},{status:400});let[v]=await p.A.query(`INSERT INTO pitch_deck_applications (
        applicant_name, email, phone, company_name, industry, funding_stage,
        funding_amount, business_description, target_audience, current_traction,
        team_size, previous_funding, pitch_deadline, specific_requirements,
        preferred_start_date, budget_range, referral_source
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,[t,r,s,i,a,o,c,u,d,l,x,_,h,f,g,m,q]);return n.NextResponse.json({message:"Application submitted successfully",application_id:v.insertId},{status:201})}catch(e){return console.error("Error creating pitch deck application:",e),n.NextResponse.json({error:"Failed to submit application"},{status:500})}}async function d(e){try{let{application_id:t,status:r,review_notes:s,assigned_consultant:i}=await e.json();if(!t||!r)return n.NextResponse.json({error:"Application ID and status are required"},{status:400});let[a]=await p.A.query(`UPDATE pitch_deck_applications 
       SET status = ?, review_notes = ?, assigned_consultant = ?, review_date = NOW()
       WHERE application_id = ?`,[r,s,i,t]);if(0===a.affectedRows)return n.NextResponse.json({error:"Application not found"},{status:404});return n.NextResponse.json({message:"Application updated successfully"})}catch(e){return console.error("Error updating pitch deck application:",e),n.NextResponse.json({error:"Failed to update application"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/pitch-deck-applications/route",pathname:"/api/pitch-deck-applications",filename:"route",bundlePath:"app/api/pitch-deck-applications/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\pitch-deck-applications\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:_,serverHooks:h}=l;function f(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:_})}},96487:()=>{},78335:()=>{},62545:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(60820),i=r(29021),a=r.n(i),o=r(33873),n=r.n(o);let p=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:a().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452,820],()=>r(69827));module.exports=s})();