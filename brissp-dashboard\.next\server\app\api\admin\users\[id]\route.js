(()=>{var e={};e.id=9452,e.ids=[9452],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},35465:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>_,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var t={};s.r(t),s.d(t,{DELETE:()=>p});var i=s(42706),n=s(28203),u=s(45994),a=s(39187),o=s(62545),d=s(45369);async function p(e){try{let r=await (0,d.dD)(e);if(r)return r;let s=e.url.split("/").pop(),{full_name:t,email:i,is_super_admin:n,password:u}=await e.json(),[p]=await o.A.query("SELECT admin_id, is_super_admin FROM admin_users WHERE admin_id = ?",[s]);if(0===p.length)return a.NextResponse.json({error:"User not found"},{status:404});if(p[0].is_super_admin&&!1===n){let[e]=await o.A.query("SELECT COUNT(*) as count FROM admin_users WHERE is_super_admin = true");if(e[0].count<=1)return a.NextResponse.json({error:"Cannot remove super admin status from the last super admin"},{status:400})}if(i){let[e]=await o.A.query("SELECT admin_id FROM admin_users WHERE email = ? AND admin_id != ?",[i,s]);if(e.length>0)return a.NextResponse.json({error:"Email is already in use by another admin"},{status:400})}let c="UPDATE admin_users SET ",l=[];if(t&&(c+="full_name = ?, ",l.push(t)),i&&(c+="email = ?, ",l.push(i)),void 0!==n&&(c+="is_super_admin = ?, ",l.push(n)),u){let e=await (0,d.Er)(u);c+="password = ?, ",l.push(e)}if(c=c.slice(0,-2)+" WHERE admin_id = ?",l.push(s),l.length>1)return await o.A.query(c,l),a.NextResponse.json({message:"Admin user updated successfully"});return a.NextResponse.json({message:"No fields to update"})}catch(e){return console.error("Update admin user error:",e),a.NextResponse.json({error:"An error occurred while updating admin user"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/users/[id]/route",pathname:"/api/admin/users/[id]",filename:"route",bundlePath:"app/api/admin/users/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\admin\\users\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:x}=c;function _(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},96487:()=>{},78335:()=>{},45369:(e,r,s)=>{"use strict";s.d(r,{B$:()=>_,Er:()=>c,HU:()=>l,dD:()=>f,nG:()=>x,nr:()=>m});var t=s(5486),i=s.n(t),n=s(43008),u=s.n(n),a=s(44512),o=s(39187),d=s(62545);let p=process.env.JWT_SECRET||"your-secret-key-change-this-in-production";async function c(e){return i().hash(e,10)}function l(e){return u().sign({id:e.admin_id,email:e.email,is_super_admin:e.is_super_admin},p,{expiresIn:"24h"})}function m(e){try{return u().verify(e,p)}catch{return null}}async function x(){let e=(0,a.UL)(),r=(await e).get("admin_token")?.value;if(!r)return null;let s=m(r);if(!s)return null;try{let[e]=await d.A.query("SELECT admin_id, email, full_name, is_super_admin FROM admin_users WHERE admin_id = ?",[s.id]);if(0===e.length)return null;return e[0]}catch(e){return console.error("Error fetching current admin:",e),null}}async function _(e){let r=e.cookies.get("admin_token")?.value;return r?m(r)?null:o.NextResponse.json({error:"Invalid token"},{status:401}):o.NextResponse.json({error:"Unauthorized"},{status:401})}async function f(e){let r=e.cookies.get("admin_token")?.value;if(!r)return o.NextResponse.json({error:"Unauthorized"},{status:401});let s=m(r);return s&&s.is_super_admin?null:o.NextResponse.json({error:"Forbidden: Requires super admin privileges"},{status:403})}},62545:(e,r,s)=>{"use strict";s.d(r,{A:()=>o});var t=s(60820),i=s(29021),n=s.n(i),u=s(33873),a=s.n(u);let o=t.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:n().readFileSync(a().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,5452,820,8935],()=>s(35465));module.exports=t})();