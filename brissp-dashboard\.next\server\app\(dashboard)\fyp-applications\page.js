(()=>{var e={};e.id=4802,e.ids=[4802],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},60202:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(70260),r=s(28203),i=s(25155),n=s.n(i),l=s(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o=["",{children:["(dashboard)",{children:["fyp-applications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,46950)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\fyp-applications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\fyp-applications\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/fyp-applications/page",pathname:"/fyp-applications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},65998:(e,t,s)=>{Promise.resolve().then(s.bind(s,46950))},29046:(e,t,s)=>{Promise.resolve().then(s.bind(s,90098))},90098:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>b});var a=s(45512),r=s(58009),i=s(87021),n=s(13393),l=s(77252),d=s(97643),o=s(54069),c=s(25409),p=s(43161),x=s(55469),u=s(39327),m=s(25637),f=s(5899),h=s(82446),j=s(79041),g=s(28531),y=s.n(g);function b(){let[e,t]=(0,r.useState)([]),[s,g]=(0,r.useState)(!0),[b,v]=(0,r.useState)("all"),[w,N]=(0,r.useState)("all"),[P,A]=(0,r.useState)("all"),[C,k]=(0,r.useState)(""),[R,_]=(0,r.useState)({page:1,limit:10,total:0,totalPages:0}),[L,S]=(0,r.useState)({total:0,pending:0,inProgress:0,completed:0}),q=async()=>{try{g(!0);let e=new URLSearchParams({page:R.page.toString(),limit:R.limit.toString()});b&&"all"!==b&&e.append("status",b),w&&"all"!==w&&e.append("type",w),P&&"all"!==P&&e.append("university",P);let s=await fetch(`/api/fyp-applications?${e}`),a=await s.json();t(a.applications),_(e=>({...e,total:a.pagination.total,totalPages:a.pagination.totalPages}))}catch(e){console.error("Error fetching applications:",e)}finally{g(!1)}},B=async()=>{try{let[e,t,s,a]=await Promise.all([fetch("/api/fyp-applications"),fetch("/api/fyp-applications?status=pending"),fetch("/api/fyp-applications?status=in-progress"),fetch("/api/fyp-applications?status=completed")]),[r,i,n,l]=await Promise.all([e.json(),t.json(),s.json(),a.json()]);S({total:r.pagination?.total||0,pending:i.pagination?.total||0,inProgress:n.pagination?.total||0,completed:l.pagination?.total||0})}catch(e){console.error("Error fetching stats:",e)}},Z=async e=>{if(confirm("Are you sure you want to delete this application?"))try{(await fetch(`/api/fyp-applications/${e}`,{method:"DELETE"})).ok&&(q(),B())}catch(e){console.error("Error deleting application:",e)}},E=e=>{let t={pending:{variant:"secondary",label:"Pending"},reviewed:{variant:"outline",label:"Reviewed"},accepted:{variant:"default",label:"Accepted"},rejected:{variant:"destructive",label:"Rejected"},"in-progress":{variant:"default",label:"In Progress"},completed:{variant:"default",label:"Completed"}},s=t[e]||t.pending;return(0,a.jsx)(l.E,{variant:s.variant,children:s.label})},D=e=>(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{research:"bg-blue-100 text-blue-800",development:"bg-green-100 text-green-800",analysis:"bg-purple-100 text-purple-800",design:"bg-pink-100 text-pink-800",other:"bg-gray-100 text-gray-800"}[e]||"bg-gray-100 text-gray-800"}`,children:e.charAt(0).toUpperCase()+e.slice(1)}),M=e.filter(e=>e.student_name&&e.student_name.toLowerCase().includes(C.toLowerCase())||e.email&&e.email.toLowerCase().includes(C.toLowerCase())||e.university&&e.university.toLowerCase().includes(C.toLowerCase())||e.project_title&&e.project_title.toLowerCase().includes(C.toLowerCase()));return(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Final Year Project Applications"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage final year project support applications"})]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(d.ZB,{className:"text-sm font-medium",children:"Total Applications"}),(0,a.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:L.total})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(d.ZB,{className:"text-sm font-medium",children:"Pending Review"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:L.pending})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(d.ZB,{className:"text-sm font-medium",children:"In Progress"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:L.inProgress})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(d.ZB,{className:"text-sm font-medium",children:"Completed"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold",children:L.completed})})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Filters"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(f.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,a.jsx)(c.p,{placeholder:"Search by name, email, university, or project title...",value:C,onChange:e=>k(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)(o.l6,{value:b,onValueChange:v,children:[(0,a.jsx)(o.bq,{className:"w-48",children:(0,a.jsx)(o.yv,{placeholder:"Filter by status"})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"all",children:"All Statuses"}),(0,a.jsx)(o.eb,{value:"pending",children:"Pending"}),(0,a.jsx)(o.eb,{value:"reviewed",children:"Reviewed"}),(0,a.jsx)(o.eb,{value:"accepted",children:"Accepted"}),(0,a.jsx)(o.eb,{value:"rejected",children:"Rejected"}),(0,a.jsx)(o.eb,{value:"in-progress",children:"In Progress"}),(0,a.jsx)(o.eb,{value:"completed",children:"Completed"})]})]}),(0,a.jsxs)(o.l6,{value:w,onValueChange:N,children:[(0,a.jsx)(o.bq,{className:"w-48",children:(0,a.jsx)(o.yv,{placeholder:"Filter by type"})}),(0,a.jsxs)(o.gC,{children:[(0,a.jsx)(o.eb,{value:"all",children:"All Types"}),(0,a.jsx)(o.eb,{value:"research",children:"Research"}),(0,a.jsx)(o.eb,{value:"development",children:"Development"}),(0,a.jsx)(o.eb,{value:"analysis",children:"Analysis"}),(0,a.jsx)(o.eb,{value:"design",children:"Design"}),(0,a.jsx)(o.eb,{value:"other",children:"Other"})]})]})]})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsxs)(d.ZB,{children:["Applications (",M.length,")"]})}),(0,a.jsx)(d.Wu,{children:s?(0,a.jsx)("div",{className:"text-center py-8",children:"Loading applications..."}):(0,a.jsxs)(n.XI,{children:[(0,a.jsx)(n.A0,{children:(0,a.jsxs)(n.Hj,{children:[(0,a.jsx)(n.nd,{children:"Student"}),(0,a.jsx)(n.nd,{children:"University"}),(0,a.jsx)(n.nd,{children:"Project Title"}),(0,a.jsx)(n.nd,{children:"Project Type"}),(0,a.jsx)(n.nd,{children:"Status"}),(0,a.jsx)(n.nd,{children:"Applied Date"}),(0,a.jsx)(n.nd,{children:"Actions"})]})}),(0,a.jsx)(n.BF,{children:M.map(e=>(0,a.jsxs)(n.Hj,{children:[(0,a.jsx)(n.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.student_name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,a.jsx)(n.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.university}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.course_of_study})]})}),(0,a.jsx)(n.nA,{children:(0,a.jsx)("div",{className:"max-w-xs truncate",title:e.project_title||"No title provided",children:e.project_title||"No title provided"})}),(0,a.jsx)(n.nA,{children:D(e.project_type)}),(0,a.jsx)(n.nA,{children:E(e.status)}),(0,a.jsx)(n.nA,{children:new Date(e.application_date).toLocaleDateString()}),(0,a.jsx)(n.nA,{children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(y(),{href:`/fyp-applications/${e.application_id}`,children:(0,a.jsx)(i.$,{variant:"outline",size:"sm",children:(0,a.jsx)(h.A,{className:"w-4 h-4"})})}),(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>Z(e.application_id),children:(0,a.jsx)(j.A,{className:"w-4 h-4"})})]})})]},e.application_id))})]})})]}),R.totalPages>1&&(0,a.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,a.jsx)(i.$,{variant:"outline",onClick:()=>_(e=>({...e,page:Math.max(1,e.page-1)})),disabled:1===R.page,children:"Previous"}),(0,a.jsxs)("span",{className:"flex items-center px-4",children:["Page ",R.page," of ",R.totalPages]}),(0,a.jsx)(i.$,{variant:"outline",onClick:()=>_(e=>({...e,page:Math.min(e.totalPages,e.page+1)})),disabled:R.page===R.totalPages,children:"Next"})]})]})}},77252:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(45512);s(58009);var r=s(21643),i=s(59462);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...s})}},97643:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>l,wL:()=>p});var a=s(45512),r=s(58009),i=s(59462);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let l=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let o=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let p=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));p.displayName="CardFooter"},25409:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(45512),r=s(58009),i=s(59462);let n=r.forwardRef(({className:e,type:t,...s},r)=>(0,a.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...s}));n.displayName="Input"},54069:(e,t,s)=>{"use strict";s.d(t,{bq:()=>x,eb:()=>h,gC:()=>f,l6:()=>c,yv:()=>p});var a=s(45512),r=s(58009),i=s(96096),n=s(7833),l=s(36624),d=s(24999),o=s(59462);let c=i.bL;i.YJ;let p=i.WT,x=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=i.l9.displayName;let u=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})}));u.displayName=i.PP.displayName;let m=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));m.displayName=i.wn.displayName;let f=r.forwardRef(({className:e,children:t,position:s="popper",...r},n)=>(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,a.jsx)(u,{}),(0,a.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,a.jsx)(m,{})]})}));f.displayName=i.UC.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let h=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(i.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(i.p4,{children:t})]}));h.displayName=i.q7.displayName,r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(i.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},46950:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\fyp-applications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\fyp-applications\\page.tsx","default")},25637:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(94825).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},55469:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(94825).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},82446:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(94825).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5899:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(94825).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},79041:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,8403,7834,9267,4873,6562,3015,2449],()=>s(60202));module.exports=a})();