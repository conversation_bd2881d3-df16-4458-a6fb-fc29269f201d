(()=>{var e={};e.id=3734,e.ids=[3734],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},31715:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>E,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>R});var t={};s.r(t),s.d(t,{DELETE:()=>c,GET:()=>l,PUT:()=>p});var o=s(42706),u=s(28203),n=s(45994),i=s(39187),a=s(62545);async function p(e){try{let r=e.url.split("/").pop();if(!r)return i.NextResponse.json({error:"Result ID not found in URL"},{status:400});let{assessment_type:s,assessment_title:t,score:o,max_score:u,result_date:n,comments:p,is_passed:l}=await e.json();if(!s||!t||void 0===o||!u||!n)return i.NextResponse.json({error:"Missing required fields"},{status:400});let c=new Date(n).toISOString().slice(0,19).replace("T"," "),d=parseFloat(o),x=parseFloat(u);if(isNaN(d)||isNaN(x))return i.NextResponse.json({error:"Score and max score must be valid numbers"},{status:400});let[R]=await a.A.query("SELECT result_id FROM results WHERE result_id = ?",[r]);if(!R[0])return i.NextResponse.json({error:"Result not found"},{status:404});await a.A.query(`UPDATE results 
       SET assessment_type = ?, 
           assessment_title = ?, 
           score = ?, 
           max_score = ?, 
           result_date = ?, 
           comments = ?, 
           is_passed = ?
       WHERE result_id = ?`,[s,t,d,x,c,p||null,l||!1,r]);let[E]=await a.A.query(`SELECT r.*, u.first_name, u.last_name
       FROM results r
       LEFT JOIN users u ON r.user_id = u.user_id
       WHERE r.result_id = ?`,[r]);return i.NextResponse.json({message:"Result updated successfully",result:E[0]})}catch(e){return console.error("Error updating result:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function l(e){try{let r=e.url.split("/").pop();if(!r)return i.NextResponse.json({error:"Result ID not found in URL"},{status:400});let[s]=await a.A.query("SELECT * FROM results WHERE result_id = ?",[r]);if(0===s.length)return i.NextResponse.json({error:"Result not found"},{status:404});return i.NextResponse.json(s[0])}catch(e){return console.error("Error fetching result:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function c(e){try{let r=e.url.split("/").pop();if(!r)return i.NextResponse.json({error:"Result ID not found in URL"},{status:400});let[s]=await a.A.query("DELETE FROM results WHERE result_id = ?",[r]);if(0===s.affectedRows)return i.NextResponse.json({error:"Result not found"},{status:404});return i.NextResponse.json({message:"Result deleted successfully"})}catch(e){return console.error("Error deleting result:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/results/[id]/route",pathname:"/api/results/[id]",filename:"route",bundlePath:"app/api/results/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\results\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:R,serverHooks:E}=d;function f(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:R})}},96487:()=>{},78335:()=>{},62545:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60820),o=s(29021),u=s.n(o),n=s(33873),i=s.n(n);let a=t.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:u().readFileSync(i().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,5452,820],()=>s(31715));module.exports=t})();