/* eslint-disable @typescript-eslint/no-unused-vars */
import { NextRequest, NextResponse } from 'next/server';
import { fileStorage, getValidationOptions } from '@/lib/file-storage';

export async function POST(request: NextRequest) {
    try {
        const data = await request.formData();
        const file = data.get('file') as File;
        const folder = (data.get('folder') as string) || 'general';
        const category = (data.get('category') as string) || 'all';

        if (!file) {
            return NextResponse.json(
                { error: 'No file uploaded' },
                { status: 400 }
            );
        }

        // Get validation options based on category
        const validationOptions = getValidationOptions(
            category as 'images' | 'documents' | 'all',
            10 * 1024 * 1024 // 10MB max
        );

        // Upload file to local storage
        const result = await fileStorage.uploadFile(file, folder);

        return NextResponse.json({
            url: result.url,
            filename: result.filename,
            originalName: result.originalName,
            size: result.size,
            mimeType: result.mimeType,
            // Legacy compatibility
            fileId: result.filename,
            thumbnailUrl: result.url
        });
    } catch (error) {
        console.error('Upload error:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Error uploading file' },
            { status: 500 }
        );
    }
}
