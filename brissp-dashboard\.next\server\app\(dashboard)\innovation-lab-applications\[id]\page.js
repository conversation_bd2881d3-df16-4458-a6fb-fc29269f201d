(()=>{var e={};e.id=7900,e.ids=[7900],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},63078:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>r.a,__next_app__:()=>o,pages:()=>m,routeModule:()=>x,tree:()=>d});var t=a(70260),i=a(28203),l=a(25155),r=a.n(l),n=a(67292),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["(dashboard)",{children:["innovation-lab-applications",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,74228)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\innovation-lab-applications\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],m=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\innovation-lab-applications\\[id]\\page.tsx"],o={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/innovation-lab-applications/[id]/page",pathname:"/innovation-lab-applications/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},76453:(e,s,a)=>{Promise.resolve().then(a.bind(a,74228))},12901:(e,s,a)=>{Promise.resolve().then(a.bind(a,93037))},93037:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>f});var t=a(45512),i=a(58009),l=a(79334),r=a(87021),n=a(97643),c=a(77252),d=a(48859),m=a(54069),o=a(71894),x=a(42092),p=a(94825);let h=(0,p.A)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),j=(0,p.A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);var u=a(25890),v=a(20111),b=a(62673),g=a(28790),N=a(71398),y=a(73325);function f(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),[a,p]=(0,i.useState)(null),[f,_]=(0,i.useState)(!0),[w,k]=(0,i.useState)(!1),[A,P]=(0,i.useState)(""),[q,R]=(0,i.useState)(""),D=async()=>{try{let s=await fetch(`/api/innovation-lab-applications/${e?.id}`),a=await s.json();s.ok&&(p(a.application),P(a.application.status),R(a.application.review_notes||""))}catch(e){console.error("Error fetching application:",e)}finally{_(!1)}},M=async()=>{if(a){k(!0);try{(await fetch(`/api/innovation-lab-applications/${a.application_id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:A,review_notes:q})})).ok&&(D(),alert("Application updated successfully!"))}catch(e){console.error("Error updating application:",e),alert("Error updating application")}finally{k(!1)}}};return f?(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center py-8",children:"Loading application details..."})}):a?(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Innovation Lab Application Details"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Application #",a.application_id]})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(e=>{let s={pending:{variant:"secondary",label:"Pending",className:"bg-yellow-100 text-yellow-800"},reviewed:{variant:"outline",label:"Reviewed",className:"bg-blue-100 text-blue-800"},accepted:{variant:"default",label:"Accepted",className:"bg-green-100 text-green-800"},rejected:{variant:"destructive",label:"Rejected",className:"bg-red-100 text-red-800"},"in-progress":{variant:"default",label:"In Progress",className:"bg-purple-100 text-purple-800"},completed:{variant:"default",label:"Completed",className:"bg-emerald-100 text-emerald-800"}},a=s[e]||s.pending;return(0,t.jsx)(c.E,{className:a.className,children:a.label})})(a.status)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(x.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Applicant Information"})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,t.jsx)("p",{className:"text-sm",children:a.applicant_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,t.jsx)("p",{className:"text-sm",children:a.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Phone"}),(0,t.jsx)("p",{className:"text-sm",children:a.phone})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"University/Organization"}),(0,t.jsx)("p",{className:"text-sm",children:a.university_organization})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(h,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Project Details"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Project Title"}),(0,t.jsx)("p",{className:"text-sm font-semibold",children:a.project_title})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Innovation Type"}),(0,t.jsx)("p",{className:"text-sm capitalize",children:a.innovation_type.replace("-","/")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Development Stage"}),(0,t.jsx)("p",{className:"text-sm capitalize",children:a.development_stage.replace("-"," ")})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Project Duration"}),(0,t.jsx)("p",{className:"text-sm",children:a.project_duration||"Not specified"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Project Description"}),(0,t.jsx)("p",{className:"text-sm mt-1 p-3 bg-gray-50 rounded",children:a.idea_description})]}),a.problem_statement&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Problem Statement"}),(0,t.jsx)("p",{className:"text-sm mt-1 p-3 bg-gray-50 rounded",children:a.problem_statement})]}),a.solution_approach&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Solution Approach"}),(0,t.jsx)("p",{className:"text-sm mt-1 p-3 bg-gray-50 rounded",children:a.solution_approach})]}),a.target_market&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Target Market"}),(0,t.jsx)("p",{className:"text-sm mt-1 p-3 bg-gray-50 rounded",children:a.target_market})]})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(j,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Technical Requirements"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[a.technologies_involved&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Technologies Involved"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.technologies_involved})]}),a.software_needs&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Software Requirements"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.software_needs})]}),a.hardware_needs&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Hardware Requirements"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.hardware_needs})]}),a.technical_expertise_required&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Technical Expertise Required"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.technical_expertise_required})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(u.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Goals and Timeline"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Project Deadline"}),(0,t.jsx)("p",{className:"text-sm",children:a.project_deadline?new Date(a.project_deadline).toLocaleDateString():"Not specified"})]})}),a.expected_outcomes&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Expected Outcomes"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.expected_outcomes})]}),a.success_metrics&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Success Metrics"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.success_metrics})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(v.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Team Information"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Team Size"}),(0,t.jsx)("p",{className:"text-sm",children:a.team_size||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Collaboration Interest"}),(0,t.jsx)("p",{className:"text-sm",children:a.collaboration_interests?"Yes":"No"})]})]}),a.team_member_roles&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Team Member Roles"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.team_member_roles})]}),a.team_experience&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Team Experience"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.team_experience})]}),a.previous_projects&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Previous Projects"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.previous_projects})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(b.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Resource Requirements"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[a.lab_access_needs&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Lab Access Needs"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.lab_access_needs})]}),a.equipment_requirements&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Equipment Requirements"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.equipment_requirements})]}),a.funding_requirements&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Funding Requirements"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.funding_requirements})]}),a.mentorship_needs&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Mentorship Needs"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.mentorship_needs})]}),a.additional_notes&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Additional Notes"}),(0,t.jsx)("p",{className:"text-sm mt-1 p-3 bg-gray-50 rounded",children:a.additional_notes})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Application Management"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Status"}),(0,t.jsxs)(m.l6,{value:A,onValueChange:P,children:[(0,t.jsx)(m.bq,{className:"mt-1",children:(0,t.jsx)(m.yv,{})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"pending",children:"Pending"}),(0,t.jsx)(m.eb,{value:"reviewed",children:"Reviewed"}),(0,t.jsx)(m.eb,{value:"accepted",children:"Accepted"}),(0,t.jsx)(m.eb,{value:"rejected",children:"Rejected"}),(0,t.jsx)(m.eb,{value:"in-progress",children:"In Progress"}),(0,t.jsx)(m.eb,{value:"completed",children:"Completed"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Review Notes"}),(0,t.jsx)(d.T,{value:q,onChange:e=>R(e.target.value),placeholder:"Add review notes...",className:"mt-1",rows:4})]}),(0,t.jsxs)(r.$,{onClick:M,disabled:w,className:"w-full",children:[(0,t.jsx)(N.A,{className:"w-4 h-4 mr-2"}),w?"Updating...":"Update Application"]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Application Timeline"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Applied Date"}),(0,t.jsx)("p",{className:"text-sm",children:new Date(a.application_date).toLocaleDateString()})]}),a.review_date&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Review Date"}),(0,t.jsx)("p",{className:"text-sm",children:new Date(a.review_date).toLocaleDateString()})]})]})]})]})]})]}):(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center py-8",children:"Application not found"})})}},74228:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\innovation-lab-applications\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\innovation-lab-applications\\[id]\\page.tsx","default")},28790:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(94825).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},25890:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(94825).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},42092:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(94825).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},20111:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(94825).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[638,8403,7834,9267,4873,6562,3015,6516],()=>a(63078));module.exports=t})();