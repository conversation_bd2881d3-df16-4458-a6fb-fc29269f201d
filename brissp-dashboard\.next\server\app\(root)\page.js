(()=>{var e={};e.id=5076,e.ids=[5076],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},10251:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},46559:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},4162:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(70260),a=t(28203),i=t(25155),n=t.n(i),o=t(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d=["",{children:["(root)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97536)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(root)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,22299)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(root)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(root)\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(root)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},54133:(e,r,t)=>{Promise.resolve().then(t.bind(t,97536))},44405:(e,r,t)=>{Promise.resolve().then(t.bind(t,47546))},98167:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,13219,23)),Promise.resolve().then(t.t.bind(t,34863,23)),Promise.resolve().then(t.t.bind(t,25155,23)),Promise.resolve().then(t.t.bind(t,40802,23)),Promise.resolve().then(t.t.bind(t,9350,23)),Promise.resolve().then(t.t.bind(t,48530,23)),Promise.resolve().then(t.t.bind(t,88921,23))},51311:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,66959,23)),Promise.resolve().then(t.t.bind(t,33875,23)),Promise.resolve().then(t.t.bind(t,88903,23)),Promise.resolve().then(t.t.bind(t,57174,23)),Promise.resolve().then(t.t.bind(t,84178,23)),Promise.resolve().then(t.t.bind(t,87190,23)),Promise.resolve().then(t.t.bind(t,61365,23))},56025:(e,r,t)=>{Promise.resolve().then(t.bind(t,56814))},26649:(e,r,t)=>{Promise.resolve().then(t.bind(t,91542))},47546:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(45512),a=t(58009),i=t(79334),n=t(25409),o=t(87021),l=t(91542),d=t(97643),c=t(94825);let p=(0,c.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),m=(0,c.A)("LockKeyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]),u=()=>{let e=(0,i.useRouter)(),[r,t]=(0,a.useState)(!1),[c,u]=(0,a.useState)({email:"",password:""}),f=e=>{let{name:r,value:t}=e.target;u(e=>({...e,[r]:t}))},h=async r=>{r.preventDefault(),t(!0);try{let r=await fetch("/api/admin/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)}),t=await r.json();if(!r.ok)throw Error(t.error||"Failed to sign in");l.o.success("Signed in successfully"),e.push("/panel")}catch(e){l.o.error(e instanceof Error?e.message:"An error occurred")}finally{t(!1)}};return(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen w-full bg-gradient-to-b from-gray-50 to-gray-100",children:(0,s.jsxs)(d.Zp,{className:"w-full max-w-md shadow-lg",children:[(0,s.jsxs)(d.aR,{className:"space-y-1 text-center",children:[(0,s.jsx)(d.ZB,{className:"text-2xl font-bold tracking-tight",children:"Admin Portal"}),(0,s.jsx)(d.BT,{children:"Enter your credentials to access the dashboard"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p,{className:"mr-2 h-4 w-4 text-gray-500"}),(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Email"})]}),(0,s.jsx)(n.p,{type:"email",name:"email",value:c.email,onChange:f,required:!0,placeholder:"<EMAIL>",className:"w-full"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(m,{className:"mr-2 h-4 w-4 text-gray-500"}),(0,s.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Password"})]}),(0,s.jsx)(n.p,{type:"password",name:"password",value:c.password,onChange:f,required:!0,placeholder:"••••••••",className:"w-full"})]}),(0,s.jsx)(o.$,{type:"submit",className:"w-full bg-blue-600 hover:bg-blue-700",disabled:r,children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Signing in..."]}):"Sign In"})]})}),(0,s.jsx)(d.wL,{className:"flex justify-center text-sm text-gray-500",children:(0,s.jsx)("p",{children:"Secure access for authorized personnel only"})})]})})}},87021:(e,r,t)=>{"use strict";t.d(r,{$:()=>d});var s=t(45512),a=t(58009),i=t(12705),n=t(21643),o=t(59462);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:r,size:t,asChild:a=!1,...n},d)=>{let c=a?i.DX:"button";return(0,s.jsx)(c,{className:(0,o.cn)(l({variant:r,size:t,className:e})),ref:d,...n})});d.displayName="Button"},97643:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>p});var s=t(45512),a=t(58009),i=t(59462);let n=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...r}));n.displayName="Card";let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent";let p=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r}));p.displayName="CardFooter"},25409:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(45512),a=t(58009),i=t(59462);let n=a.forwardRef(({className:e,type:r,...t},a)=>(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...t}));n.displayName="Input"},59462:(e,r,t)=>{"use strict";t.d(r,{cn:()=>i});var s=t(82281),a=t(94805);function i(...e){return(0,a.QP)((0,s.$)(e))}},22299:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var s=t(62740),a=t(10251),i=t.n(a),n=t(46559),o=t.n(n);t(37942);var l=t(56814);let d={title:"Brissp Admin",description:"Brissp Academy Admin Dashboard"};function c({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsxs)("body",{className:`${i().variable} ${o().variable} antialiased`,children:[(0,s.jsx)("div",{className:"flex h-screen bg-gray-50",children:e}),(0,s.jsx)(l.Toaster,{})]})})}},97536:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(root)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(root)\\page.tsx","default")},46055:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},37942:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,8403,7834,9267],()=>t(4162));module.exports=s})();