(()=>{var e={};e.id=2291,e.ids=[2291],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},10644:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=t(70260),i=t(28203),a=t(25155),n=t.n(a),d=t(67292),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);t.d(r,l);let o=["",{children:["(dashboard)",{children:["internship-applications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,90685)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\internship-applications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\internship-applications\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/internship-applications/page",pathname:"/internship-applications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},71859:(e,r,t)=>{Promise.resolve().then(t.bind(t,90685))},32107:(e,r,t)=>{Promise.resolve().then(t.bind(t,17278))},17278:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(45512),i=t(58009),a=t(87021),n=t(13393),d=t(77252),l=t(82446),o=t(79041),c=t(43161),p=t(55469),x=t(20111),h=t(25637),u=t(73826),m=t(28531),b=t.n(m);function g(){let[e,r]=(0,i.useState)([]),[t,m]=(0,i.useState)([]),[g,v]=(0,i.useState)(!1),[y,j]=(0,i.useState)("all"),f=async()=>{try{let e=await fetch("/api/internship-applications"),t=await e.json();r(t.applications||[])}catch(e){console.error("Error fetching internship applications:",e)}},N=async e=>{if(confirm("Are you sure you want to delete this application?")){v(!0);try{(await fetch(`/api/internship-applications/${e}`,{method:"DELETE"})).ok&&f()}catch(e){console.error("Error deleting internship application:",e)}finally{v(!1)}}},w=e=>{let r={pending:{variant:"secondary",label:"Pending",className:"bg-yellow-100 text-yellow-800"},reviewed:{variant:"outline",label:"Under Review",className:"bg-blue-100 text-blue-800"},shortlisted:{variant:"default",label:"In Progress",className:"bg-purple-100 text-purple-800"},accepted:{variant:"default",label:"Approved",className:"bg-green-100 text-green-800"},rejected:{variant:"destructive",label:"Rejected",className:"bg-red-100 text-red-800"}},t=r[e]||r.pending;return(0,s.jsx)(d.E,{className:t.className,children:t.label})},A=e=>(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${e?({paid:"bg-green-100 text-green-800",unpaid:"bg-blue-100 text-blue-800",both:"bg-purple-100 text-purple-800"})[e]:"bg-gray-100 text-gray-800"}`,children:e?e.charAt(0).toUpperCase()+e.slice(1):"N/A"}),k={all:e.length,pending:e.filter(e=>"pending"===e.status).length,underReview:e.filter(e=>"reviewed"===e.status).length,approved:e.filter(e=>"accepted"===e.status).length,inProgress:e.filter(e=>"shortlisted"===e.status).length};return(0,s.jsxs)("div",{className:"p-6 space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Internship Applications"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage internship applications and track progress"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-6",children:[(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Applications"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.all})]}),(0,s.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.pending})]}),(0,s.jsx)(p.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Under Review"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.underReview})]}),(0,s.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Approved"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.approved})]}),(0,s.jsx)(h.A,{className:"h-8 w-8 text-green-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"In Progress"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.inProgress})]}),(0,s.jsx)(u.A,{className:"h-8 w-8 text-purple-600"})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border shadow-sm",children:[(0,s.jsx)("div",{className:"border-b",children:(0,s.jsxs)("nav",{className:"flex space-x-8 px-6",children:[(0,s.jsxs)("button",{onClick:()=>j("all"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"all"===y?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["All Applications (",k.all,")"]}),(0,s.jsxs)("button",{onClick:()=>j("pending"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"pending"===y?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Pending (",k.pending,")"]}),(0,s.jsxs)("button",{onClick:()=>j("under-review"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"under-review"===y?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Under Review (",k.underReview,")"]}),(0,s.jsxs)("button",{onClick:()=>j("approved"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"approved"===y?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Approved (",k.approved,")"]}),(0,s.jsxs)("button",{onClick:()=>j("in-progress"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"in-progress"===y?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["In Progress (",k.inProgress,")"]})]})}),(0,s.jsx)("div",{className:"p-6",children:g?(0,s.jsx)("div",{className:"text-center py-8",children:"Loading applications..."}):0===t.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{children:"No applications found for this status."})}):(0,s.jsx)(({applications:e})=>(0,s.jsxs)(n.XI,{children:[(0,s.jsx)(n.A0,{children:(0,s.jsxs)(n.Hj,{children:[(0,s.jsx)(n.nd,{children:"Applicant"}),(0,s.jsx)(n.nd,{children:"University"}),(0,s.jsx)(n.nd,{children:"Course"}),(0,s.jsx)(n.nd,{children:"Type Preference"}),(0,s.jsx)(n.nd,{children:"Status"}),(0,s.jsx)(n.nd,{children:"Applied Date"}),(0,s.jsx)(n.nd,{children:"Actions"})]})}),(0,s.jsx)(n.BF,{children:e.map(e=>(0,s.jsxs)(n.Hj,{children:[(0,s.jsx)(n.nA,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.applicant_name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,s.jsx)(n.nA,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.university}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Year ",e.year_of_study]})]})}),(0,s.jsx)(n.nA,{children:(0,s.jsx)("div",{className:"max-w-xs truncate",title:e.course_of_study,children:e.course_of_study})}),(0,s.jsx)(n.nA,{children:A(e.internship_type_preference)}),(0,s.jsx)(n.nA,{children:w(e.status)}),(0,s.jsx)(n.nA,{children:new Date(e.application_date).toLocaleDateString()}),(0,s.jsx)(n.nA,{children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(b(),{href:`/internship-applications/${e.application_id}`,children:(0,s.jsx)(a.$,{variant:"outline",size:"sm",children:(0,s.jsx)(l.A,{className:"w-4 h-4"})})}),(0,s.jsx)(a.$,{variant:"outline",size:"sm",onClick:()=>N(e.application_id),disabled:g,children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})]})})]},e.application_id))})]}),{applications:t})})]})]})}},77252:(e,r,t)=>{"use strict";t.d(r,{E:()=>d});var s=t(45512);t(58009);var i=t(21643),a=t(59462);let n=(0,i.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,...t}){return(0,s.jsx)("div",{className:(0,a.cn)(n({variant:r}),e),...t})}},90685:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\internship-applications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\internship-applications\\page.tsx","default")},25637:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},73826:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55469:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},82446:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},79041:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},20111:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,8403,7834,9267,4873,2449],()=>t(10644));module.exports=s})();