/* eslint-disable @typescript-eslint/no-unused-vars */
import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { RowDataPacket, ResultSetHeader } from 'mysql2';
import { InternshipApplication } from '../route';

// GET specific internship application by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!id) {
      return NextResponse.json(
        { error: 'Missing application ID' },
        { status: 400 }
      );
    }

    const [rows] = await pool.query<InternshipApplication[]>(
      'SELECT * FROM internship_applications WHERE application_id = ?',
      [id]
    );

    if (!rows[0]) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Get progress tracking data (optional, may not exist)
    let progressRows: RowDataPacket[] = [];
    try {
      const [progress] = await pool.query<RowDataPacket[]>(
        `SELECT * FROM internship_progress
         WHERE application_id = ?
         ORDER BY week_number`,
        [id]
      );
      progressRows = progress;
    } catch (error) {
      // Progress table might not exist, continue without it
      console.log('Progress table not found, continuing without progress data');
    }

    return NextResponse.json({
      application: rows[0],
      progress: progressRows
    });
  } catch (error) {
    console.error('Error fetching internship application:', error);
    return NextResponse.json(
      { error: 'Failed to fetch application' },
      { status: 500 }
    );
  }
}

// PUT update specific internship application
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;
    const data = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Missing application ID' },
        { status: 400 }
      );
    }

    const {
      status,
      review_notes
    } = data;

    const [result] = await pool.query<ResultSetHeader>(
      `UPDATE internship_applications
       SET status = ?, review_notes = ?, review_date = NOW()
       WHERE application_id = ?`,
      [status, review_notes, id]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Application updated successfully' });
  } catch (error) {
    console.error('Error updating internship application:', error);
    return NextResponse.json(
      { error: 'Failed to update application' },
      { status: 500 }
    );
  }
}

// DELETE internship application
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!id) {
      return NextResponse.json(
        { error: 'Missing application ID' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      'DELETE FROM internship_applications WHERE application_id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Application deleted successfully' });
  } catch (error) {
    console.error('Error deleting internship application:', error);
    return NextResponse.json(
      { error: 'Failed to delete application' },
      { status: 500 }
    );
  }
}
