(()=>{var e={};e.id=3645,e.ids=[3645],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},58405:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>d,serverHooks:()=>_,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{DELETE:()=>l,GET:()=>u,PUT:()=>c});var i=t(42706),o=t(28203),a=t(45994),n=t(39187),p=t(62545);async function u(e,{params:r}){try{let e=(await r).id;if(!e)return n.NextResponse.json({error:"Missing application ID"},{status:400});let[t]=await p.A.query("SELECT * FROM final_year_project_applications WHERE application_id = ?",[e]);if(!t[0])return n.NextResponse.json({error:"Application not found"},{status:404});let[s]=await p.A.query(`SELECT * FROM fyp_progress 
       WHERE application_id = ? 
       ORDER BY milestone`,[e]);return n.NextResponse.json({application:t[0],progress:s})}catch(e){return console.error("Error fetching FYP application:",e),n.NextResponse.json({error:"Failed to fetch application"},{status:500})}}async function c(e,{params:r}){try{let t=(await r).id,s=await e.json();if(!t)return n.NextResponse.json({error:"Missing application ID"},{status:400});let{status:i,review_notes:o,assigned_supervisor:a,project_start_date:u,project_completion_date:c,final_report_url:l,presentation_url:d,grade_achieved:x}=s,[f]=await p.A.query(`UPDATE final_year_project_applications 
       SET status = ?, review_notes = ?, assigned_supervisor = ?, 
           project_start_date = ?, project_completion_date = ?, 
           final_report_url = ?, presentation_url = ?, grade_achieved = ?, 
           review_date = NOW()
       WHERE application_id = ?`,[i,o,a,u,c,l,d,x,t]);if(0===f.affectedRows)return n.NextResponse.json({error:"Application not found"},{status:404});return n.NextResponse.json({message:"Application updated successfully"})}catch(e){return console.error("Error updating FYP application:",e),n.NextResponse.json({error:"Failed to update application"},{status:500})}}async function l(e,{params:r}){try{let e=(await r).id;if(!e)return n.NextResponse.json({error:"Missing application ID"},{status:400});let[t]=await p.A.query("DELETE FROM final_year_project_applications WHERE application_id = ?",[e]);if(0===t.affectedRows)return n.NextResponse.json({error:"Application not found"},{status:404});return n.NextResponse.json({message:"Application deleted successfully"})}catch(e){return console.error("Error deleting FYP application:",e),n.NextResponse.json({error:"Failed to delete application"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/fyp-applications/[id]/route",pathname:"/api/fyp-applications/[id]",filename:"route",bundlePath:"app/api/fyp-applications/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\fyp-applications\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:_}=d;function E(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>p});var s=t(60820),i=t(29021),o=t.n(i),a=t(33873),n=t.n(a);let p=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(58405));module.exports=s})();