"use strict";exports.id=6562,exports.ids=[6562],exports.modules={72421:(e,t,n)=>{n.d(t,{Eq:()=>l});var r=new WeakMap,o=new WeakMap,a={},c=0,i=function(e){return e&&(e.host||i(e.parentNode))},u=function(e,t,n,u){var l=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=i(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var d=a[n],s=[],f=new Set,v=new Set(l),p=function(e){!e||f.has(e)||(f.add(e),p(e.parentNode))};l.forEach(p);var h=function(e){!e||v.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))h(e);else try{var t=e.getAttribute(u),a=null!==t&&"false"!==t,c=(r.get(e)||0)+1,i=(d.get(e)||0)+1;r.set(e,c),d.set(e,i),s.push(e),1===c&&a&&o.set(e,!0),1===i&&e.setAttribute(n,"true"),a||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return h(t),f.clear(),c++,function(){s.forEach(function(e){var t=r.get(e)-1,a=d.get(e)-1;r.set(e,t),d.set(e,a),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),a||e.removeAttribute(n)}),--c||(r=new WeakMap,r=new WeakMap,o=new WeakMap,a={})}},l=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r,o=Array.from(Array.isArray(e)?e:[e]),a=t||(r=e,"undefined"==typeof document?null:(Array.isArray(r)?r[0]:r).ownerDocument.body);return a?(o.push.apply(o,Array.from(a.querySelectorAll("[aria-live]"))),u(o,a,n,"aria-hidden")):function(){return null}}},67783:(e,t,n)=>{n.d(t,{A:()=>H});var r,o,a=function(){return(a=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function c(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create,Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,n(58009)),u="right-scroll-bar-position",l="width-before-scroll-bar";function d(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var s="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,f=new WeakMap;function v(e){return e}var p=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=v),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},c=function(){return Promise.resolve().then(a)};c(),n={push:function(e){t.push(e),c()},filter:function(e){return t=t.filter(e),n}}}});return o.options=a({async:!0,ssr:!1},e),o}(),h=function(){},m=i.forwardRef(function(e,t){var n,r,o,u,l=i.useRef(null),v=i.useState({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:h}),m=v[0],g=v[1],y=e.forwardProps,b=e.children,w=e.className,E=e.removeScrollBar,S=e.enabled,C=e.shards,x=e.sideCar,k=e.noIsolation,A=e.inert,M=e.allowPinchZoom,N=e.as,R=e.gapMode,P=c(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[l,t],r=function(e){return n.forEach(function(t){return d(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,s(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||d(e,null)}),r.forEach(function(e){t.has(e)||d(e,o)})}f.set(u,n)},[n]),u),O=a(a({},P),m);return i.createElement(i.Fragment,null,S&&i.createElement(x,{sideCar:p,removeScrollBar:E,shards:C,noIsolation:k,inert:A,setCallbacks:g,allowPinchZoom:!!M,lockRef:l,gapMode:R}),y?i.cloneElement(i.Children.only(b),a(a({},O),{ref:L})):i.createElement(void 0===N?"div":N,a({},O,{className:w,ref:L}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:l,zeroRight:u};var g=function(e){var t=e.sideCar,n=c(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,a({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,c;(a=t).styleSheet?a.styleSheet.cssText=r:a.appendChild(document.createTextNode(r)),c=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(c)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},b=function(){var e=y();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},w=function(){var e=b();return function(t){return e(t.styles,t.dynamic),null}},E={left:0,top:0,right:0,gap:0},S=function(e){return parseInt(e||"",10)||0},C=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[S(n),S(r),S(o)]},x=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return E;var t=C(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},k=w(),A="data-scroll-locked",M=function(e,t,n,r){var o=e.left,a=e.top,c=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},N=function(){var e=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(e)?e:0},R=function(){i.useEffect(function(){return document.body.setAttribute(A,(N()+1).toString()),function(){var e=N()-1;e<=0?document.body.removeAttribute(A):document.body.setAttribute(A,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;R();var a=i.useMemo(function(){return x(o)},[o]);return i.createElement(k,{styles:M(a,!t,o,n?"":"!important")})},L=!1;if("undefined"!=typeof window)try{var O=Object.defineProperty({},"passive",{get:function(){return L=!0,!0}});window.addEventListener("test",O,O),window.removeEventListener("test",O,O)}catch(e){L=!1}var j=!!L&&{passive:!1},T=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},W=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),B(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},B=function(e,t){return"v"===e?T(t,"overflowY"):T(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},$=function(e,t,n,r,o){var a,c=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=c*r,u=n.target,l=t.contains(u),d=!1,s=i>0,f=0,v=0;do{var p=I(e,u),h=p[0],m=p[1]-p[2]-c*h;(h||m)&&B(e,u)&&(f+=m,v+=h),u instanceof ShadowRoot?u=u.host:u=u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return s&&(o&&1>Math.abs(f)||!o&&i>f)?d=!0:!s&&(o&&1>Math.abs(v)||!o&&-i>v)&&(d=!0),d},X=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Y=function(e){return[e.deltaX,e.deltaY]},_=function(e){return e&&"current"in e?e.current:e},q=0,D=[];let Z=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(q++)[0],a=i.useState(w)[0],c=i.useRef(e);i.useEffect(function(){c.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(_),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var o,a=X(e),i=n.current,u="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],d=e.target,s=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===s&&"range"===d.type)return!1;var f=W(s,d);if(!f)return!0;if(f?o=s:(o="v"===s?"h":"v",f=W(s,d)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var v=r.current||o;return $(v,t,e,"h"===v?u:l,!0)},[]),l=i.useCallback(function(e){if(D.length&&D[D.length-1]===a){var n="deltaY"in e?Y(e):X(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(_).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),d=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),s=i.useCallback(function(e){n.current=X(e),r.current=void 0},[]),f=i.useCallback(function(t){d(t.type,Y(t),t.target,u(t,e.lockRef.current))},[]),v=i.useCallback(function(t){d(t.type,X(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return D.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",l,j),document.addEventListener("touchmove",l,j),document.addEventListener("touchstart",s,j),function(){D=D.filter(function(e){return e!==a}),document.removeEventListener("wheel",l,j),document.removeEventListener("touchmove",l,j),document.removeEventListener("touchstart",s,j)}},[]);var p=e.removeScrollBar,h=e.inert;return i.createElement(i.Fragment,null,h?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?i.createElement(P,{gapMode:e.gapMode}):null)},p.useMedium(r),g);var F=i.forwardRef(function(e,t){return i.createElement(m,a({},e,{ref:t,sideCar:Z}))});F.classNames=m.classNames;let H=F},31412:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},6004:(e,t,n)=>{n.d(t,{A:()=>c,q:()=>a});var r=n(58009),o=n(45512);function a(e,t){let n=r.createContext(t),a=e=>{let{children:t,...a}=e,c=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(n.Provider,{value:c,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function c(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){let c=r.createContext(a),i=n.length;n=[...n,a];let u=t=>{let{scope:n,children:a,...u}=t,l=n?.[e]?.[i]||c,d=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(l.Provider,{value:d,children:a})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[i]||c,l=r.useContext(u);if(l)return l;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},59018:(e,t,n)=>{n.d(t,{jH:()=>a});var r=n(58009);n(45512);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},19632:(e,t,n)=>{n.d(t,{Oh:()=>a});var r=n(58009),o=0;function a(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??c()),document.body.insertAdjacentElement("beforeend",e[1]??c()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function c(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},30096:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(58009),a=n(49397),c=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),i=0;function u(e){let[t,n]=o.useState(c());return(0,a.N)(()=>{e||n(e=>e??String(i++))},[e]),e||(t?`radix-${t}`:"")}},92828:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(58009);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},13024:(e,t,n)=>{n.d(t,{i:()=>a});var r=n(58009),o=n(92828);function a({prop:e,defaultProp:t,onChange:n=()=>{}}){let[a,c]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[a]=n,c=r.useRef(a),i=(0,o.c)(t);return r.useEffect(()=>{c.current!==a&&(i(a),c.current=a)},[a,c,i]),n}({defaultProp:t,onChange:n}),i=void 0!==e,u=i?e:a,l=(0,o.c)(n);return[u,r.useCallback(t=>{if(i){let n="function"==typeof t?t(e):t;n!==e&&l(n)}else c(t)},[i,e,c,l])]}},99850:(e,t,n)=>{n.d(t,{U:()=>a});var r=n(58009),o=n(92828);function a(e,t=globalThis?.document){let n=(0,o.c)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}},49397:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(58009),o=globalThis?.document?r.useLayoutEffect:()=>{}}};