(()=>{var e={};e.id=7386,e.ids=[7386],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},73844:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=r(70260),o=r(28203),n=r(25155),a=r.n(n),i=r(67292),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(s,l);let d=["",{children:["(dashboard)",{children:["password-management",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,19512)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\password-management\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\password-management\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(dashboard)/password-management/page",pathname:"/password-management",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81960:(e,s,r)=>{Promise.resolve().then(r.bind(r,19512))},68808:(e,s,r)=>{Promise.resolve().then(r.bind(r,7716))},94168:(e,s,r)=>{Promise.resolve().then(r.bind(r,60189)),Promise.resolve().then(r.bind(r,22234)),Promise.resolve().then(r.bind(r,56814))},3896:(e,s,r)=>{Promise.resolve().then(r.bind(r,52401)),Promise.resolve().then(r.bind(r,58783)),Promise.resolve().then(r.bind(r,91542))},98167:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},51311:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},7716:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(45512),o=r(58009),n=r(91542);function a(){let[e,s]=(0,o.useState)([]),[r,a]=(0,o.useState)(!0),[i,l]=(0,o.useState)(null),[d,c]=(0,o.useState)(""),u=async e=>{if(e.preventDefault(),!i){n.o.error("Please select a user");return}if(!d){n.o.error("Please enter a new password");return}try{a(!0);let e=await fetch(`/api/admin/users/${i}/password`,{method:"PUT",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({password:d})});if(e.ok)n.o.success("Password updated successfully"),c(""),l(null);else{let s=await e.json();n.o.error(s.error||"Failed to update password")}}catch(e){console.error("Error updating password:",e),n.o.error("An error occurred while updating password")}finally{a(!1)}};return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Password Management"}),r?(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Select User"}),0===e.length?(0,t.jsx)("p",{className:"text-gray-500",children:"No users found"}):(0,t.jsx)("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:e.map(e=>(0,t.jsxs)("div",{className:`p-3 border rounded-lg cursor-pointer ${i===e.admin_id?"border-blue-500 bg-blue-50":"border-gray-200 hover:bg-gray-50"}`,onClick:()=>l(e.admin_id),children:[(0,t.jsx)("p",{className:"font-medium",children:e.full_name}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.email})]},e.admin_id))})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Change Password"}),(0,t.jsxs)("form",{onSubmit:u,children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{htmlFor:"newPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"New Password"}),(0,t.jsx)("input",{type:"password",id:"newPassword",value:d,onChange:e=>c(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter new password",disabled:!i})]}),(0,t.jsx)("button",{type:"submit",disabled:!i||!d||r,className:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",children:"Update Password"})]})]})]})]})}},52401:(e,s,r)=>{"use strict";r.d(s,{default:()=>i});var t=r(45512),o=r(58009),n=r(79334),a=r(91542);let i=({children:e})=>{let s=(0,n.useRouter)(),[r,i]=(0,o.useState)(null);return((0,o.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/admin/auth/check",{method:"GET",credentials:"include",cache:"no-store"});if(!e.ok)throw Error("Not authenticated");let s=await e.json();console.log("Auth check response:",s),i(!0)}catch(e){console.error("Authentication check failed:",e),i(!1),a.o.error("Please log in to access the dashboard"),s.push("/")}})()},[s]),null===r)?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"})}):r?(0,t.jsx)(t.Fragment,{children:e}):null}},58783:(e,s,r)=>{"use strict";r.d(s,{default:()=>C});var t=r(45512),o=r(58009),n=r(28531),a=r.n(n),i=r(38440),l=r(1422),d=r(57631),c=r(35603),u=r(43161),p=r(62673),m=r(28650),h=r(52975),b=r(39327),f=r(55817),x=r(53100),g=r(93346),v=r(6472),w=r(51255),y=r(35120),j=r(48320),P=r(79334),N=r(91542);let k=({onToggle:e})=>(0,t.jsxs)("div",{className:"md:hidden bg-white border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30",children:[(0,t.jsx)("button",{type:"button",onClick:e,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Toggle navigation menu",title:"Toggle navigation menu",children:(0,t.jsx)(j.A,{className:"w-6 h-6"})}),(0,t.jsx)("h1",{className:"font-semibold text-lg",children:"Brissp Dashboard"}),(0,t.jsx)("div",{className:"w-10"})," "]}),A=({isOpen:e=!1,onToggle:s})=>{let r=(0,P.useRouter)(),[n,j]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=()=>{j(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,o.useEffect)(()=>{let r=r=>{if(n&&e&&s){let e=document.getElementById("mobile-sidebar");e&&!e.contains(r.target)&&s()}};if(n&&e)return document.addEventListener("mousedown",r),()=>document.removeEventListener("mousedown",r)},[n,e,s]),(0,o.useEffect)(()=>(n&&e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[n,e]);let k=async()=>{try{(await fetch("/api/admin/auth/logout",{method:"POST",credentials:"include"})).ok?(N.o.success("Logged out successfully"),r.push("/")):N.o.error("Failed to log out")}catch(e){console.error("Logout error:",e),N.o.error("An error occurred during logout")}},A=()=>{n&&s&&s()},C=[{href:"/panel",icon:i.A,label:"Dashboard"},{href:"/applications",icon:l.A,label:"Course Applications"},{href:"/pitch-deck-applications",icon:d.A,label:"Pitch Deck Applications"},{href:"/internship-applications",icon:c.A,label:"Internship Applications"},{href:"/fyp-applications",icon:u.A,label:"FYP Applications"},{href:"/innovation-lab-applications",icon:p.A,label:"Innovation Lab Applications"},{href:"/results",icon:m.A,label:"Results"},{href:"/downloadables",icon:h.A,label:"Resources"},{href:"/courses",icon:b.A,label:"Courses"},{href:"/curriculum",icon:f.A,label:"Curriculum"},{href:"/notice-board",icon:x.A,label:"Notice Board"},{href:"/graduates",icon:g.A,label:"Graduates"},{href:"/password-management",icon:v.A,label:"Password Management"}];return(0,t.jsxs)(t.Fragment,{children:[n&&e&&(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:s}),(0,t.jsxs)("div",{id:"mobile-sidebar",className:`
          ${n?`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`:"fixed inset-y-0 left-0 w-64"}
          border-r bg-white h-screen flex flex-col
        `,children:[(0,t.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,t.jsx)(a(),{href:"/panel",className:"flex items-center space-x-2",onClick:A,children:(0,t.jsx)("span",{className:"font-semibold text-lg",children:"Brissp Dash"})}),n&&(0,t.jsx)("button",{type:"button",onClick:s,className:"p-2 rounded-lg hover:bg-gray-100 md:hidden","aria-label":"Close navigation menu",title:"Close navigation menu",children:(0,t.jsx)(w.A,{className:"w-5 h-5"})})]}),(0,t.jsx)("nav",{className:"p-4 space-y-2 flex-grow overflow-y-auto",children:C.map(e=>{let s=e.icon;return(0,t.jsxs)(a(),{href:e.href,onClick:A,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200",children:[(0,t.jsx)(s,{className:"w-5 h-5 flex-shrink-0"}),(0,t.jsx)("span",{className:"truncate",children:e.label})]},e.href)})}),(0,t.jsx)("div",{className:"p-4 border-t mt-auto",children:(0,t.jsxs)("button",{type:"button",onClick:k,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full transition-colors duration-200",children:[(0,t.jsx)(y.A,{className:"w-5 h-5 flex-shrink-0"}),(0,t.jsx)("span",{children:"Logout"})]})})]})]})},C=({children:e})=>{let[s,r]=(0,o.useState)(!1),[n,a]=(0,o.useState)(!1);(0,o.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;a(e),e||r(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let i=()=>{r(!s)};return(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,t.jsx)(k,{onToggle:i}),(0,t.jsx)(A,{isOpen:s,onToggle:i}),(0,t.jsx)("div",{className:`
        transition-all duration-300 ease-in-out
        ${n?"ml-0":"ml-64"}
      `,children:(0,t.jsx)("main",{className:"min-h-screen",children:e})})]})}},71975:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>p,metadata:()=>u});var t=r(62740),o=r(41583),n=r.n(o),a=r(76499),i=r.n(a);r(2012);var l=r(22234),d=r(60189),c=r(56814);let u={title:"Brissp Admin",description:"Brissp Academy Admin Dashboard"};function p({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsxs)("body",{className:`${n().variable} ${i().variable} antialiased`,children:[(0,t.jsx)(d.default,{children:(0,t.jsx)(l.default,{children:e})}),(0,t.jsx)(c.Toaster,{})]})})}},19512:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\password-management\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\password-management\\page.tsx","default")},60189:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\AuthGuard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\AuthGuard.tsx","default")},22234:(e,s,r)=>{"use strict";r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\ResponsiveLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\ResponsiveLayout.tsx","default")},46055:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(88077);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},2012:()=>{}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,8403,7834,4873],()=>r(73844));module.exports=t})();