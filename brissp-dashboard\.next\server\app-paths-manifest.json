{"/_not-found/page": "app/_not-found/page.js", "/api/admin/auth/check/route": "app/api/admin/auth/check/route.js", "/api/admin/auth/login/route": "app/api/admin/auth/login/route.js", "/api/admin/auth/logout/route": "app/api/admin/auth/logout/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/applications/[applicationId]/route": "app/api/applications/[applicationId]/route.js", "/api/admin/auth/me/route": "app/api/admin/auth/me/route.js", "/api/admin/users/[id]/password/route": "app/api/admin/users/[id]/password/route.js", "/api/courses/[id]/route": "app/api/courses/[id]/route.js", "/api/admin/users/[id]/route": "app/api/admin/users/[id]/route.js", "/api/curriculum/[id]/route": "app/api/curriculum/[id]/route.js", "/api/carriculum/route": "app/api/carriculum/route.js", "/api/applications/route": "app/api/applications/route.js", "/api/courses/route": "app/api/courses/route.js", "/api/curriculum/route": "app/api/curriculum/route.js", "/api/dashboard/stats/route": "app/api/dashboard/stats/route.js", "/api/debug-table/route": "app/api/debug-table/route.js", "/api/downloadables/[id]/route": "app/api/downloadables/[id]/route.js", "/api/downloadables/route": "app/api/downloadables/route.js", "/api/enrollments/route": "app/api/enrollments/route.js", "/api/files/[...path]/route": "app/api/files/[...path]/route.js", "/api/files/manage/route": "app/api/files/manage/route.js", "/api/fyp-applications/[id]/route": "app/api/fyp-applications/[id]/route.js", "/api/graduates/[id]/route": "app/api/graduates/[id]/route.js", "/api/fyp-applications/route": "app/api/fyp-applications/route.js", "/api/gallery/[id]/route": "app/api/gallery/[id]/route.js", "/api/gallery/route": "app/api/gallery/route.js", "/api/graduates/route": "app/api/graduates/route.js", "/api/innovation-lab-applications/[id]/route": "app/api/innovation-lab-applications/[id]/route.js", "/api/notices/[id]/route": "app/api/notices/[id]/route.js", "/api/innovation-lab-applications/route": "app/api/innovation-lab-applications/route.js", "/api/internship-applications/[id]/route": "app/api/internship-applications/[id]/route.js", "/api/internship-applications/route": "app/api/internship-applications/route.js", "/api/results/[id]/route": "app/api/results/[id]/route.js", "/api/pitch-deck-applications/[id]/route": "app/api/pitch-deck-applications/[id]/route.js", "/api/notices/route": "app/api/notices/route.js", "/api/pitch-deck-applications/route": "app/api/pitch-deck-applications/route.js", "/api/results/route": "app/api/results/route.js", "/api/setup/route": "app/api/setup/route.js", "/api/test-db/route": "app/api/test-db/route.js", "/api/test-internships/route": "app/api/test-internships/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/users/route": "app/api/users/route.js", "/(dashboard)/courses/[id]/edit/page": "app/(dashboard)/courses/[id]/edit/page.js", "/(dashboard)/courses/new/page": "app/(dashboard)/courses/new/page.js", "/(dashboard)/applications/page": "app/(dashboard)/applications/page.js", "/(dashboard)/courses/page": "app/(dashboard)/courses/page.js", "/(dashboard)/courses/refresher/[id]/edit/page": "app/(dashboard)/courses/refresher/[id]/edit/page.js", "/(dashboard)/curriculum/[id]/edit/page": "app/(dashboard)/curriculum/[id]/edit/page.js", "/(dashboard)/curriculum/new/page": "app/(dashboard)/curriculum/new/page.js", "/(dashboard)/downloadables/new/page": "app/(dashboard)/downloadables/new/page.js", "/(dashboard)/downloadables/[id]/edit/page": "app/(dashboard)/downloadables/[id]/edit/page.js", "/(dashboard)/curriculum/page": "app/(dashboard)/curriculum/page.js", "/(dashboard)/downloadables/page": "app/(dashboard)/downloadables/page.js", "/(dashboard)/fyp-applications/[id]/page": "app/(dashboard)/fyp-applications/[id]/page.js", "/(dashboard)/graduates/[id]/edit/page": "app/(dashboard)/graduates/[id]/edit/page.js", "/(dashboard)/fyp-applications/page": "app/(dashboard)/fyp-applications/page.js", "/(dashboard)/graduates/new/page": "app/(dashboard)/graduates/new/page.js", "/(dashboard)/graduates/page": "app/(dashboard)/graduates/page.js", "/(dashboard)/innovation-lab-applications/[id]/page": "app/(dashboard)/innovation-lab-applications/[id]/page.js", "/(dashboard)/internship-applications/[id]/page": "app/(dashboard)/internship-applications/[id]/page.js", "/(dashboard)/internship-applications/page": "app/(dashboard)/internship-applications/page.js", "/(dashboard)/notice-board/[id]/edit/page": "app/(dashboard)/notice-board/[id]/edit/page.js", "/(dashboard)/notice-board/new/page": "app/(dashboard)/notice-board/new/page.js", "/(dashboard)/innovation-lab-applications/page": "app/(dashboard)/innovation-lab-applications/page.js", "/(dashboard)/notice-board/page": "app/(dashboard)/notice-board/page.js", "/(dashboard)/panel/page": "app/(dashboard)/panel/page.js", "/(dashboard)/password-management/page": "app/(dashboard)/password-management/page.js", "/(dashboard)/pitch-deck-applications/[id]/page": "app/(dashboard)/pitch-deck-applications/[id]/page.js", "/(dashboard)/pitch-deck-applications/page": "app/(dashboard)/pitch-deck-applications/page.js", "/(dashboard)/results/page": "app/(dashboard)/results/page.js", "/(dashboard)/courses/refresher/new/page": "app/(dashboard)/courses/refresher/new/page.js", "/api/upload/cloud/route": "app/api/upload/cloud/route.js", "/(root)/page": "app/(root)/page.js"}