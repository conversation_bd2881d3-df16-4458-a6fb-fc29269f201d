(()=>{var e={};e.id=8180,e.ids=[8180],e.modules={28303:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=28303,e.exports=t},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},20331:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>_,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>c});var i=r(42706),n=r(28203),o=r(45994),a=r(39187),p=r(62545);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("status"),s=t.get("type"),i=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"10"),o=(i-1)*n,u=`
      SELECT *
      FROM internship_applications
    `,c=[],l=[];r&&"all"!==r&&(l.push("status = ?"),c.push(r)),s&&"all"!==s&&(l.push("internship_type_preference = ?"),c.push(s)),l.length>0&&(u+=" WHERE "+l.join(" AND ")),u+=" ORDER BY application_date DESC LIMIT ? OFFSET ?",c.push(n,o),console.log("Executing query:",u),console.log("Query params:",c);let[d]=await p.A.query(u,c);console.log("Query result count:",d.length),d.length>0&&console.log("Sample application data:",d[0]);let h="SELECT COUNT(*) as total FROM internship_applications",x=[];l.length>0&&(h+=" WHERE "+l.join(" AND "),r&&"all"!==r&&x.push(r),s&&"all"!==s&&x.push(s));let[_]=await p.A.query(h,x),g=_[0].total;return a.NextResponse.json({applications:d,pagination:{page:i,limit:n,total:g,totalPages:Math.ceil(g/n)}})}catch(e){return console.error("Error fetching internship applications:",e),a.NextResponse.json({error:"Failed to fetch applications",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function c(e){try{let{internship_id:t,student_name:r,student_email:s,student_phone:i,university:n,course_of_study:o,year_of_study:u,gpa:c,cv_url:l,cover_letter:d,portfolio_url:h,linkedin_url:x,github_url:_,availability_start:g,preferred_duration:v,motivation:y,relevant_experience:f,technical_skills:m,soft_skills:q,preferred_industry:E,internship_type_preference:S}=await e.json();if(!r||!s||!i||!n||!o||!u)return a.NextResponse.json({error:"Missing required fields"},{status:400});let[R]=await p.A.query(`INSERT INTO internship_applications (
        internship_id, student_name, student_email, student_phone, university,
        course_of_study, year_of_study, gpa, cv_url, cover_letter, portfolio_url,
        linkedin_url, github_url, availability_start, preferred_duration, motivation,
        relevant_experience, technical_skills, soft_skills, preferred_industry,
        internship_type_preference
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,[t,r,s,i,n,o,u,c,l,d,h,x,_,g,v,y,f,m,q,E,S]);return a.NextResponse.json({message:"Application submitted successfully",application_id:R.insertId},{status:201})}catch(e){return console.error("Error creating internship application:",e),a.NextResponse.json({error:"Failed to submit application"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/internship-applications/route",pathname:"/api/internship-applications",filename:"route",bundlePath:"app/api/internship-applications/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\internship-applications\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:x}=l;function _(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},96487:()=>{},78335:()=>{},62545:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(60820),i=r(29021),n=r.n(i),o=r(33873),a=r.n(o);let p=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:n().readFileSync(a().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452,820],()=>r(20331));module.exports=s})();