(()=>{var e={};e.id=5694,e.ids=[5694],e.modules={28303:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=28303,e.exports=t},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},65147:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>T,routeModule:()=>c,serverHooks:()=>E,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>u});var a=r(42706),i=r(28203),o=r(45994),p=r(39187),n=r(62545);async function u(){try{let[e]=await n.A.query("SELECT COUNT(*) as total FROM applications WHERE status = 'approved'"),t=e[0].total,[r]=await n.A.query("SELECT COUNT(*) as total FROM courses"),[s]=await n.A.query("SELECT COUNT(*) as total FROM pitch_deck_applications"),a=s[0]?.total||0,[i]=await n.A.query("SELECT COUNT(*) as total FROM internship_applications"),o=i[0]?.total||0,[u]=await n.A.query("SELECT COUNT(*) as total FROM final_year_project_applications"),c=u[0]?.total||0,d=r[0].total,[l]=await n.A.query(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending
      FROM applications
    `),{total:E,pending:T}=l[0],[O]=await n.A.query(`
      SELECT 
        a.*,
        c.title as course_title
      FROM applications a
      JOIN courses c ON a.course_id = c.course_id
      ORDER BY a.application_date DESC
      LIMIT 5
    `),[x]=await n.A.query(`
      SELECT 
        DATE(application_date) as date,
        COUNT(*) as applications
      FROM applications
      WHERE application_date >= DATE_SUB(CURRENT_DATE, INTERVAL 7 DAY)
      GROUP BY DATE(application_date)
      ORDER BY date
    `),[S]=await n.A.query(`
      SELECT 
        c.title as course_title,
        COUNT(DISTINCT CASE WHEN a.status = 'approved' THEN a.application_id END) as total_students,
        COUNT(DISTINCT CASE WHEN a.status = 'pending' THEN a.application_id END) as pending_applications
      FROM courses c
      LEFT JOIN applications a ON c.course_id = a.course_id
      GROUP BY c.course_id, c.title
      ORDER BY total_students DESC
      LIMIT 5
    `);return p.NextResponse.json({totalStudents:t,totalCourses:d,totalApplications:E,pendingApplications:T,totalPitchDeck:a,totalInternships:o,totalFYP:c,recentApplications:O,applicationTrend:x,courseStats:S})}catch(e){return console.error("Error fetching dashboard stats:",e),p.NextResponse.json({error:"Internal Server Error"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:E}=c;function T(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},96487:()=>{},78335:()=>{},62545:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(60820),a=r(29021),i=r.n(a),o=r(33873),p=r.n(o);let n=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(p().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452,820],()=>r(65147));module.exports=s})();