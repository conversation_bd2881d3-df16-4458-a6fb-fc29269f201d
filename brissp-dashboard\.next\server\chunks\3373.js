exports.id=3373,exports.ids=[3373,8273],exports.modules={94168:(e,t,s)=>{Promise.resolve().then(s.bind(s,60189)),Promise.resolve().then(s.bind(s,22234)),Promise.resolve().then(s.bind(s,56814))},3896:(e,t,s)=>{Promise.resolve().then(s.bind(s,52401)),Promise.resolve().then(s.bind(s,58783)),Promise.resolve().then(s.bind(s,91542))},98167:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},51311:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},52401:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var r=s(45512),n=s(58009),a=s(79334),i=s(91542);let o=({children:e})=>{let t=(0,a.useRouter)(),[s,o]=(0,n.useState)(null);return((0,n.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/admin/auth/check",{method:"GET",credentials:"include",cache:"no-store"});if(!e.ok)throw Error("Not authenticated");let t=await e.json();console.log("Auth check response:",t),o(!0)}catch(e){console.error("Authentication check failed:",e),o(!1),i.o.error("Please log in to access the dashboard"),t.push("/")}})()},[t]),null===s)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"})}):s?(0,r.jsx)(r.Fragment,{children:e}):null}},36929:(e,t,s)=>{"use strict";s.d(t,{A:()=>c});var r=s(45512),n=s(58009),a=s(25409),i=s(87021),o=s(53261),l=s(48859),d=s(54069);let c=({initialData:e,onSubmit:t,isLoading:s})=>{let[c,u]=(0,n.useState)([]),[m,f]=(0,n.useState)({course_id:e?.course_id||0,week_number:e?.week_number||1,topic:e?.topic||"",content:e?.content||"",learning_objectives:e?.learning_objectives||""});(0,n.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/courses"),t=await e.json();u(t)}catch(e){console.error("Error fetching courses:",e)}})()},[]);let p=e=>{let{name:t,value:s}=e.target;f(e=>({...e,[t]:s}))},h=async e=>{e.preventDefault(),await t({...m,course_id:Number(m.course_id)})};return(0,r.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"course_id",children:"Course"}),(0,r.jsxs)(d.l6,{value:m.course_id.toString(),onValueChange:e=>{f(t=>({...t,course_id:Number(e)}))},children:[(0,r.jsx)(d.bq,{children:(0,r.jsx)(d.yv,{placeholder:"Select a course"})}),(0,r.jsx)(d.gC,{children:c.map(e=>(0,r.jsx)(d.eb,{value:e.course_id.toString(),children:e.title},e.course_id))})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"week_number",children:"Week Number"}),(0,r.jsx)(a.p,{id:"week_number",name:"week_number",type:"number",min:"1",value:m.week_number,onChange:p,required:!0})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"topic",children:"Topic"}),(0,r.jsx)(a.p,{id:"topic",name:"topic",value:m.topic,onChange:p,required:!0})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"content",children:"Content"}),(0,r.jsx)(l.T,{id:"content",name:"content",value:m.content,onChange:p,required:!0,rows:5})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(o.J,{htmlFor:"learning_objectives",children:"Learning Objectives"}),(0,r.jsx)(l.T,{id:"learning_objectives",name:"learning_objectives",value:m.learning_objectives,onChange:p,rows:3})]}),(0,r.jsx)(i.$,{type:"submit",disabled:s,children:s?"Saving...":"Save Curriculum"})]})}},58783:(e,t,s)=>{"use strict";s.d(t,{default:()=>P});var r=s(45512),n=s(58009),a=s(28531),i=s.n(a),o=s(38440),l=s(1422),d=s(57631),c=s(35603),u=s(43161),m=s(62673),f=s(28650),p=s(52975),h=s(39327),b=s(55817),x=s(53100),g=s(93346),v=s(6472),y=s(51255),j=s(35120),w=s(48320),N=s(79334),k=s(91542);let A=({onToggle:e})=>(0,r.jsxs)("div",{className:"md:hidden bg-white border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30",children:[(0,r.jsx)("button",{type:"button",onClick:e,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Toggle navigation menu",title:"Toggle navigation menu",children:(0,r.jsx)(w.A,{className:"w-6 h-6"})}),(0,r.jsx)("h1",{className:"font-semibold text-lg",children:"Brissp Dashboard"}),(0,r.jsx)("div",{className:"w-10"})," "]}),C=({isOpen:e=!1,onToggle:t})=>{let s=(0,N.useRouter)(),[a,w]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=()=>{w(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,n.useEffect)(()=>{let s=s=>{if(a&&e&&t){let e=document.getElementById("mobile-sidebar");e&&!e.contains(s.target)&&t()}};if(a&&e)return document.addEventListener("mousedown",s),()=>document.removeEventListener("mousedown",s)},[a,e,t]),(0,n.useEffect)(()=>(a&&e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[a,e]);let A=async()=>{try{(await fetch("/api/admin/auth/logout",{method:"POST",credentials:"include"})).ok?(k.o.success("Logged out successfully"),s.push("/")):k.o.error("Failed to log out")}catch(e){console.error("Logout error:",e),k.o.error("An error occurred during logout")}},C=()=>{a&&t&&t()},P=[{href:"/panel",icon:o.A,label:"Dashboard"},{href:"/applications",icon:l.A,label:"Course Applications"},{href:"/pitch-deck-applications",icon:d.A,label:"Pitch Deck Applications"},{href:"/internship-applications",icon:c.A,label:"Internship Applications"},{href:"/fyp-applications",icon:u.A,label:"FYP Applications"},{href:"/innovation-lab-applications",icon:m.A,label:"Innovation Lab Applications"},{href:"/results",icon:f.A,label:"Results"},{href:"/downloadables",icon:p.A,label:"Resources"},{href:"/courses",icon:h.A,label:"Courses"},{href:"/curriculum",icon:b.A,label:"Curriculum"},{href:"/notice-board",icon:x.A,label:"Notice Board"},{href:"/graduates",icon:g.A,label:"Graduates"},{href:"/password-management",icon:v.A,label:"Password Management"}];return(0,r.jsxs)(r.Fragment,{children:[a&&e&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:t}),(0,r.jsxs)("div",{id:"mobile-sidebar",className:`
          ${a?`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`:"fixed inset-y-0 left-0 w-64"}
          border-r bg-white h-screen flex flex-col
        `,children:[(0,r.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,r.jsx)(i(),{href:"/panel",className:"flex items-center space-x-2",onClick:C,children:(0,r.jsx)("span",{className:"font-semibold text-lg",children:"Brissp Dash"})}),a&&(0,r.jsx)("button",{type:"button",onClick:t,className:"p-2 rounded-lg hover:bg-gray-100 md:hidden","aria-label":"Close navigation menu",title:"Close navigation menu",children:(0,r.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("nav",{className:"p-4 space-y-2 flex-grow overflow-y-auto",children:P.map(e=>{let t=e.icon;return(0,r.jsxs)(i(),{href:e.href,onClick:C,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200",children:[(0,r.jsx)(t,{className:"w-5 h-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.label})]},e.href)})}),(0,r.jsx)("div",{className:"p-4 border-t mt-auto",children:(0,r.jsxs)("button",{type:"button",onClick:A,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full transition-colors duration-200",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 flex-shrink-0"}),(0,r.jsx)("span",{children:"Logout"})]})})]})]})},P=({children:e})=>{let[t,s]=(0,n.useState)(!1),[a,i]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;i(e),e||s(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let o=()=>{s(!t)};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(A,{onToggle:o}),(0,r.jsx)(C,{isOpen:t,onToggle:o}),(0,r.jsx)("div",{className:`
        transition-all duration-300 ease-in-out
        ${a?"ml-0":"ml-64"}
      `,children:(0,r.jsx)("main",{className:"min-h-screen",children:e})})]})}},87021:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(45512),n=s(58009),a=s(12705),i=s(21643),o=s(59462);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:t,size:s,asChild:n=!1,...i},d)=>{let c=n?a.DX:"button";return(0,r.jsx)(c,{className:(0,o.cn)(l({variant:t,size:s,className:e})),ref:d,...i})});d.displayName="Button"},25409:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(45512),n=s(58009),a=s(59462);let i=n.forwardRef(({className:e,type:t,...s},n)=>(0,r.jsx)("input",{type:t,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:n,...s}));i.displayName="Input"},53261:(e,t,s)=>{"use strict";s.d(t,{J:()=>c});var r=s(45512),n=s(58009),a=s(18055),i=n.forwardRef((e,t)=>(0,r.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=s(21643),l=s(59462);let d=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i,{ref:s,className:(0,l.cn)(d(),e),...t}));c.displayName=i.displayName},54069:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>b,gC:()=>h,l6:()=>c,yv:()=>u});var r=s(45512),n=s(58009),a=s(96096),i=s(7833),o=s(36624),l=s(24999),d=s(59462);let c=a.bL;a.YJ;let u=a.WT,m=n.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsxs)(a.l9,{ref:n,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[t,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=a.l9.displayName;let f=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})}));f.displayName=a.PP.displayName;let p=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}));p.displayName=a.wn.displayName;let h=n.forwardRef(({className:e,children:t,position:s="popper",...n},i)=>(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{ref:i,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,r.jsx)(f,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(p,{})]})}));h.displayName=a.UC.displayName,n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=a.JU.displayName;let b=n.forwardRef(({className:e,children:t,...s},n)=>(0,r.jsxs)(a.q7,{ref:n,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})}),(0,r.jsx)(a.p4,{children:t})]}));b.displayName=a.q7.displayName,n.forwardRef(({className:e,...t},s)=>(0,r.jsx)(a.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=a.wv.displayName},48859:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var r=s(45512),n=s(58009),a=s(59462);let i=n.forwardRef(({className:e,...t},s)=>(0,r.jsx)("textarea",{className:(0,a.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));i.displayName="Textarea"},59462:(e,t,s)=>{"use strict";s.d(t,{cn:()=>a});var r=s(82281),n=s(94805);function a(...e){return(0,n.QP)((0,r.$)(e))}},71975:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>u});var r=s(62740),n=s(41583),a=s.n(n),i=s(76499),o=s.n(i);s(2012);var l=s(22234),d=s(60189),c=s(56814);let u={title:"Brissp Admin",description:"Brissp Academy Admin Dashboard"};function m({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{className:`${a().variable} ${o().variable} antialiased`,children:[(0,r.jsx)(d.default,{children:(0,r.jsx)(l.default,{children:e})}),(0,r.jsx)(c.Toaster,{})]})})}},60189:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\AuthGuard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\AuthGuard.tsx","default")},22234:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\ResponsiveLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\ResponsiveLayout.tsx","default")},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(88077);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},2012:()=>{}};