(()=>{var e={};e.id=7990,e.ids=[7990],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},59564:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var s=t(70260),a=t(28203),o=t(25155),n=t.n(o),i=t(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p=["",{children:["(dashboard)",{children:["graduates",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,82552)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\graduates\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\graduates\\new\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/graduates/new/page",pathname:"/graduates/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},31739:(e,r,t)=>{Promise.resolve().then(t.bind(t,82552))},42355:(e,r,t)=>{Promise.resolve().then(t.bind(t,8684))},8684:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(45512),a=t(58009),o=t(79334),n=t(30855);function i(){let e=(0,o.useRouter)(),[r,t]=(0,a.useState)(!1),i=async(r,s)=>{t(!0);try{let t="";if(s){let e=new FormData;e.append("file",s),e.append("upload_preset","your_upload_preset");let r=await fetch("https://api.cloudinary.com/v1_1/your_cloud_name/image/upload",{method:"POST",body:e});if(r.ok){let{secure_url:e}=await r.json();t=e}}(await fetch("/api/graduates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...r,certificate_file_url:t})})).ok&&e.push("/graduates")}catch(e){console.error("Error creating graduate:",e)}finally{t(!1)}};return(0,s.jsxs)("div",{className:"p-6 ",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Create New Graduate"}),(0,s.jsx)("div",{className:"bg-white rounded-lg border p-6",children:(0,s.jsx)(n.A,{onSubmit:i,isLoading:r})})]})}},82552:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\graduates\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\graduates\\new\\page.tsx","default")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,8403,7834,9267,4873,5543],()=>t(59564));module.exports=s})();