(()=>{var e={};e.id=8579,e.ids=[8579],e.modules={28303:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=28303,e.exports=t},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},2288:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>u});var i=r(42706),o=r(28203),n=r(45994),a=r(39187),p=r(62545);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("status"),s=parseInt(t.get("limit")||"50"),i=parseInt(t.get("offset")||"0"),o=`
      SELECT 
        application_id, applicant_name, email, phone, university_organization,
        project_title, innovation_type, development_stage, status, application_date
      FROM innovation_lab_applications
    `,n=[];r&&(o+=" WHERE status = ?",n.push(r)),o+=" ORDER BY application_date DESC LIMIT ? OFFSET ?",n.push(s,i);let[u]=await p.A.query(o,n);return a.NextResponse.json({applications:u,pagination:{limit:s,offset:i,total:u.length}})}catch(e){return console.error("Error fetching innovation lab applications:",e),a.NextResponse.json({error:"An error occurred while fetching applications"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/innovation-lab-applications/route",pathname:"/api/innovation-lab-applications",filename:"route",bundlePath:"app/api/innovation-lab-applications/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\innovation-lab-applications\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=c;function v(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},96487:()=>{},78335:()=>{},62545:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(60820),i=r(29021),o=r.n(i),n=r(33873),a=r.n(n);let p=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(a().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452,820],()=>r(2288));module.exports=s})();