(()=>{var e={};e.id=6230,e.ids=[6230],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},89324:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=r(70260),a=r(28203),n=r(25155),i=r.n(n),l=r(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);let o=["",{children:["(dashboard)",{children:["innovation-lab-applications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43436)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\innovation-lab-applications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\innovation-lab-applications\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/innovation-lab-applications/page",pathname:"/innovation-lab-applications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},508:(e,t,r)=>{Promise.resolve().then(r.bind(r,43436))},10236:(e,t,r)=>{Promise.resolve().then(r.bind(r,96040))},96040:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(45512),a=r(58009),n=r(87021),i=r(13393),l=r(77252),d=r(82446),o=r(79041),c=r(43161),p=r(55469),x=r(25637),m=r(62673),h=r(73826),b=r(28531),u=r.n(b);function g(){let[e,t]=(0,a.useState)([]),[r,b]=(0,a.useState)([]),[g,v]=(0,a.useState)(!1),[j,y]=(0,a.useState)("all"),f=async()=>{try{let e=await fetch("/api/innovation-lab-applications"),r=await e.json();t(r.applications||[])}catch(e){console.error("Error fetching innovation lab applications:",e)}},w=async e=>{if(confirm("Are you sure you want to delete this application?")){v(!0);try{(await fetch(`/api/innovation-lab-applications/${e}`,{method:"DELETE"})).ok&&f()}catch(e){console.error("Error deleting innovation lab application:",e)}finally{v(!1)}}},N=e=>{let t={pending:{variant:"secondary",label:"Pending",className:"bg-yellow-100 text-yellow-800"},reviewed:{variant:"outline",label:"Reviewed",className:"bg-blue-100 text-blue-800"},accepted:{variant:"default",label:"Accepted",className:"bg-green-100 text-green-800"},rejected:{variant:"destructive",label:"Rejected",className:"bg-red-100 text-red-800"},"in-progress":{variant:"default",label:"In Progress",className:"bg-purple-100 text-purple-800"},completed:{variant:"default",label:"Completed",className:"bg-emerald-100 text-emerald-800"}},r=t[e]||t.pending;return(0,s.jsx)(l.E,{className:r.className,children:r.label})},A=e=>(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{software:"bg-blue-100 text-blue-800",hardware:"bg-orange-100 text-orange-800","ai-ml":"bg-purple-100 text-purple-800",biotech:"bg-green-100 text-green-800",fintech:"bg-yellow-100 text-yellow-800",edtech:"bg-indigo-100 text-indigo-800",cleantech:"bg-emerald-100 text-emerald-800",other:"bg-gray-100 text-gray-800"}[e]||"bg-gray-100 text-gray-800"}`,children:e?e.charAt(0).toUpperCase()+e.slice(1).replace("-","/"):"N/A"}),k={all:e.length,pending:e.filter(e=>"pending"===e.status).length,reviewed:e.filter(e=>"reviewed"===e.status).length,accepted:e.filter(e=>"accepted"===e.status).length,inProgress:e.filter(e=>"in-progress"===e.status).length,completed:e.filter(e=>"completed"===e.status).length};return(0,s.jsxs)("div",{className:"p-6 space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Innovation Lab Applications"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage innovation lab applications and track project progress"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-6 gap-6",children:[(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Applications"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.all})]}),(0,s.jsx)(c.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Pending"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.pending})]}),(0,s.jsx)(p.A,{className:"h-8 w-8 text-yellow-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Reviewed"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.reviewed})]}),(0,s.jsx)(d.A,{className:"h-8 w-8 text-blue-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Accepted"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.accepted})]}),(0,s.jsx)(x.A,{className:"h-8 w-8 text-green-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"In Progress"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.inProgress})]}),(0,s.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})]})}),(0,s.jsx)("div",{className:"bg-white p-6 rounded-lg border shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:k.completed})]}),(0,s.jsx)(h.A,{className:"h-8 w-8 text-emerald-600"})]})})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border shadow-sm",children:[(0,s.jsx)("div",{className:"border-b",children:(0,s.jsxs)("nav",{className:"flex space-x-8 px-6",children:[(0,s.jsxs)("button",{type:"button",onClick:()=>y("all"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"all"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["All Applications (",k.all,")"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>y("pending"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"pending"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Pending (",k.pending,")"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>y("reviewed"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"reviewed"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Reviewed (",k.reviewed,")"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>y("accepted"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"accepted"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Accepted (",k.accepted,")"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>y("in-progress"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"in-progress"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["In Progress (",k.inProgress,")"]}),(0,s.jsxs)("button",{type:"button",onClick:()=>y("completed"),className:`py-4 px-1 border-b-2 font-medium text-sm ${"completed"===j?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:["Completed (",k.completed,")"]})]})}),(0,s.jsx)("div",{className:"p-6",children:g?(0,s.jsx)("div",{className:"text-center py-8",children:"Loading applications..."}):0===r.length?(0,s.jsx)("div",{className:"text-center py-8",children:(0,s.jsx)("p",{children:"No applications found for this status."})}):(0,s.jsx)(({applications:e})=>(0,s.jsxs)(i.XI,{children:[(0,s.jsx)(i.A0,{children:(0,s.jsxs)(i.Hj,{children:[(0,s.jsx)(i.nd,{children:"Applicant"}),(0,s.jsx)(i.nd,{children:"Project Title"}),(0,s.jsx)(i.nd,{children:"Innovation Type"}),(0,s.jsx)(i.nd,{children:"Stage"}),(0,s.jsx)(i.nd,{children:"Status"}),(0,s.jsx)(i.nd,{children:"Applied Date"}),(0,s.jsx)(i.nd,{children:"Actions"})]})}),(0,s.jsx)(i.BF,{children:e.map(e=>(0,s.jsxs)(i.Hj,{children:[(0,s.jsx)(i.nA,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.applicant_name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,s.jsx)(i.nA,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium max-w-xs truncate",title:e.project_title,children:e.project_title}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.university_organization})]})}),(0,s.jsx)(i.nA,{children:A(e.innovation_type)}),(0,s.jsx)(i.nA,{children:(0,s.jsx)("span",{className:"capitalize text-sm",children:e.development_stage.replace("-"," ")})}),(0,s.jsx)(i.nA,{children:N(e.status)}),(0,s.jsx)(i.nA,{children:new Date(e.application_date).toLocaleDateString()}),(0,s.jsx)(i.nA,{children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(u(),{href:`/innovation-lab-applications/${e.application_id}`,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",children:(0,s.jsx)(d.A,{className:"w-4 h-4"})})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>w(e.application_id),disabled:g,children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})]})})]},e.application_id))})]}),{applications:r})})]})]})}},77252:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(45512);r(58009);var a=r(21643),n=r(59462);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,s.jsx)("div",{className:(0,n.cn)(i({variant:t}),e),...r})}},43436:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\innovation-lab-applications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\innovation-lab-applications\\page.tsx","default")},25637:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},73826:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55469:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},82446:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},79041:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,8403,7834,9267,4873,2449],()=>r(89324));module.exports=s})();