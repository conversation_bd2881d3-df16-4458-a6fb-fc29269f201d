(()=>{var e={};e.id=4068,e.ids=[4068],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},53588:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>l});var t=s(70260),a=s(28203),n=s(25155),o=s.n(n),i=s(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(r,d);let l=["",{children:["(dashboard)",{children:["downloadables",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94622)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/downloadables/page",pathname:"/downloadables",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},72265:(e,r,s)=>{Promise.resolve().then(s.bind(s,94622))},18206:(e,r,s)=>{Promise.resolve().then(s.bind(s,30282))},30282:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>u});var t=s(45512),a=s(58009),n=s(87021),o=s(13393),i=s(46335),d=s(52975),l=s(95758),c=s(79041),p=s(28531),h=s.n(p);function u(){let[e,r]=(0,a.useState)([]),s=async()=>{try{let e=await fetch("/api/downloadables"),s=await e.json();r(s)}catch(e){console.error("Error fetching downloadable files:",e)}},p=async e=>{if(confirm("Are you sure you want to delete this file?"))try{(await fetch(`/api/downloadables/${e}`,{method:"DELETE"})).ok&&s()}catch(e){console.error("Error deleting file:",e)}},u=e=>e.split(".").pop()?.toLowerCase()||"";return(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Resource File Management"}),(0,t.jsx)(h(),{href:"/downloadables/new",children:(0,t.jsxs)(n.$,{children:[(0,t.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Add New File"]})})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg border",children:(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nd,{children:"File Name"}),(0,t.jsx)(o.nd,{children:"Type"}),(0,t.jsx)(o.nd,{children:"Course"}),(0,t.jsx)(o.nd,{children:"Upload Date"}),(0,t.jsx)(o.nd,{children:"Actions"})]})}),(0,t.jsx)(o.BF,{children:e.map(e=>(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{children:e.file_name}),(0,t.jsx)(o.nA,{children:u(e.file_url)}),(0,t.jsx)(o.nA,{children:e.course_title||"N/A"}),(0,t.jsx)(o.nA,{children:new Date(e.upload_date).toLocaleDateString()}),(0,t.jsxs)(o.nA,{className:"flex space-x-2",children:[(0,t.jsx)("a",{href:e.file_url,target:"_blank",rel:"noopener noreferrer",children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(d.A,{className:"w-4 h-4"})})}),(0,t.jsx)(h(),{href:`/downloadables/${e.file_id}/edit`,children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(l.A,{className:"w-4 h-4"})})}),(0,t.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>p(e.file_id),children:(0,t.jsx)(c.A,{className:"w-4 h-4"})})]})]},e.file_id))})]})})]})}},94622:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\downloadables\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\downloadables\\page.tsx","default")},95758:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(94825).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},46335:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(94825).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},79041:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,8403,7834,9267,4873,2449],()=>s(53588));module.exports=t})();