(()=>{var e={};e.id=6950,e.ids=[6950],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},10121:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(42706),i=t(28203),u=t(45994),o=t(39187),a=t(62545),c=t(45369);async function p(e){try{let r=await (0,c.B$)(e);if(r)return r;let[t]=await a.A.query("SELECT admin_id, email, full_name FROM admin_users ORDER BY full_name");return o.NextResponse.json({users:t})}catch(e){return console.error("Get users error:",e),o.NextResponse.json({error:"An error occurred while fetching users"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:x}=d;function v(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},96487:()=>{},78335:()=>{},45369:(e,r,t)=>{"use strict";t.d(r,{B$:()=>v,Er:()=>d,HU:()=>l,dD:()=>_,nG:()=>x,nr:()=>m});var s=t(5486),n=t.n(s),i=t(43008),u=t.n(i),o=t(44512),a=t(39187),c=t(62545);let p=process.env.JWT_SECRET||"your-secret-key-change-this-in-production";async function d(e){return n().hash(e,10)}function l(e){return u().sign({id:e.admin_id,email:e.email,is_super_admin:e.is_super_admin},p,{expiresIn:"24h"})}function m(e){try{return u().verify(e,p)}catch{return null}}async function x(){let e=(0,o.UL)(),r=(await e).get("admin_token")?.value;if(!r)return null;let t=m(r);if(!t)return null;try{let[e]=await c.A.query("SELECT admin_id, email, full_name, is_super_admin FROM admin_users WHERE admin_id = ?",[t.id]);if(0===e.length)return null;return e[0]}catch(e){return console.error("Error fetching current admin:",e),null}}async function v(e){let r=e.cookies.get("admin_token")?.value;return r?m(r)?null:a.NextResponse.json({error:"Invalid token"},{status:401}):a.NextResponse.json({error:"Unauthorized"},{status:401})}async function _(e){let r=e.cookies.get("admin_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=m(r);return t&&t.is_super_admin?null:a.NextResponse.json({error:"Forbidden: Requires super admin privileges"},{status:403})}},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60820),n=t(29021),i=t.n(n),u=t(33873),o=t.n(u);let a=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(o().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820,8935],()=>t(10121));module.exports=s})();