exports.id=5543,exports.ids=[5543,8273],exports.modules={94168:(e,t,r)=>{Promise.resolve().then(r.bind(r,60189)),Promise.resolve().then(r.bind(r,22234)),Promise.resolve().then(r.bind(r,56814))},3896:(e,t,r)=>{Promise.resolve().then(r.bind(r,52401)),Promise.resolve().then(r.bind(r,58783)),Promise.resolve().then(r.bind(r,91542))},98167:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},51311:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},52401:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var s=r(45512),i=r(58009),n=r(79334),a=r(91542);let l=({children:e})=>{let t=(0,n.useRouter)(),[r,l]=(0,i.useState)(null);return((0,i.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/admin/auth/check",{method:"GET",credentials:"include",cache:"no-store"});if(!e.ok)throw Error("Not authenticated");let t=await e.json();console.log("Auth check response:",t),l(!0)}catch(e){console.error("Authentication check failed:",e),l(!1),a.o.error("Please log in to access the dashboard"),t.push("/")}})()},[t]),null===r)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"})}):r?(0,s.jsx)(s.Fragment,{children:e}):null}},30855:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(45512),i=r(58009),n=r(25409),a=r(87021),l=r(53261),o=r(79041);let c=({initialData:e,onSubmit:t,isLoading:r})=>{let[c,d]=(0,i.useState)({course_id:e?.course_id?Number(e.course_id):void 0,name:e?.name||"",email:e?.email||"",cell_number:e?.cell_number||"",year_of_completion:e?.year_of_completion||new Date().getFullYear(),period_of_study:e?.period_of_study||"",final_score:e?.final_score||"",certificate_number:e?.certificate_number||"",projects:e?.projects||[{project_title:"",description:"",github_url:"",project_url:"",technologies_used:"",completion_date:new Date}],social_links:e?.social_links||[{platform:"github",url:""}]}),[u,m]=(0,i.useState)(null),[p,f]=(0,i.useState)(null),h=e=>{let{name:t,value:r}=e.target;d(e=>({...e,[t]:r}))},b=(e,t,r)=>{let s=[...c.projects];s[e]={...s[e],[t]:"completion_date"===t?new Date(r):r},d(e=>({...e,projects:s}))},x=(e,t,r)=>{let s=[...c.social_links];s[e]={...s[e],[t]:r},d(e=>({...e,social_links:s}))},g=e=>{d(t=>({...t,projects:t.projects.filter((t,r)=>r!==e)}))},v=e=>{d(t=>({...t,social_links:t.social_links.filter((t,r)=>r!==e)}))},j=async e=>{e.preventDefault();let r=new FormData;u&&r.append("certificate_file",u),p&&r.append("graduate_image",p),r.append("graduateData",JSON.stringify({course_id:c.course_id,name:c.name,email:c.email,cell_number:c.cell_number,year_of_completion:c.year_of_completion,period_of_study:c.period_of_study,final_score:"string"==typeof c.final_score?Number(c.final_score):c.final_score,certificate_number:c.certificate_number})),r.append("projects",JSON.stringify(c.projects)),r.append("social_links",JSON.stringify(c.social_links));try{if(!(await fetch("/api/graduates",{method:"POST",body:r})).ok)throw Error("Failed to submit form");t&&await t({...c,course_id:"string"==typeof c.course_id?Number(c.course_id):c.course_id,final_score:"string"==typeof c.final_score?Number(c.final_score):c.final_score},u)}catch(e){console.error("Error submitting form:",e)}};return(0,s.jsxs)("form",{onSubmit:j,encType:"multipart/form-data",className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"name",children:"Name"}),(0,s.jsx)(n.p,{id:"name",name:"name",value:c.name,onChange:h,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(n.p,{id:"email",name:"email",type:"email",value:c.email,onChange:h,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"cell_number",children:"Cell Number"}),(0,s.jsx)(n.p,{id:"cell_number",name:"cell_number",value:c.cell_number,onChange:h})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"year_of_completion",children:"Year of Completion"}),(0,s.jsx)(n.p,{id:"year_of_completion",name:"year_of_completion",type:"number",value:c.year_of_completion,onChange:h,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"period_of_study",children:"Period of Study"}),(0,s.jsx)(n.p,{id:"period_of_study",name:"period_of_study",value:c.period_of_study,onChange:h,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"final_score",children:"Final Score"}),(0,s.jsx)(n.p,{id:"final_score",name:"final_score",type:"number",step:"0.01",value:c.final_score,onChange:h})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"certificate_number",children:"Certificate Number"}),(0,s.jsx)(n.p,{id:"certificate_number",name:"certificate_number",value:c.certificate_number,onChange:h,required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"certificate_file",children:"Certificate File"}),(0,s.jsx)(n.p,{id:"certificate_file",name:"certificate_file",type:"file",onChange:e=>m(e.target.files?.[0]||null),accept:".pdf,.jpg,.jpeg,.png"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"graduate_image",children:"Graduate Image"}),(0,s.jsx)(n.p,{id:"graduate_image",name:"graduate_image",type:"file",onChange:e=>f(e.target.files?.[0]||null),accept:"image/*"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Projects"}),(0,s.jsx)(a.$,{type:"button",onClick:()=>{d(e=>({...e,projects:[...e.projects,{project_title:"",description:"",github_url:"",project_url:"",technologies_used:"",completion_date:new Date}]}))},children:"Add Project"})]}),c.projects.map((e,t)=>(0,s.jsxs)("div",{className:"border p-4 rounded-lg space-y-4",children:[(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(a.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>g(t),children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Project Title"}),(0,s.jsx)(n.p,{value:e.project_title,onChange:e=>b(t,"project_title",e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"GitHub URL"}),(0,s.jsx)(n.p,{value:e.github_url,onChange:e=>b(t,"github_url",e.target.value),required:!0})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Project URL"}),(0,s.jsx)(n.p,{value:e.project_url,onChange:e=>b(t,"project_url",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Technologies Used"}),(0,s.jsx)(n.p,{value:e.technologies_used,onChange:e=>b(t,"technologies_used",e.target.value)})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Completion Date"}),(0,s.jsx)(n.p,{type:"date",value:e.completion_date?e.completion_date.toISOString().split("T")[0]:"",onChange:e=>b(t,"completion_date",e.target.value)})]})]})]},t))]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Social Links"}),(0,s.jsx)(a.$,{type:"button",onClick:()=>{d(e=>({...e,social_links:[...e.social_links,{platform:"github",url:""}]}))},children:"Add Social Link"})]}),c.social_links.map((e,t)=>(0,s.jsxs)("div",{className:"flex gap-4 items-end",children:[(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)(l.J,{children:"Platform"}),(0,s.jsxs)("select",{name:"platform",value:e.platform,onChange:e=>x(t,"platform",e.target.value),className:"w-full border rounded-md p-2",title:"Select social media platform",children:[(0,s.jsx)("option",{value:"github",children:"GitHub"}),(0,s.jsx)("option",{value:"linkedin",children:"LinkedIn"}),(0,s.jsx)("option",{value:"twitter",children:"Twitter"}),(0,s.jsx)("option",{value:"portfolio",children:"Portfolio"}),(0,s.jsx)("option",{value:"other",children:"Other"})]})]}),(0,s.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,s.jsx)(l.J,{children:"URL"}),(0,s.jsx)(n.p,{value:e.url,onChange:e=>x(t,"url",e.target.value),required:!0})]}),(0,s.jsx)(a.$,{type:"button",variant:"destructive",size:"sm",onClick:()=>v(t),children:(0,s.jsx)(o.A,{className:"w-4 h-4"})})]},t))]}),(0,s.jsx)(a.$,{type:"submit",disabled:r,children:r?"Saving...":"Save Graduate"})]})}},58783:(e,t,r)=>{"use strict";r.d(t,{default:()=>A});var s=r(45512),i=r(58009),n=r(28531),a=r.n(n),l=r(38440),o=r(1422),c=r(57631),d=r(35603),u=r(43161),m=r(62673),p=r(28650),f=r(52975),h=r(39327),b=r(55817),x=r(53100),g=r(93346),v=r(6472),j=r(51255),y=r(35120),_=r(48320),w=r(79334),N=r(91542);let k=({onToggle:e})=>(0,s.jsxs)("div",{className:"md:hidden bg-white border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30",children:[(0,s.jsx)("button",{type:"button",onClick:e,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Toggle navigation menu",title:"Toggle navigation menu",children:(0,s.jsx)(_.A,{className:"w-6 h-6"})}),(0,s.jsx)("h1",{className:"font-semibold text-lg",children:"Brissp Dashboard"}),(0,s.jsx)("div",{className:"w-10"})," "]}),C=({isOpen:e=!1,onToggle:t})=>{let r=(0,w.useRouter)(),[n,_]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{_(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,i.useEffect)(()=>{let r=r=>{if(n&&e&&t){let e=document.getElementById("mobile-sidebar");e&&!e.contains(r.target)&&t()}};if(n&&e)return document.addEventListener("mousedown",r),()=>document.removeEventListener("mousedown",r)},[n,e,t]),(0,i.useEffect)(()=>(n&&e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[n,e]);let k=async()=>{try{(await fetch("/api/admin/auth/logout",{method:"POST",credentials:"include"})).ok?(N.o.success("Logged out successfully"),r.push("/")):N.o.error("Failed to log out")}catch(e){console.error("Logout error:",e),N.o.error("An error occurred during logout")}},C=()=>{n&&t&&t()},A=[{href:"/panel",icon:l.A,label:"Dashboard"},{href:"/applications",icon:o.A,label:"Course Applications"},{href:"/pitch-deck-applications",icon:c.A,label:"Pitch Deck Applications"},{href:"/internship-applications",icon:d.A,label:"Internship Applications"},{href:"/fyp-applications",icon:u.A,label:"FYP Applications"},{href:"/innovation-lab-applications",icon:m.A,label:"Innovation Lab Applications"},{href:"/results",icon:p.A,label:"Results"},{href:"/downloadables",icon:f.A,label:"Resources"},{href:"/courses",icon:h.A,label:"Courses"},{href:"/curriculum",icon:b.A,label:"Curriculum"},{href:"/notice-board",icon:x.A,label:"Notice Board"},{href:"/graduates",icon:g.A,label:"Graduates"},{href:"/password-management",icon:v.A,label:"Password Management"}];return(0,s.jsxs)(s.Fragment,{children:[n&&e&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:t}),(0,s.jsxs)("div",{id:"mobile-sidebar",className:`
          ${n?`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`:"fixed inset-y-0 left-0 w-64"}
          border-r bg-white h-screen flex flex-col
        `,children:[(0,s.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,s.jsx)(a(),{href:"/panel",className:"flex items-center space-x-2",onClick:C,children:(0,s.jsx)("span",{className:"font-semibold text-lg",children:"Brissp Dash"})}),n&&(0,s.jsx)("button",{type:"button",onClick:t,className:"p-2 rounded-lg hover:bg-gray-100 md:hidden","aria-label":"Close navigation menu",title:"Close navigation menu",children:(0,s.jsx)(j.A,{className:"w-5 h-5"})})]}),(0,s.jsx)("nav",{className:"p-4 space-y-2 flex-grow overflow-y-auto",children:A.map(e=>{let t=e.icon;return(0,s.jsxs)(a(),{href:e.href,onClick:C,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200",children:[(0,s.jsx)(t,{className:"w-5 h-5 flex-shrink-0"}),(0,s.jsx)("span",{className:"truncate",children:e.label})]},e.href)})}),(0,s.jsx)("div",{className:"p-4 border-t mt-auto",children:(0,s.jsxs)("button",{type:"button",onClick:k,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full transition-colors duration-200",children:[(0,s.jsx)(y.A,{className:"w-5 h-5 flex-shrink-0"}),(0,s.jsx)("span",{children:"Logout"})]})})]})]})},A=({children:e})=>{let[t,r]=(0,i.useState)(!1),[n,a]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;a(e),e||r(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let l=()=>{r(!t)};return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsx)(k,{onToggle:l}),(0,s.jsx)(C,{isOpen:t,onToggle:l}),(0,s.jsx)("div",{className:`
        transition-all duration-300 ease-in-out
        ${n?"ml-0":"ml-64"}
      `,children:(0,s.jsx)("main",{className:"min-h-screen",children:e})})]})}},87021:(e,t,r)=>{"use strict";r.d(t,{$:()=>c});var s=r(45512),i=r(58009),n=r(12705),a=r(21643),l=r(59462);let o=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...a},c)=>{let d=i?n.DX:"button";return(0,s.jsx)(d,{className:(0,l.cn)(o({variant:t,size:r,className:e})),ref:c,...a})});c.displayName="Button"},25409:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(45512),i=r(58009),n=r(59462);let a=i.forwardRef(({className:e,type:t,...r},i)=>(0,s.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:i,...r}));a.displayName="Input"},53261:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(45512),i=r(58009),n=r(18055),a=i.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=r(21643),o=r(59462);let c=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=i.forwardRef(({className:e,...t},r)=>(0,s.jsx)(a,{ref:r,className:(0,o.cn)(c(),e),...t}));d.displayName=a.displayName},59462:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(82281),i=r(94805);function n(...e){return(0,i.QP)((0,s.$)(e))}},71975:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var s=r(62740),i=r(41583),n=r.n(i),a=r(76499),l=r.n(a);r(2012);var o=r(22234),c=r(60189),d=r(56814);let u={title:"Brissp Admin",description:"Brissp Academy Admin Dashboard"};function m({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsxs)("body",{className:`${n().variable} ${l().variable} antialiased`,children:[(0,s.jsx)(c.default,{children:(0,s.jsx)(o.default,{children:e})}),(0,s.jsx)(d.Toaster,{})]})})}},60189:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\AuthGuard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\AuthGuard.tsx","default")},22234:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\ResponsiveLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\ResponsiveLayout.tsx","default")},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(88077);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},2012:()=>{},18055:(e,t,r)=>{"use strict";r.d(t,{sG:()=>u,hO:()=>m});var s=r(58009),i=r(55740),n=r(29952),a=r(45512),l=s.forwardRef((e,t)=>{let{children:r,...i}=e,n=s.Children.toArray(r),l=n.find(d);if(l){let e=l.props.children,r=n.map(t=>t!==l?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(o,{...i,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,a.jsx)(o,{...i,ref:t,children:r})});l.displayName="Slot";var o=s.forwardRef((e,t)=>{let{children:r,...i}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return s.cloneElement(r,{...function(e,t){let r={...t};for(let s in t){let i=e[s],n=t[s];/^on[A-Z]/.test(s)?i&&n?r[s]=(...e)=>{n(...e),i(...e)}:i&&(r[s]=i):"style"===s?r[s]={...i,...n}:"className"===s&&(r[s]=[i,n].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props),ref:t?(0,n.t)(t,e):e})}return s.Children.count(r)>1?s.Children.only(null):null});o.displayName="SlotClone";var c=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function d(e){return s.isValidElement(e)&&e.type===c}var u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=s.forwardRef((e,r)=>{let{asChild:s,...i}=e,n=s?l:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function m(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},79041:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};