import { NextRequest, NextResponse } from 'next/server';
import { readFile, stat } from 'fs/promises';
import { join } from 'path';
import { fileStorage } from '@/lib/file-storage';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const resolvedParams = await params;
    const filePath = resolvedParams.path.join('/');
    const fullPath = join(process.cwd(), 'public/uploads', filePath);
    
    // Check if file exists
    const exists = await fileStorage.fileExists(fullPath);
    if (!exists) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Get file stats
    const stats = await stat(fullPath);
    
    // Read file
    const fileBuffer = await readFile(fullPath);
    
    // Determine content type based on file extension
    const extension = filePath.substring(filePath.lastIndexOf('.'));
    const contentType = getContentType(extension);
    
    // Create response with proper headers
    const response = new NextResponse(fileBuffer);
    
    // Set content headers
    response.headers.set('Content-Type', contentType);
    response.headers.set('Content-Length', stats.size.toString());
    response.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
    
    // Set CORS headers to allow access from Academy
    response.headers.set('Access-Control-Allow-Origin', '*');
    response.headers.set('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type');
    
    // Set content disposition for downloads
    if (request.nextUrl.searchParams.get('download') === 'true') {
      const filename = filePath.substring(filePath.lastIndexOf('/') + 1);
      response.headers.set('Content-Disposition', `attachment; filename="${filename}"`);
    }
    
    return response;
  } catch (error) {
    console.error('Error serving file:', error);
    return NextResponse.json(
      { error: 'Failed to serve file' },
      { status: 500 }
    );
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

function getContentType(extension: string): string {
  const contentTypes: Record<string, string> = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.txt': 'text/plain',
    '.csv': 'text/csv',
  };
  
  return contentTypes[extension.toLowerCase()] || 'application/octet-stream';
}
