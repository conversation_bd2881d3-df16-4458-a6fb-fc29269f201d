(()=>{var e={};e.id=5688,e.ids=[5688],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},36180:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>p,routeModule:()=>c,tree:()=>l});var a=t(70260),s=t(28203),o=t(25155),i=t.n(o),d=t(67292),n={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>d[e]);t.d(r,n);let l=["",{children:["(dashboard)",{children:["graduates",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8098)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\graduates\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\graduates\\[id]\\edit\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/graduates/[id]/edit/page",pathname:"/graduates/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55176:(e,r,t)=>{Promise.resolve().then(t.bind(t,8098))},18728:(e,r,t)=>{Promise.resolve().then(t.bind(t,40310))},40310:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var a=t(45512),s=t(58009),o=t(30855),i=t(79334);let d=()=>{let e=(0,i.useParams)(),r=e?.id,[t,d]=(0,s.useState)(null),[n,l]=(0,s.useState)(!0),[p,u]=(0,s.useState)(null);(0,s.useEffect)(()=>{r&&(async()=>{try{let e=await fetch(`/api/graduates/${r}`);if(!e.ok)throw Error(`Error: ${e.status}`);let t=await e.json();t.projects&&Array.isArray(t.projects)&&(t.projects=t.projects.map(e=>({...e,completion_date:e.completion_date?new Date(e.completion_date):null}))),d(t)}catch(e){console.error("Failed to fetch graduate:",e),u("Failed to load graduate data. Please try again.")}finally{l(!1)}})()},[r]);let c=async(e,t)=>{l(!0),u(null);try{let a=new FormData;t&&a.append("certificate_file",t),a.append("graduateData",JSON.stringify(e));let s=await fetch(`/api/graduates/${r}`,{method:"PUT",body:a});if(!s.ok)throw Error(`Error: ${s.status}`);window.location.href="/graduates"}catch(e){console.error("Failed to update graduate:",e),u("Failed to update graduate. Please try again."),l(!1)}};return n?(0,a.jsx)("div",{className:"p-6",children:"Loading..."}):p?(0,a.jsx)("div",{className:"p-6 text-red-500",children:p}):(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Edit Graduate"}),(0,a.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[t&&(0,a.jsx)(o.A,{initialData:t,onSubmit:c,isLoading:n}),!t&&!n&&(0,a.jsx)("div",{className:"text-red-500",children:"Graduate not found"})]})]})}},8098:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\graduates\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\graduates\\[id]\\edit\\page.tsx","default")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[638,8403,7834,9267,4873,5543],()=>t(36180));module.exports=a})();