(()=>{var e={};e.id=1408,e.ids=[1408],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},82503:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>u});var i=t(42706),n=t(28203),o=t(45994),a=t(39187),p=t(62545);async function u(){try{let[e]=await p.A.query(`SELECT COUNT(*) as count FROM information_schema.tables 
       WHERE table_schema = DATABASE() AND table_name = 'internship_applications'`);if(0===e[0].count)return a.NextResponse.json({error:"Table internship_applications does not exist",tableExists:!1});let[r]=await p.A.query("SELECT * FROM internship_applications ORDER BY application_date DESC"),[t]=await p.A.query("DESCRIBE internship_applications"),[s]=await p.A.query(`SELECT COLUMN_NAME FROM information_schema.COLUMNS
       WHERE TABLE_SCHEMA = DATABASE()
       AND TABLE_NAME = 'internship_applications'
       AND COLUMN_NAME IN ('student_name', 'student_email', 'university', 'internship_type_preference')`);return a.NextResponse.json({tableExists:!0,totalRecords:r.length,records:r,tableStructure:t,foundColumns:s.map(e=>e.COLUMN_NAME),expectedColumns:["student_name","student_email","university","internship_type_preference"],message:"Database test successful"})}catch(e){return console.error("Database test error:",e),a.NextResponse.json({error:"Database connection failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let c=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test-internships/route",pathname:"/api/test-internships",filename:"route",bundlePath:"app/api/test-internships/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\test-internships\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=c;function E(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>p});var s=t(60820),i=t(29021),n=t.n(i),o=t(33873),a=t.n(o);let p=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:n().readFileSync(a().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(82503));module.exports=s})();