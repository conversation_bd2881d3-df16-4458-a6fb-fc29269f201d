(()=>{var e={};e.id=107,e.ids=[107],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},31214:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>d});var t=a(70260),i=a(28203),r=a(25155),l=a.n(r),n=a(67292),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["(dashboard)",{children:["internship-applications",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,99591)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\internship-applications\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\internship-applications\\[id]\\page.tsx"],x={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/internship-applications/[id]/page",pathname:"/internship-applications/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55914:(e,s,a)=>{Promise.resolve().then(a.bind(a,99591))},18962:(e,s,a)=>{Promise.resolve().then(a.bind(a,78432))},78432:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>g});var t=a(45512),i=a(58009),r=a(79334),l=a(87021),n=a(97643),c=a(77252),d=a(48859),o=a(54069),x=a(71894),p=a(42092),m=a(93346),h=a(35603),u=a(94825);let j=(0,u.A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]),v=(0,u.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var f=a(28790),b=a(71398),N=a(73325);function g(){let e=(0,r.useParams)(),s=(0,r.useRouter)(),[a,u]=(0,i.useState)(null),[g,y]=(0,i.useState)(!0),[_,w]=(0,i.useState)(!1),[k,A]=(0,i.useState)(""),[P,S]=(0,i.useState)(""),R=async()=>{if(!e||!e.id){y(!1);return}try{let s=await fetch(`/api/internship-applications/${e.id}`),a=await s.json();s.ok&&(u(a.application),A(a.application.status),S(a.application.review_notes||""))}catch(e){console.error("Error fetching application:",e)}finally{y(!1)}},C=async()=>{if(a){w(!0);try{(await fetch(`/api/internship-applications/${a.application_id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:k,review_notes:P})})).ok&&(R(),alert("Application updated successfully!"))}catch(e){console.error("Error updating application:",e),alert("Error updating application")}finally{w(!1)}}};return g?(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center py-8",children:"Loading application details..."})}):a?(0,t.jsxs)("div",{className:"p-6 space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>s.back(),children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Internship Application Details"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Application #",a.application_id]})]})]}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(e=>{let s={pending:{variant:"secondary",label:"Pending",className:"bg-yellow-100 text-yellow-800"},reviewed:{variant:"outline",label:"Under Review",className:"bg-blue-100 text-blue-800"},shortlisted:{variant:"default",label:"Shortlisted",className:"bg-purple-100 text-purple-800"},accepted:{variant:"default",label:"Accepted",className:"bg-green-100 text-green-800"},rejected:{variant:"destructive",label:"Rejected",className:"bg-red-100 text-red-800"}},a=s[e]||s.pending;return(0,t.jsx)(c.E,{className:a.className,children:a.label})})(a.status)})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Applicant Information"})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Full Name"}),(0,t.jsx)("p",{className:"text-sm",children:a.applicant_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,t.jsx)("p",{className:"text-sm",children:a.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Phone"}),(0,t.jsx)("p",{className:"text-sm",children:a.phone})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"GPA"}),(0,t.jsx)("p",{className:"text-sm",children:a.gpa||"Not provided"})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Academic Information"})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"University"}),(0,t.jsx)("p",{className:"text-sm",children:a.university})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Course of Study"}),(0,t.jsx)("p",{className:"text-sm",children:a.course_of_study})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Year of Study"}),(0,t.jsx)("p",{className:"text-sm",children:a.year_of_study})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Internship Preferences"})]})}),(0,t.jsx)(n.Wu,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Type Preference"}),(0,t.jsx)("p",{className:"text-sm capitalize",children:a.internship_type_preference||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Preferred Industry"}),(0,t.jsx)("p",{className:"text-sm",children:a.preferred_industry||"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Availability Start"}),(0,t.jsx)("p",{className:"text-sm",children:a.availability_start?new Date(a.availability_start).toLocaleDateString():"Not specified"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Preferred Duration"}),(0,t.jsx)("p",{className:"text-sm",children:a.preferred_duration||"Not specified"})]})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(j,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Skills and Experience"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[a.technical_skills&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Technical Skills"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.technical_skills})]}),a.soft_skills&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Soft Skills"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.soft_skills})]}),a.relevant_experience&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Relevant Experience"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.relevant_experience})]}),a.motivation&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Motivation"}),(0,t.jsx)("p",{className:"text-sm mt-1",children:a.motivation})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(v,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Documents and Links"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[a.cv_url&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"CV/Resume"}),(0,t.jsx)("p",{className:"text-sm",children:(0,t.jsx)("a",{href:a.cv_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"View CV"})})]}),a.portfolio_url&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Portfolio"}),(0,t.jsx)("p",{className:"text-sm",children:(0,t.jsx)("a",{href:a.portfolio_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"View Portfolio"})})]}),a.linkedin_url&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"LinkedIn"}),(0,t.jsx)("p",{className:"text-sm",children:(0,t.jsx)("a",{href:a.linkedin_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"View LinkedIn"})})]}),a.github_url&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"GitHub"}),(0,t.jsx)("p",{className:"text-sm",children:(0,t.jsx)("a",{href:a.github_url,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"View GitHub"})})]})]}),a.cover_letter&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Cover Letter"}),(0,t.jsx)("p",{className:"text-sm mt-1 p-3 bg-gray-50 rounded",children:a.cover_letter})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Application Management"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Status"}),(0,t.jsxs)(o.l6,{value:k,onValueChange:A,children:[(0,t.jsx)(o.bq,{className:"mt-1",children:(0,t.jsx)(o.yv,{})}),(0,t.jsxs)(o.gC,{children:[(0,t.jsx)(o.eb,{value:"pending",children:"Pending"}),(0,t.jsx)(o.eb,{value:"reviewed",children:"Under Review"}),(0,t.jsx)(o.eb,{value:"shortlisted",children:"Shortlisted"}),(0,t.jsx)(o.eb,{value:"accepted",children:"Accepted"}),(0,t.jsx)(o.eb,{value:"rejected",children:"Rejected"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Review Notes"}),(0,t.jsx)(d.T,{value:P,onChange:e=>S(e.target.value),placeholder:"Add review notes...",className:"mt-1",rows:4})]}),(0,t.jsxs)(l.$,{onClick:C,disabled:_,className:"w-full",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 mr-2"}),_?"Updating...":"Update Application"]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.A,{className:"w-5 h-5"}),(0,t.jsx)("span",{children:"Application Timeline"})]})}),(0,t.jsxs)(n.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Applied Date"}),(0,t.jsx)("p",{className:"text-sm",children:new Date(a.application_date).toLocaleDateString()})]}),a.review_date&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Review Date"}),(0,t.jsx)("p",{className:"text-sm",children:new Date(a.review_date).toLocaleDateString()})]})]})]})]})]})]}):(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center py-8",children:"Application not found"})})}},99591:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\internship-applications\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\internship-applications\\[id]\\page.tsx","default")},28790:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(94825).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},42092:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(94825).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[638,8403,7834,9267,4873,6562,3015,6516],()=>a(31214));module.exports=t})();