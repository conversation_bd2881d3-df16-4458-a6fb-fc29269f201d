(()=>{var e={};e.id=9241,e.ids=[9241],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},41366:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>o,routeModule:()=>h,tree:()=>l});var a=t(70260),i=t(28203),r=t(25155),n=t.n(r),c=t(67292),d={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);t.d(s,d);let l=["",{children:["(dashboard)",{children:["pitch-deck-applications",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,38669)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\pitch-deck-applications\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\pitch-deck-applications\\[id]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/pitch-deck-applications/[id]/page",pathname:"/pitch-deck-applications/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},776:(e,s,t)=>{Promise.resolve().then(t.bind(t,38669))},87624:(e,s,t)=>{Promise.resolve().then(t.bind(t,42916))},42916:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y});var a=t(45512),i=t(58009),r=t(79334),n=t(87021),c=t(97643),d=t(77252),l=t(54069),o=t(48859),p=t(25409),h=t(53261),m=t(28531),x=t.n(m),u=t(71894),j=t(20111);let g=(0,t(94825).A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var v=t(25890),f=t(71398),_=t(73325);function y(){let e=(0,r.useParams)();(0,r.useRouter)();let[s,t]=(0,i.useState)(null),[m,y]=(0,i.useState)([]),[b,N]=(0,i.useState)(!0),[w,k]=(0,i.useState)(!1),[A,P]=(0,i.useState)({status:"",review_notes:"",assigned_consultant:"",project_start_date:"",project_completion_date:"",final_pitch_deck_url:"",success_metrics:""}),D=async()=>{try{N(!0);let s=await fetch(`/api/pitch-deck-applications/${e?.id}`),a=await s.json();t(a.application),y(a.progress||[]),P({status:a.application.status||"",review_notes:a.application.review_notes||"",assigned_consultant:a.application.assigned_consultant||"",project_start_date:a.application.project_start_date?new Date(a.application.project_start_date).toISOString().split("T")[0]:"",project_completion_date:a.application.project_completion_date?new Date(a.application.project_completion_date).toISOString().split("T")[0]:"",final_pitch_deck_url:a.application.final_pitch_deck_url||"",success_metrics:a.application.success_metrics||""})}catch(e){console.error("Error fetching application:",e)}finally{N(!1)}},J=async()=>{try{k(!0),(await fetch(`/api/pitch-deck-applications/${e?.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(A)})).ok?(D(),alert("Application updated successfully!")):alert("Failed to update application")}catch(e){console.error("Error updating application:",e),alert("Error updating application")}finally{k(!1)}};return b?(0,a.jsx)("div",{className:"p-6",children:"Loading application details..."}):s?(0,a.jsxs)("div",{className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(x(),{href:"/pitch-deck-applications",children:(0,a.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,a.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Back to Applications"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:s.applicant_name}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Pitch Deck Application #",s.application_id]})]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(e=>{let s={pending:{variant:"secondary",label:"Pending"},reviewed:{variant:"outline",label:"Reviewed"},accepted:{variant:"default",label:"Accepted"},rejected:{variant:"destructive",label:"Rejected"},"in-progress":{variant:"default",label:"In Progress"},completed:{variant:"default",label:"Completed"}},t=s[e]||s.pending;return(0,a.jsx)(d.E,{variant:t.variant,children:t.label})})(s.status)})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"w-5 h-5 mr-2"}),"Applicant Information"]})}),(0,a.jsx)(c.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Name"}),(0,a.jsx)("p",{className:"font-medium",children:s.applicant_name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,a.jsx)("p",{children:s.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Phone"}),(0,a.jsx)("p",{children:s.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Company"}),(0,a.jsx)("p",{children:s.company_name||"N/A"})]})]})})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center",children:[(0,a.jsx)(g,{className:"w-5 h-5 mr-2"}),"Business Information"]})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Industry"}),(0,a.jsx)("p",{children:s.industry||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Funding Stage"}),(0,a.jsx)("p",{className:"capitalize",children:s.funding_stage.replace("-"," ")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Funding Amount"}),(0,a.jsx)("p",{children:s.funding_amount||"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Team Size"}),(0,a.jsx)("p",{children:s.team_size||"N/A"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Business Description"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:s.business_description})]}),s.target_audience&&(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Target Audience"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:s.target_audience})]}),s.current_traction&&(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Current Traction"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:s.current_traction})]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"w-5 h-5 mr-2"}),"Project Requirements"]})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Budget Range"}),(0,a.jsx)("p",{className:"capitalize",children:s.budget_range})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Preferred Start Date"}),(0,a.jsx)("p",{children:s.preferred_start_date?new Date(s.preferred_start_date).toLocaleDateString():"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Pitch Deadline"}),(0,a.jsx)("p",{children:s.pitch_deadline?new Date(s.pitch_deadline).toLocaleDateString():"N/A"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Previous Funding"}),(0,a.jsx)("p",{children:s.previous_funding?"Yes":"No"})]})]}),s.specific_requirements&&(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{className:"text-sm font-medium text-gray-500",children:"Specific Requirements"}),(0,a.jsx)("p",{className:"mt-1 text-sm",children:s.specific_requirements})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Application Management"})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{htmlFor:"status",children:"Status"}),(0,a.jsxs)(l.l6,{value:A.status,onValueChange:e=>P(s=>({...s,status:e})),children:[(0,a.jsx)(l.bq,{children:(0,a.jsx)(l.yv,{placeholder:"Select status"})}),(0,a.jsxs)(l.gC,{children:[(0,a.jsx)(l.eb,{value:"pending",children:"Pending"}),(0,a.jsx)(l.eb,{value:"reviewed",children:"Reviewed"}),(0,a.jsx)(l.eb,{value:"accepted",children:"Accepted"}),(0,a.jsx)(l.eb,{value:"rejected",children:"Rejected"}),(0,a.jsx)(l.eb,{value:"in-progress",children:"In Progress"}),(0,a.jsx)(l.eb,{value:"completed",children:"Completed"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{htmlFor:"assigned_consultant",children:"Assigned Consultant"}),(0,a.jsx)(p.p,{id:"assigned_consultant",value:A.assigned_consultant,onChange:e=>P(s=>({...s,assigned_consultant:e.target.value})),placeholder:"Enter consultant name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{htmlFor:"review_notes",children:"Review Notes"}),(0,a.jsx)(o.T,{id:"review_notes",value:A.review_notes,onChange:e=>P(s=>({...s,review_notes:e.target.value})),placeholder:"Add review notes...",rows:4})]}),(0,a.jsxs)(n.$,{onClick:J,disabled:w,className:"w-full",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2"}),w?"Updating...":"Update Application"]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsxs)(c.ZB,{className:"flex items-center",children:[(0,a.jsx)(_.A,{className:"w-5 h-5 mr-2"}),"Project Timeline"]})}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{htmlFor:"project_start_date",children:"Start Date"}),(0,a.jsx)(p.p,{id:"project_start_date",type:"date",value:A.project_start_date,onChange:e=>P(s=>({...s,project_start_date:e.target.value}))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{htmlFor:"project_completion_date",children:"Completion Date"}),(0,a.jsx)(p.p,{id:"project_completion_date",type:"date",value:A.project_completion_date,onChange:e=>P(s=>({...s,project_completion_date:e.target.value}))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{htmlFor:"final_pitch_deck_url",children:"Final Pitch Deck URL"}),(0,a.jsx)(p.p,{id:"final_pitch_deck_url",value:A.final_pitch_deck_url,onChange:e=>P(s=>({...s,final_pitch_deck_url:e.target.value})),placeholder:"https://..."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(h.J,{htmlFor:"success_metrics",children:"Success Metrics"}),(0,a.jsx)(o.T,{id:"success_metrics",value:A.success_metrics,onChange:e=>P(s=>({...s,success_metrics:e.target.value})),placeholder:"Document success metrics...",rows:3})]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsx)(c.aR,{children:(0,a.jsx)(c.ZB,{children:"Application Info"})}),(0,a.jsxs)(c.Wu,{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Applied:"}),(0,a.jsx)("span",{children:new Date(s.application_date).toLocaleDateString()})]}),s.review_date&&(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Reviewed:"}),(0,a.jsx)("span",{children:new Date(s.review_date).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Last Updated:"}),(0,a.jsx)("span",{children:new Date(s.updated_at).toLocaleDateString()})]})]})]})]})]})]}):(0,a.jsx)("div",{className:"p-6",children:"Application not found"})}},25409:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(45512),i=t(58009),r=t(59462);let n=i.forwardRef(({className:e,type:s,...t},i)=>(0,a.jsx)("input",{type:s,className:(0,r.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:i,...t}));n.displayName="Input"},53261:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var a=t(45512),i=t(58009),r=t(18055),n=i.forwardRef((e,s)=>(0,a.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var c=t(21643),d=t(59462);let l=(0,c.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=i.forwardRef(({className:e,...s},t)=>(0,a.jsx)(n,{ref:t,className:(0,d.cn)(l(),e),...s}));o.displayName=n.displayName},38669:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\pitch-deck-applications\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\pitch-deck-applications\\[id]\\page.tsx","default")},25890:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(94825).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},20111:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(94825).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[638,8403,7834,9267,4873,6562,3015,6516],()=>t(41366));module.exports=a})();