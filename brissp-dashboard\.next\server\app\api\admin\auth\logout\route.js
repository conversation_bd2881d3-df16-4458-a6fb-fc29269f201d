(()=>{var e={};e.id=4049,e.ids=[4049],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},6039:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{POST:()=>i});var o=t(42706),a=t(28203),n=t(45994),u=t(39187);async function i(e){try{let e=u.NextResponse.json({message:"Logged out successfully"});return e.cookies.set({name:"admin_token",value:"",httpOnly:!0,secure:!0,maxAge:0,path:"/",sameSite:"lax"}),e}catch(e){return console.error("Logout error:",e),u.NextResponse.json({error:"An error occurred during logout"},{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/auth/logout/route",pathname:"/api/admin/auth/logout",filename:"route",bundlePath:"app/api/admin/auth/logout/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\admin\\auth\\logout\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},96487:()=>{},78335:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452],()=>t(6039));module.exports=s})();