(()=>{var e={};e.id=453,e.ids=[453],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},12082:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>p});var o=t(42706),i=t(28203),n=t(45994),a=t(39187),u=t(62545);async function c(){try{let[e]=await u.A.query(`
      SELECT f.*, c.title as course_title
      FROM downloadable_files f
      LEFT JOIN courses c ON f.course_id = c.course_id
      ORDER BY f.upload_date DESC
    `);return a.NextResponse.json(e)}catch(e){return console.error("Error fetching files:",e),a.NextResponse.json({error:"Failed to fetch files"},{status:500})}}async function p(e){try{let r=await e.json(),{file_name:t,file_url:s,description:o,course_id:i,file_type:n,file_size:c}=r;if(!t||!s)return a.NextResponse.json({error:"File name and URL are required"},{status:400});let[p]=await u.A.query(`INSERT INTO downloadable_files 
       (file_name, file_url, description, course_id, file_type, file_size) 
       VALUES (?, ?, ?, ?, ?, ?)`,[t,s,o||null,i||null,n||null,c||null]);return a.NextResponse.json({id:p.insertId,...r},{status:201})}catch(e){return console.error("Error creating file:",e),a.NextResponse.json({error:"Failed to create file"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/downloadables/route",pathname:"/api/downloadables",filename:"route",bundlePath:"app/api/downloadables/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\downloadables\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:f}=l;function q(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(60820),o=t(29021),i=t.n(o),n=t(33873),a=t.n(n);let u=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(a().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(12082));module.exports=s})();