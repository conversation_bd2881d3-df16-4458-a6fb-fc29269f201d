(()=>{var e={};e.id=7595,e.ids=[7595],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},15356:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(70260),i=r(28203),n=r(25155),a=r.n(n),l=r(67292),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(s,o);let d=["",{children:["(dashboard)",{children:["courses",{children:["refresher",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87425)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\refresher\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\refresher\\[id]\\edit\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/courses/refresher/[id]/edit/page",pathname:"/courses/refresher/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8054:(e,s,r)=>{Promise.resolve().then(r.bind(r,87425))},71262:(e,s,r)=>{Promise.resolve().then(r.bind(r,38613))},38613:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(45512),i=r(58009),n=r(79334),a=r(55757);function l(){let e=(0,n.useRouter)(),s=(0,n.useParams)(),[r,l]=(0,i.useState)(null),[o,d]=(0,i.useState)(!1),[c,u]=(0,i.useState)(!0),m=async(r,t)=>{d(!0);try{let i=r.image_url;if(t){let e=new FormData;e.append("file",t);let s=await fetch("/api/upload",{method:"POST",body:e});if(s.ok){let{url:e}=await s.json();i=e}}(await fetch(`/api/courses/${s.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({...r,image_url:i})})).ok&&e.push("/courses")}catch(e){console.error("Error updating refresher course:",e)}finally{d(!1)}};return c?(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Loading course data..."})}):r?"refresher"!==r.program_type?(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center text-red-600",children:"This is not a refresher course. Please use the regular course edit page."})}):(0,t.jsxs)("div",{className:"p-6 ",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Edit Refresher Course"}),(0,t.jsx)("div",{className:"bg-white rounded-lg border p-6",children:(0,t.jsx)(a.A,{initialData:r,onSubmit:m,isLoading:o})})]}):(0,t.jsx)("div",{className:"p-6",children:(0,t.jsx)("div",{className:"text-center text-red-600",children:"Course not found"})})}},55757:(e,s,r)=>{"use strict";r.d(s,{A:()=>d});var t=r(45512),i=r(58009),n=r(87021),a=r(25409),l=r(47920),o=r(27042);function d({initialData:e,onSubmit:s,isLoading:r}){let[d,c]=(0,i.useState)(e||{title:"",course_code:"",description:"",duration_months:0,price:0,department:"Computer Science",category:"adults",program_type:"refresher",num_lectures:0,skill_level:"intermediate",languages:"English",class_days:"Evenings & Weekends"}),[u,m]=(0,i.useState)(null),p=(0,l.hG)({extensions:[o.A],content:d.description,onUpdate:({editor:e})=>{c(s=>({...s,description:e.getHTML()}))}}),h=e=>{let{name:s,value:r}=e.target;c(e=>({...e,[s]:"duration_months"===s||"price"===s||"num_lectures"===s?Number(r):r}))};return(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s(d,u||void 0)},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-blue-800 mb-2",children:"Refresher Course"}),(0,t.jsx)("p",{className:"text-blue-700 text-sm",children:"This form is specifically designed for creating refresher courses. These courses are typically designed for professionals looking to update their skills or refresh their knowledge in a specific field."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Course Title"}),(0,t.jsx)(a.p,{name:"title",value:d.title,onChange:h,placeholder:"e.g., Data Science Bootcamp",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Course Code"}),(0,t.jsx)(a.p,{name:"course_code",value:d.course_code,onChange:h,placeholder:"e.g., REF-DS-001",required:!0}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Use REF- prefix for refresher courses"})]}),(0,t.jsxs)("div",{className:"col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Description"}),(0,t.jsxs)("div",{className:"border rounded-md p-2",children:[(0,t.jsxs)("div",{className:"border-b p-2 mb-2 flex gap-2",children:[(0,t.jsx)("button",{type:"button",onClick:()=>p?.chain().focus().toggleBold().run(),className:`p-2 ${p?.isActive("bold")?"bg-gray-200":""} rounded`,children:(0,t.jsx)("strong",{children:"B"})}),(0,t.jsx)("button",{type:"button",onClick:()=>p?.chain().focus().toggleItalic().run(),className:`p-2 ${p?.isActive("italic")?"bg-gray-200":""} rounded`,children:(0,t.jsx)("em",{children:"I"})})]}),(0,t.jsx)(l.$Z,{editor:p,className:"min-h-[100px] p-2"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Duration (Months)"}),(0,t.jsx)(a.p,{type:"number",name:"duration_months",value:d.duration_months,onChange:h,min:"1",max:"12",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Price ($)"}),(0,t.jsx)(a.p,{type:"number",name:"price",value:d.price,onChange:h,min:"0",step:"0.01",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Department"}),(0,t.jsxs)("select",{title:"department",name:"department",value:d.department,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,t.jsx)("option",{value:"Computer Science",children:"Computer Science"}),(0,t.jsx)("option",{value:"Auto Engineering",children:"Auto Engineering"}),(0,t.jsx)("option",{value:"Business",children:"Business"}),(0,t.jsx)("option",{value:"Design",children:"Design"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Skill Level"}),(0,t.jsxs)("select",{title:"skill level",name:"skill_level",value:d.skill_level,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,t.jsx)("option",{value:"beginner",children:"Beginner"}),(0,t.jsx)("option",{value:"intermediate",children:"Intermediate"}),(0,t.jsx)("option",{value:"advanced",children:"Advanced"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Number of Lectures"}),(0,t.jsx)(a.p,{type:"number",name:"num_lectures",value:d.num_lectures,onChange:h,min:"1",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Languages"}),(0,t.jsx)(a.p,{name:"languages",value:d.languages,onChange:h,placeholder:"e.g., English, Spanish",required:!0})]}),(0,t.jsxs)("div",{className:"col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Class Schedule"}),(0,t.jsxs)("select",{title:"class days",name:"class_days",value:d.class_days,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,t.jsx)("option",{value:"Evenings & Weekends",children:"Evenings & Weekends"}),(0,t.jsx)("option",{value:"Weekends Only",children:"Weekends Only"}),(0,t.jsx)("option",{value:"Evening Classes",children:"Evening Classes"}),(0,t.jsx)("option",{value:"Flexible Schedule",children:"Flexible Schedule"})]})]}),(0,t.jsxs)("div",{className:"col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Course Image"}),(0,t.jsx)(a.p,{type:"file",accept:"image/*",onChange:e=>{e.target.files&&e.target.files[0]&&m(e.target.files[0])},className:"w-full"}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Upload a course image (optional)"})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>window.history.back(),children:"Cancel"}),(0,t.jsx)(n.$,{type:"submit",disabled:r,children:r?"Creating...":"Create Refresher Course"})]})]})}},87425:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\courses\\\\refresher\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\refresher\\[id]\\edit\\page.tsx","default")}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,8403,7834,9267,4873,9847,8273],()=>r(15356));module.exports=t})();