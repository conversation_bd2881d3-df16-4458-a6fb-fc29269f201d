(()=>{var e={};e.id=1896,e.ids=[1896],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11723:e=>{"use strict";e.exports=require("querystring")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},96456:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>_});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>d});var a=t(42706),i=t(28203),o=t(45994),u=t(39187),n=t(62545),c=t(49647);async function p(e){try{let{searchParams:r}=new URL(e.url),t=r.get("courseId"),s=r.get("year"),a=`
      SELECT 
        g.*,
        GROUP_CONCAT(DISTINCT p.project_id) as project_ids,
        GROUP_CONCAT(DISTINCT sl.link_id) as social_link_ids
      FROM graduates g
      LEFT JOIN projects p ON g.graduate_id = p.graduate_id
      LEFT JOIN social_links sl ON g.graduate_id = sl.graduate_id
    `,i=[],o=[];t&&(o.push("g.course_id = ?"),i.push(t)),s&&(o.push("g.year_of_completion = ?"),i.push(s)),o.length>0&&(a+=` WHERE ${o.join(" AND ")}`),a+=" GROUP BY g.graduate_id";let[c]=await n.A.query(a,i),p=await Promise.all(c.map(async e=>{let[r]=await n.A.query("SELECT * FROM projects WHERE graduate_id = ?",[e.graduate_id]),[t]=await n.A.query("SELECT * FROM social_links WHERE graduate_id = ?",[e.graduate_id]);return{...e,projects:r,social_links:t}}));return u.NextResponse.json(p)}catch(e){return console.error(e),u.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function d(e){let r=await n.A.getConnection();await r.beginTransaction();try{let t,s;let a=await e.formData(),i=a.get("certificate_file");i&&(t=await (0,c.R)(i,"certificates"));let o=a.get("graduate_image");o&&(s=await (0,c.R)(o,"graduates"));let n=JSON.parse(a.get("graduateData")),p=JSON.parse(a.get("projects")),d=JSON.parse(a.get("social_links")),[l]=await r.query("INSERT INTO graduates SET ?",{...n,certificate_file_url:t,graduate_image_url:s}),g=l.insertId;for(let e of p)await r.query("INSERT INTO projects SET ?",{...e,graduate_id:g});for(let e of d)await r.query("INSERT INTO social_links SET ?",{...e,graduate_id:g});return await r.commit(),u.NextResponse.json({id:g},{status:201})}catch(e){return await r.rollback(),console.error("Error submitting graduate:",e),u.NextResponse.json({error:"Internal Server Error"},{status:500})}finally{r.release()}}let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/graduates/route",pathname:"/api/graduates",filename:"route",bundlePath:"app/api/graduates/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\graduates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:_,serverHooks:x}=l;function E(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:_})}},96487:()=>{},78335:()=>{},49647:(e,r,t)=>{"use strict";t.d(r,{R:()=>a});var s=t(97873);async function a(e,r){try{let t=await e.arrayBuffer(),a=Buffer.from(t);return(await new Promise((e,t)=>{s.v2.uploader.upload_stream({folder:r,resource_type:"auto"},(r,s)=>{r?t(r):s?e(s):t(Error("No result from Cloudinary"))}).end(a)})).secure_url}catch(e){throw console.error("Error uploading to Cloudinary:",e),e}}s.v2.config({cloud_name:process.env.CLOUDINARY_CLOUD_NAME,api_key:process.env.CLOUDINARY_API_KEY,api_secret:process.env.CLOUDINARY_API_SECRET})},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(60820),a=t(29021),i=t.n(a),o=t(33873),u=t.n(o);let n=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(u().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820,7873],()=>t(96456));module.exports=s})();