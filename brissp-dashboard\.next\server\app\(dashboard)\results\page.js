(()=>{var e={};e.id=5763,e.ids=[5763],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},14792:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=t(70260),r=t(28203),l=t(25155),n=t.n(l),i=t(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let o=["",{children:["(dashboard)",{children:["results",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,72065)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\results\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\results\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/results/page",pathname:"/results",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},89655:(e,s,t)=>{Promise.resolve().then(t.bind(t,72065))},42799:(e,s,t)=>{Promise.resolve().then(t.bind(t,6638))},6638:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>em});var a=t(45512),r=t(58009),l=t(69193),n=t(91542),i=t(97643),d=t(53261),o=t(25409),c=t(54069),u=t(29952),m=t(6004),x=t(31412),p=t(13024),h=t(66582),f=t(38762),j=t(98060);t(55740);var g=t(12705),v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,s)=>{let t=r.forwardRef((e,t)=>{let{asChild:r,...l}=e,n=r?g.DX:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n,{...l,ref:t})});return t.displayName=`Primitive.${s}`,{...e,[s]:t}},{}),b="Checkbox",[y,N]=(0,m.A)(b),[w,_]=y(b),C=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,name:l,checked:n,defaultChecked:i,required:d,disabled:o,value:c="on",onCheckedChange:m,form:h,...f}=e,[j,g]=r.useState(null),b=(0,u.s)(s,e=>g(e)),y=r.useRef(!1),N=!j||h||!!j.closest("form"),[_=!1,C]=(0,p.i)({prop:n,defaultProp:i,onChange:m}),R=r.useRef(_);return r.useEffect(()=>{let e=j?.form;if(e){let s=()=>C(R.current);return e.addEventListener("reset",s),()=>e.removeEventListener("reset",s)}},[j,C]),(0,a.jsxs)(w,{scope:t,state:_,disabled:o,children:[(0,a.jsx)(v.button,{type:"button",role:"checkbox","aria-checked":A(_)?"mixed":_,"aria-required":d,"data-state":F(_),"data-disabled":o?"":void 0,disabled:o,value:c,...f,ref:b,onKeyDown:(0,x.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,x.m)(e.onClick,e=>{C(e=>!!A(e)||!e),N&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})}),N&&(0,a.jsx)(k,{control:j,bubbles:!y.current,name:l,value:c,checked:_,required:d,disabled:o,form:h,style:{transform:"translateX(-100%)"},defaultChecked:!A(i)&&i})]})});C.displayName=b;var R="CheckboxIndicator",S=r.forwardRef((e,s)=>{let{__scopeCheckbox:t,forceMount:r,...l}=e,n=_(R,t);return(0,a.jsx)(j.C,{present:r||A(n.state)||!0===n.state,children:(0,a.jsx)(v.span,{"data-state":F(n.state),"data-disabled":n.disabled?"":void 0,...l,ref:s,style:{pointerEvents:"none",...e.style}})})});S.displayName=R;var k=e=>{let{control:s,checked:t,bubbles:l=!0,defaultChecked:n,...i}=e,d=r.useRef(null),o=(0,h.Z)(t),c=(0,f.X)(s);r.useEffect(()=>{let e=d.current,s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==t&&s){let a=new Event("click",{bubbles:l});e.indeterminate=A(t),s.call(e,!A(t)&&t),e.dispatchEvent(a)}},[o,t,l]);let u=r.useRef(!A(t)&&t);return(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n??u.current,...i,tabIndex:-1,ref:d,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function A(e){return"indeterminate"===e}function F(e){return A(e)?"indeterminate":e?"checked":"unchecked"}var P=t(24999),E=t(59462);let D=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(C,{ref:t,className:(0,E.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...s,children:(0,a.jsx)(S,{className:(0,E.cn)("flex items-center justify-center text-current"),children:(0,a.jsx)(P.A,{className:"h-4 w-4"})})}));D.displayName=C.displayName;var T=t(87021),$=t(48859);function B({courses:e,selectedCourse:s,onCourseSelect:t}){let[l,u]=(0,r.useState)([]),[m,x]=(0,r.useState)({user_id:"",enrollment_id:"",assessment_type:"",assessment_title:"",score:"",max_score:"100",result_date:"",comments:"",is_passed:!1}),p=async e=>{e.preventDefault();try{if(!(await fetch("/api/results",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...m,course_id:s,score:parseFloat(m.score),max_score:parseFloat(m.max_score)})})).ok)throw Error("Failed to submit result");n.o.success("Result added successfully"),x({user_id:"",enrollment_id:"",assessment_type:"",assessment_title:"",score:"",max_score:"100",result_date:"",comments:"",is_passed:!1})}catch{n.o.error("Failed to submit result")}};return(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Add New Result"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("form",{onSubmit:p,className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"course",children:"Course"}),(0,a.jsxs)(c.l6,{value:s,onValueChange:t,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select a course"})}),(0,a.jsx)(c.gC,{children:e.map(e=>(0,a.jsx)(c.eb,{value:e.course_id.toString(),children:e.title},e.course_id))})]})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"student",children:"Student"}),(0,a.jsxs)(c.l6,{value:m.enrollment_id,onValueChange:e=>{let s=l.find(s=>s.enrollment_id.toString()===e);x({...m,enrollment_id:e,user_id:s?s.user_id.toString():""})},disabled:!s,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select a student"})}),(0,a.jsx)(c.gC,{children:l.map(e=>(0,a.jsx)(c.eb,{value:e.enrollment_id.toString(),children:`${e.first_name} ${e.last_name}`},e.enrollment_id))})]})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"assessment-type",children:"Assessment Type"}),(0,a.jsxs)(c.l6,{value:m.assessment_type,onValueChange:e=>x({...m,assessment_type:e}),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select assessment type"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"quiz",children:"Quiz"}),(0,a.jsx)(c.eb,{value:"exam",children:"Exam"}),(0,a.jsx)(c.eb,{value:"assignment",children:"Assignment"}),(0,a.jsx)(c.eb,{value:"project",children:"Project"}),(0,a.jsx)(c.eb,{value:"final",children:"Final"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"assessment-title",children:"Assessment Title"}),(0,a.jsx)(o.p,{id:"assessment-title",placeholder:"Enter assessment title",value:m.assessment_title,onChange:e=>x({...m,assessment_title:e.target.value})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"score",children:"Score"}),(0,a.jsx)(o.p,{id:"score",type:"number",placeholder:"Enter score",value:m.score,onChange:e=>x({...m,score:e.target.value})})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"max-score",children:"Max Score"}),(0,a.jsx)(o.p,{id:"max-score",type:"number",placeholder:"Enter max score",value:m.max_score,onChange:e=>x({...m,max_score:e.target.value})})]})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"result-date",children:"Result Date"}),(0,a.jsx)(o.p,{id:"result-date",type:"date",value:m.result_date,onChange:e=>x({...m,result_date:e.target.value})})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"comments",children:"Comments"}),(0,a.jsx)($.T,{id:"comments",placeholder:"Enter comments (optional)",value:m.comments,onChange:e=>x({...m,comments:e.target.value}),rows:3})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(D,{id:"is-passed",checked:m.is_passed,onCheckedChange:e=>x({...m,is_passed:e})}),(0,a.jsx)(d.J,{htmlFor:"is-passed",className:"font-normal",children:"Mark as passed"})]}),(0,a.jsx)(T.$,{type:"submit",className:"w-full",children:"Submit Result"})]})})})]})}var J=t(13393),Z=t(71965);function q({results:e,onEdit:s,editingResult:t,onUpdate:l,onCancelEdit:n}){let[u,m]=(0,r.useState)(null);t&&(!u||u.result_id!==t.result_id)&&m({...t});let x=e=>{e.preventDefault(),u&&l(u)};return(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Results List"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(J.XI,{children:[(0,a.jsx)(J.A0,{children:(0,a.jsxs)(J.Hj,{children:[(0,a.jsx)(J.nd,{children:"Student"}),(0,a.jsx)(J.nd,{children:"Assessment"}),(0,a.jsx)(J.nd,{children:"Score"}),(0,a.jsx)(J.nd,{children:"Date"}),(0,a.jsx)(J.nd,{children:"Status"}),(0,a.jsx)(J.nd,{children:"Actions"})]})}),(0,a.jsx)(J.BF,{children:e.map(e=>(0,a.jsxs)(J.Hj,{children:[(0,a.jsx)(J.nA,{children:`${e.first_name} ${e.last_name}`}),(0,a.jsxs)(J.nA,{children:[(0,a.jsx)("div",{className:"font-medium",children:e.assessment_title}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.assessment_type})]}),(0,a.jsx)(J.nA,{children:`${e.score}/${e.max_score}`}),(0,a.jsx)(J.nA,{children:new Date(e.result_date).toLocaleDateString()}),(0,a.jsx)(J.nA,{children:(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${e.is_passed?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_passed?"Passed":"Failed"})}),(0,a.jsx)(J.nA,{children:(0,a.jsxs)(Z.lG,{open:t?.result_id===e.result_id,onOpenChange:t=>{t||n(),t&&s(e)},children:[(0,a.jsx)(Z.zM,{asChild:!0,children:(0,a.jsx)(T.$,{variant:"outline",size:"sm",children:"Edit"})}),(0,a.jsxs)(Z.Cf,{className:"sm:max-w-[425px]",children:[(0,a.jsx)(Z.c7,{children:(0,a.jsx)(Z.L3,{children:"Edit Result"})}),u&&(0,a.jsxs)("form",{onSubmit:x,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"edit-assessment-type",children:"Assessment Type"}),(0,a.jsxs)(c.l6,{value:u.assessment_type,onValueChange:e=>m({...u,assessment_type:e}),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select assessment type"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"quiz",children:"Quiz"}),(0,a.jsx)(c.eb,{value:"exam",children:"Exam"}),(0,a.jsx)(c.eb,{value:"assignment",children:"Assignment"}),(0,a.jsx)(c.eb,{value:"project",children:"Project"}),(0,a.jsx)(c.eb,{value:"final",children:"Final"})]})]})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"edit-assessment-title",children:"Assessment Title"}),(0,a.jsx)(o.p,{id:"edit-assessment-title",value:u.assessment_title,onChange:e=>m({...u,assessment_title:e.target.value})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"edit-score",children:"Score"}),(0,a.jsx)(o.p,{id:"edit-score",type:"number",value:u.score,onChange:e=>m({...u,score:parseFloat(e.target.value)})})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"edit-max-score",children:"Max Score"}),(0,a.jsx)(o.p,{id:"edit-max-score",type:"number",value:u.max_score,onChange:e=>m({...u,max_score:parseFloat(e.target.value)})})]})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"edit-result-date",children:"Result Date"}),(0,a.jsx)(o.p,{id:"edit-result-date",type:"date",value:u.result_date.split("T")[0],onChange:e=>m({...u,result_date:e.target.value})})]}),(0,a.jsxs)("div",{className:"grid gap-2",children:[(0,a.jsx)(d.J,{htmlFor:"edit-comments",children:"Comments"}),(0,a.jsx)($.T,{id:"edit-comments",value:u.comments,onChange:e=>m({...u,comments:e.target.value}),rows:3})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(D,{id:"edit-is-passed",checked:u.is_passed,onCheckedChange:e=>m({...u,is_passed:e})}),(0,a.jsx)(d.J,{htmlFor:"edit-is-passed",className:"font-normal",children:"Mark as passed"})]}),(0,a.jsxs)(Z.Es,{children:[(0,a.jsx)(T.$,{type:"button",variant:"outline",onClick:n,children:"Cancel"}),(0,a.jsx)(T.$,{type:"submit",children:"Save Changes"})]})]})]})]})})]},e.result_id))})]})})})]})}var z=t(23081),M=t(94199),L=t(14169),W=t(11452),X=t(1263),I=t(6070),U=(0,M.gu)({chartName:"BarChart",GraphicalChild:L.y,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:W.W},{axisType:"yAxis",AxisComp:X.h}],formatAxisMap:I.pr}),V=t(26507),G=t(707);function O({analytics:e}){return(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Average Score"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"text-3xl font-bold",children:[e.averageScore.toFixed(1),"%"]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Pass Rate"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"text-3xl font-bold",children:[e.passRate.toFixed(1),"%"]})})]}),(0,a.jsxs)(i.Zp,{className:"md:col-span-2 lg:col-span-3",children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Assessment Type Distribution"})}),(0,a.jsx)(i.Wu,{className:"h-[300px]",children:(0,a.jsx)(z.u,{width:"100%",height:"100%",children:(0,a.jsxs)(U,{data:e.assessmentTypeBreakdown,children:[(0,a.jsx)(V.d,{strokeDasharray:"3 3"}),(0,a.jsx)(W.W,{dataKey:"name"}),(0,a.jsx)(X.h,{}),(0,a.jsx)(G.m,{}),(0,a.jsx)(L.y,{dataKey:"value",fill:"#4f46e5"})]})})})]})]})}var H=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,s)=>{let t=r.forwardRef((e,t)=>{let{asChild:r,...l}=e,n=r?g.DX:s;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n,{...l,ref:t})});return t.displayName=`Primitive.${s}`,{...e,[s]:t}},{}),K="Progress",[Y,Q]=(0,m.A)(K),[ee,es]=Y(K),et=r.forwardRef((e,s)=>{var t,r;let{__scopeProgress:l,value:n=null,max:i,getValueLabel:d=el,...o}=e;(i||0===i)&&!ed(i)&&console.error((t=`${i}`,`Invalid prop \`max\` of value \`${t}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let c=ed(i)?i:100;null===n||eo(n,c)||console.error((r=`${n}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=eo(n,c)?n:null,m=ei(u)?d(u,c):void 0;return(0,a.jsx)(ee,{scope:l,value:u,max:c,children:(0,a.jsx)(H.div,{"aria-valuemax":c,"aria-valuemin":0,"aria-valuenow":ei(u)?u:void 0,"aria-valuetext":m,role:"progressbar","data-state":en(u,c),"data-value":u??void 0,"data-max":c,...o,ref:s})})});et.displayName=K;var ea="ProgressIndicator",er=r.forwardRef((e,s)=>{let{__scopeProgress:t,...r}=e,l=es(ea,t);return(0,a.jsx)(H.div,{"data-state":en(l.value,l.max),"data-value":l.value??void 0,"data-max":l.max,...r,ref:s})});function el(e,s){return`${Math.round(e/s*100)}%`}function en(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function ei(e){return"number"==typeof e}function ed(e){return ei(e)&&!isNaN(e)&&e>0}function eo(e,s){return ei(e)&&!isNaN(e)&&e<=s&&e>=0}er.displayName=ea;let ec=r.forwardRef(({className:e,value:s,...t},r)=>(0,a.jsx)(et,{ref:r,className:(0,E.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...t,children:(0,a.jsx)(er,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));function eu({courses:e}){let[s,t]=(0,r.useState)(""),[l,n]=(0,r.useState)([]),[o,u]=(0,r.useState)(""),[m,x]=(0,r.useState)([]),[p,h]=(0,r.useState)(0),[f,j]=(0,r.useState)(0),g=e=>e>=90?"text-green-600":e>=80?"text-green-500":e>=70?"text-yellow-500":e>=60?"text-orange-500":"text-red-500";return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Student Results Analysis"}),(0,a.jsx)(i.BT,{children:"View a student's performance in a specific course"})]}),(0,a.jsx)(i.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"course-select",children:"Select Course"}),(0,a.jsxs)(c.l6,{value:s,onValueChange:t,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select a course"})}),(0,a.jsx)(c.gC,{children:e.map(e=>(0,a.jsx)(c.eb,{value:e.course_id.toString(),children:e.title},e.course_id))})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(d.J,{htmlFor:"student-select",children:"Select Student"}),(0,a.jsxs)(c.l6,{value:o,onValueChange:u,disabled:!s||0===l.length,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select a student"})}),(0,a.jsx)(c.gC,{children:l.map(e=>(0,a.jsx)(c.eb,{value:e.user_id.toString(),children:`${e.first_name} ${e.last_name}`},e.user_id))})]})]})]})})]}),o&&m.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Average Score"})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-baseline space-x-2",children:[(0,a.jsxs)("span",{className:`text-4xl font-bold ${g(p)}`,children:[p.toFixed(1),"%"]}),(0,a.jsxs)("span",{className:`text-2xl ${g(p)}`,children:["(",p>=90?"A":p>=80?"B":p>=70?"C":p>=60?"D":"F",")"]})]}),(0,a.jsx)(ec,{value:p,className:"h-2 mt-2"})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Pass Rate"})}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"text-4xl font-bold",children:[f.toFixed(1),"%"]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground mt-1",children:[Math.round(f/100*m.length),"/",m.length," assessments passed"]})]})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Assessment Results"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)(J.XI,{children:[(0,a.jsx)(J.A0,{children:(0,a.jsxs)(J.Hj,{children:[(0,a.jsx)(J.nd,{children:"Assessment"}),(0,a.jsx)(J.nd,{children:"Type"}),(0,a.jsx)(J.nd,{children:"Score"}),(0,a.jsx)(J.nd,{children:"Percentage"}),(0,a.jsx)(J.nd,{children:"Date"}),(0,a.jsx)(J.nd,{children:"Status"})]})}),(0,a.jsx)(J.BF,{children:m.map(e=>{let s=e.score/e.max_score*100;return(0,a.jsxs)(J.Hj,{children:[(0,a.jsx)(J.nA,{className:"font-medium",children:e.assessment_title}),(0,a.jsx)(J.nA,{className:"capitalize",children:e.assessment_type}),(0,a.jsx)(J.nA,{children:`${e.score}/${e.max_score}`}),(0,a.jsxs)(J.nA,{className:g(s),children:[s.toFixed(1),"%"]}),(0,a.jsx)(J.nA,{children:new Date(e.result_date).toLocaleDateString()}),(0,a.jsx)(J.nA,{children:(0,a.jsx)("span",{className:`px-2 py-1 rounded-full text-xs ${e.is_passed?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.is_passed?"Passed":"Failed"})})]},e.result_id)})})]})})]})]}),o&&0===m.length&&(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"py-8",children:(0,a.jsx)("div",{className:"text-center text-muted-foreground",children:"No results found for this student in the selected course."})})})]})}function em(){let[e,s]=(0,r.useState)([]),[t,i]=(0,r.useState)(""),[d,o]=(0,r.useState)([]),[c,u]=(0,r.useState)({averageScore:0,passRate:0,assessmentTypeBreakdown:[]}),m=async()=>{try{let e=t?`/api/results?courseId=${t}`:"/api/results",s=await fetch(e),a=await s.json();o(a.results),x(a.results)}catch{n.o.error("Failed to fetch results")}},x=e=>{if(!e||0===e.length){u({averageScore:0,passRate:0,assessmentTypeBreakdown:[]});return}u({averageScore:e.reduce((e,s)=>e+s.score/s.max_score*100,0)/e.length,passRate:e.filter(e=>e.is_passed).length/e.length*100,assessmentTypeBreakdown:Object.entries(e.reduce((e,s)=>(e[s.assessment_type]=(e[s.assessment_type]||0)+1,e),{})).map(([e,s])=>({name:e,value:s}))})},[p,h]=(0,r.useState)(null),f=async e=>{try{if(!(await fetch(`/api/results/${e.result_id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok)throw Error("Failed to update result");n.o.success("Result updated successfully"),m(),h(null)}catch{n.o.error("Failed to update result")}};return(0,a.jsxs)("div",{className:"p-6 container space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Results Management"})}),(0,a.jsxs)(l.tU,{defaultValue:"add",className:"space-y-6",children:[(0,a.jsxs)(l.j7,{children:[(0,a.jsx)(l.Xi,{value:"add",children:"Add Result"}),(0,a.jsx)(l.Xi,{value:"view",children:"View Results"}),(0,a.jsx)(l.Xi,{value:"analytics",children:"Analytics"}),(0,a.jsx)(l.Xi,{value:"student",children:"Student Analysis"})]}),(0,a.jsx)(l.av,{value:"add",children:(0,a.jsx)(B,{courses:e,selectedCourse:t,onCourseSelect:i})}),(0,a.jsx)(l.av,{value:"view",children:(0,a.jsx)(q,{results:d,onEdit:e=>{h(e)},editingResult:p,onUpdate:f,onCancelEdit:()=>h(null)})}),(0,a.jsx)(l.av,{value:"analytics",children:(0,a.jsx)(O,{analytics:c})}),(0,a.jsx)(l.av,{value:"student",children:(0,a.jsx)(eu,{courses:e})})]})]})}ec.displayName=et.displayName},97643:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>u});var a=t(45512),r=t(58009),l=t(59462);let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...s}));n.displayName="Card";let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent";let u=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s}));u.displayName="CardFooter"},71965:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>m,Es:()=>p,L3:()=>h,c7:()=>x,lG:()=>d,zM:()=>o});var a=t(45512),r=t(58009),l=t(32958),n=t(51255),i=t(59462);let d=l.bL,o=l.l9,c=l.ZL;l.bm;let u=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hJ,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));u.displayName=l.hJ.displayName;let m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(l.UC,{ref:r,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[s,(0,a.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=l.UC.displayName;let x=({className:e,...s})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});x.displayName="DialogHeader";let p=({className:e,...s})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});p.displayName="DialogFooter";let h=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.hE,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));h.displayName=l.hE.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.VY,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s})).displayName=l.VY.displayName},25409:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(45512),r=t(58009),l=t(59462);let n=r.forwardRef(({className:e,type:s,...t},r)=>(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...t}));n.displayName="Input"},53261:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var a=t(45512),r=t(58009),l=t(18055),n=r.forwardRef((e,s)=>(0,a.jsx)(l.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var i=t(21643),d=t(59462);let o=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(n,{ref:t,className:(0,d.cn)(o(),e),...s}));c.displayName=n.displayName},54069:(e,s,t)=>{"use strict";t.d(s,{bq:()=>m,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>u});var a=t(45512),r=t(58009),l=t(96096),n=t(7833),i=t(36624),d=t(24999),o=t(59462);let c=l.bL;l.YJ;let u=l.WT,m=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=l.l9.displayName;let x=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})}));x.displayName=l.PP.displayName;let p=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}));p.displayName=l.wn.displayName;let h=r.forwardRef(({className:e,children:s,position:t="popper",...r},n)=>(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(p,{})]})}));h.displayName=l.UC.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let f=r.forwardRef(({className:e,children:s,...t},r)=>(0,a.jsxs)(l.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:s})]}));f.displayName=l.q7.displayName,r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.wv.displayName},69193:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>i});var a=t(45512),r=t(58009),l=t(72366),n=t(59462);let i=l.bL,d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.B8,{ref:t,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));d.displayName=l.B8.displayName;let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.l9,{ref:t,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));o.displayName=l.l9.displayName;let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)(l.UC,{ref:t,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));c.displayName=l.UC.displayName},48859:(e,s,t)=>{"use strict";t.d(s,{T:()=>n});var a=t(45512),r=t(58009),l=t(59462);let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:t,...s}));n.displayName="Textarea"},72065:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\results\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\results\\page.tsx","default")}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[638,8403,7834,9267,4873,6562,3015,4434,8449,2449],()=>t(14792));module.exports=a})();