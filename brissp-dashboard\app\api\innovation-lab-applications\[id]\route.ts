/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import pool from '@/lib/db';
import { ResultSetHeader } from 'mysql2';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const applicationId = parseInt(resolvedParams.id);

    if (isNaN(applicationId)) {
      return NextResponse.json(
        { error: 'Invalid application ID' },
        { status: 400 }
      );
    }

    const [applications] = await pool.query(
      'SELECT * FROM innovation_lab_applications WHERE application_id = ?',
      [applicationId]
    );

    const applicationsArray = applications as any[];

    if (applicationsArray.length === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      application: applicationsArray[0]
    });

  } catch (error) {
    console.error('Error fetching innovation lab application:', error);
    return NextResponse.json(
      { error: 'An error occurred while fetching the application' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const applicationId = parseInt(resolvedParams.id);
    const data = await request.json();

    if (isNaN(applicationId)) {
      return NextResponse.json(
        { error: 'Invalid application ID' },
        { status: 400 }
      );
    }

    const updateQuery = `
      UPDATE innovation_lab_applications 
      SET status = ?, review_notes = ?, review_date = NOW(), updated_at = NOW()
      WHERE application_id = ?
    `;

    const [result] = await pool.query<ResultSetHeader>(updateQuery, [
      data.status,
      data.review_notes || null,
      applicationId
    ]);

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Application updated successfully'
    });

  } catch (error) {
    console.error('Error updating innovation lab application:', error);
    return NextResponse.json(
      { error: 'An error occurred while updating the application' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const applicationId = parseInt(resolvedParams.id);

    if (isNaN(applicationId)) {
      return NextResponse.json(
        { error: 'Invalid application ID' },
        { status: 400 }
      );
    }

    const [result] = await pool.query<ResultSetHeader>(
      'DELETE FROM innovation_lab_applications WHERE application_id = ?',
      [applicationId]
    );

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Application deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting innovation lab application:', error);
    return NextResponse.json(
      { error: 'An error occurred while deleting the application' },
      { status: 500 }
    );
  }
}
