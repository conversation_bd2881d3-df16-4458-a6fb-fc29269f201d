(()=>{var e={};e.id=293,e.ids=[293],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},55510:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>c,tree:()=>p});var s=t(70260),o=t(28203),i=t(25155),n=t.n(i),a=t(67292),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let p=["",{children:["(dashboard)",{children:["curriculum",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,27129)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\new\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(dashboard)/curriculum/new/page",pathname:"/curriculum/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},53402:(e,r,t)=>{Promise.resolve().then(t.bind(t,27129))},21858:(e,r,t)=>{Promise.resolve().then(t.bind(t,27349))},27349:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(45512),o=t(58009),i=t(79334),n=t(36929);function a(){let e=(0,i.useRouter)(),[r,t]=(0,o.useState)(!1),a=async r=>{t(!0);try{(await fetch("/api/curriculum",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)})).ok&&e.push("/curriculum")}catch(e){console.error("Error creating curriculum:",e)}finally{t(!1)}};return(0,s.jsxs)("div",{className:"p-6 ",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Create New Curriculum"}),(0,s.jsx)("div",{className:"bg-white rounded-lg border p-6",children:(0,s.jsx)(n.A,{onSubmit:a,isLoading:r})})]})}},27129:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\curriculum\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\new\\page.tsx","default")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,8403,7834,9267,4873,6562,3015,3373],()=>t(55510));module.exports=s})();