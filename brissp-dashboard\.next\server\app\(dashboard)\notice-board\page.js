(()=>{var e={};e.id=5618,e.ids=[5618],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},27786:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>h,tree:()=>c});var s=t(70260),i=t(28203),n=t(25155),o=t.n(n),a=t(67292),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);t.d(r,d);let c=["",{children:["(dashboard)",{children:["notice-board",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8522)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/notice-board/page",pathname:"/notice-board",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},96914:(e,r,t)=>{Promise.resolve().then(t.bind(t,8522))},86298:(e,r,t)=>{Promise.resolve().then(t.bind(t,61358))},61358:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(45512),i=t(58009),n=t(87021),o=t(13393),a=t(46335),d=t(95758),c=t(79041),l=t(28531),p=t.n(l);let h=()=>{let[e,r]=(0,i.useState)([]);(0,i.useEffect)(()=>{t()},[]);let t=async()=>{try{let e=await fetch("/api/notices"),t=await e.json();r(t)}catch(e){console.error("Error fetching notices:",e)}},l=async e=>{if(confirm("Are you sure you want to delete this notice?"))try{(await fetch(`/api/notices/${e}`,{method:"DELETE"})).ok&&t()}catch(e){console.error("Error deleting notice:",e)}};return(0,s.jsxs)("div",{className:"p-6 ",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"Notice Board Management"}),(0,s.jsx)(p(),{href:"/notice-board/new",children:(0,s.jsxs)(n.$,{children:[(0,s.jsx)(a.A,{className:"w-4 h-4 mr-2"}),"Add New Notice"]})})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg border",children:(0,s.jsxs)(o.XI,{children:[(0,s.jsx)(o.A0,{children:(0,s.jsxs)(o.Hj,{children:[(0,s.jsx)(o.nd,{children:"Title"}),(0,s.jsx)(o.nd,{children:"Description"}),(0,s.jsx)(o.nd,{children:"Actions"})]})}),(0,s.jsx)(o.BF,{children:e.map(e=>(0,s.jsxs)(o.Hj,{children:[(0,s.jsx)(o.nA,{children:e.title}),(0,s.jsx)(o.nA,{children:e.description}),(0,s.jsxs)(o.nA,{className:"flex space-x-2",children:[(0,s.jsx)(p(),{href:`/notice-board/${e.notice_id}/edit`,children:(0,s.jsx)(n.$,{variant:"outline",size:"sm",children:(0,s.jsx)(d.A,{className:"w-4 h-4"})})}),(0,s.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>l(e.notice_id),children:(0,s.jsx)(c.A,{className:"w-4 h-4"})})]})]},e.notice_id))})]})})]})}},8522:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\notice-board\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\page.tsx","default")},95758:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},46335:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},79041:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,8403,7834,9267,4873,2449],()=>t(27786));module.exports=s})();