exports.id=1066,exports.ids=[1066,8273],exports.modules={94168:(e,t,s)=>{Promise.resolve().then(s.bind(s,60189)),Promise.resolve().then(s.bind(s,22234)),Promise.resolve().then(s.bind(s,56814))},3896:(e,t,s)=>{Promise.resolve().then(s.bind(s,52401)),Promise.resolve().then(s.bind(s,58783)),Promise.resolve().then(s.bind(s,91542))},98167:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,13219,23)),Promise.resolve().then(s.t.bind(s,34863,23)),Promise.resolve().then(s.t.bind(s,25155,23)),Promise.resolve().then(s.t.bind(s,40802,23)),Promise.resolve().then(s.t.bind(s,9350,23)),Promise.resolve().then(s.t.bind(s,48530,23)),Promise.resolve().then(s.t.bind(s,88921,23))},51311:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,66959,23)),Promise.resolve().then(s.t.bind(s,33875,23)),Promise.resolve().then(s.t.bind(s,88903,23)),Promise.resolve().then(s.t.bind(s,57174,23)),Promise.resolve().then(s.t.bind(s,84178,23)),Promise.resolve().then(s.t.bind(s,87190,23)),Promise.resolve().then(s.t.bind(s,61365,23))},52401:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(45512),i=s(58009),n=s(79334),o=s(91542);let a=({children:e})=>{let t=(0,n.useRouter)(),[s,a]=(0,i.useState)(null);return((0,i.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/admin/auth/check",{method:"GET",credentials:"include",cache:"no-store"});if(!e.ok)throw Error("Not authenticated");let t=await e.json();console.log("Auth check response:",t),a(!0)}catch(e){console.error("Authentication check failed:",e),a(!1),o.o.error("Please log in to access the dashboard"),t.push("/")}})()},[t]),null===s)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"})}):s?(0,r.jsx)(r.Fragment,{children:e}):null}},35564:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(45512),i=s(58009),n=s(87021),o=s(25409),a=s(48859);function l({initialData:e,onSubmit:t,isLoading:s}){let[l,d]=(0,i.useState)(e?{...e,publish_date:new Date(e.publish_date),expiry_date:e.expiry_date?new Date(e.expiry_date):void 0}:{title:"",description:"",priority:"medium",publish_date:new Date,expiry_date:void 0}),c=e=>{let{name:t,value:s}=e.target;d(e=>({...e,[t]:"publish_date"===t||"expiry_date"===t?new Date(s):s}))};return(0,r.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(l)},className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Title"}),(0,r.jsx)(o.p,{name:"title",value:l.title,onChange:c,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Description"}),(0,r.jsx)(a.T,{name:"description",value:l.description,onChange:c,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Priority"}),(0,r.jsxs)("select",{title:"Priority",name:"priority",value:l.priority,onChange:c,className:"w-full border rounded-md p-2",required:!0,children:[(0,r.jsx)("option",{value:"low",children:"Low"}),(0,r.jsx)("option",{value:"medium",children:"Medium"}),(0,r.jsx)("option",{value:"high",children:"High"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Publish Date"}),(0,r.jsx)(o.p,{type:"date",name:"publish_date",value:l.publish_date?l.publish_date.toISOString().split("T")[0]:"",onChange:c,required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Expiry Date"}),(0,r.jsx)(o.p,{type:"date",name:"expiry_date",value:l.expiry_date?l.expiry_date.toISOString().split("T")[0]:"",onChange:c})]}),(0,r.jsx)("div",{className:"flex justify-end space-x-4",children:(0,r.jsx)(n.$,{type:"submit",disabled:s,children:s?"Saving...":"Save Notice"})})]})}},58783:(e,t,s)=>{"use strict";s.d(t,{default:()=>C});var r=s(45512),i=s(58009),n=s(28531),o=s.n(n),a=s(38440),l=s(1422),d=s(57631),c=s(35603),u=s(43161),m=s(62673),h=s(28650),p=s(52975),b=s(39327),f=s(55817),x=s(53100),v=s(93346),g=s(6472),y=s(51255),j=s(35120),w=s(48320),N=s(79334),k=s(91542);let A=({onToggle:e})=>(0,r.jsxs)("div",{className:"md:hidden bg-white border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30",children:[(0,r.jsx)("button",{type:"button",onClick:e,className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200","aria-label":"Toggle navigation menu",title:"Toggle navigation menu",children:(0,r.jsx)(w.A,{className:"w-6 h-6"})}),(0,r.jsx)("h1",{className:"font-semibold text-lg",children:"Brissp Dashboard"}),(0,r.jsx)("div",{className:"w-10"})," "]}),P=({isOpen:e=!1,onToggle:t})=>{let s=(0,N.useRouter)(),[n,w]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{w(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,i.useEffect)(()=>{let s=s=>{if(n&&e&&t){let e=document.getElementById("mobile-sidebar");e&&!e.contains(s.target)&&t()}};if(n&&e)return document.addEventListener("mousedown",s),()=>document.removeEventListener("mousedown",s)},[n,e,t]),(0,i.useEffect)(()=>(n&&e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[n,e]);let A=async()=>{try{(await fetch("/api/admin/auth/logout",{method:"POST",credentials:"include"})).ok?(k.o.success("Logged out successfully"),s.push("/")):k.o.error("Failed to log out")}catch(e){console.error("Logout error:",e),k.o.error("An error occurred during logout")}},P=()=>{n&&t&&t()},C=[{href:"/panel",icon:a.A,label:"Dashboard"},{href:"/applications",icon:l.A,label:"Course Applications"},{href:"/pitch-deck-applications",icon:d.A,label:"Pitch Deck Applications"},{href:"/internship-applications",icon:c.A,label:"Internship Applications"},{href:"/fyp-applications",icon:u.A,label:"FYP Applications"},{href:"/innovation-lab-applications",icon:m.A,label:"Innovation Lab Applications"},{href:"/results",icon:h.A,label:"Results"},{href:"/downloadables",icon:p.A,label:"Resources"},{href:"/courses",icon:b.A,label:"Courses"},{href:"/curriculum",icon:f.A,label:"Curriculum"},{href:"/notice-board",icon:x.A,label:"Notice Board"},{href:"/graduates",icon:v.A,label:"Graduates"},{href:"/password-management",icon:g.A,label:"Password Management"}];return(0,r.jsxs)(r.Fragment,{children:[n&&e&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",onClick:t}),(0,r.jsxs)("div",{id:"mobile-sidebar",className:`
          ${n?`fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${e?"translate-x-0":"-translate-x-full"}`:"fixed inset-y-0 left-0 w-64"}
          border-r bg-white h-screen flex flex-col
        `,children:[(0,r.jsxs)("div",{className:"p-4 border-b flex items-center justify-between",children:[(0,r.jsx)(o(),{href:"/panel",className:"flex items-center space-x-2",onClick:P,children:(0,r.jsx)("span",{className:"font-semibold text-lg",children:"Brissp Dash"})}),n&&(0,r.jsx)("button",{type:"button",onClick:t,className:"p-2 rounded-lg hover:bg-gray-100 md:hidden","aria-label":"Close navigation menu",title:"Close navigation menu",children:(0,r.jsx)(y.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("nav",{className:"p-4 space-y-2 flex-grow overflow-y-auto",children:C.map(e=>{let t=e.icon;return(0,r.jsxs)(o(),{href:e.href,onClick:P,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200",children:[(0,r.jsx)(t,{className:"w-5 h-5 flex-shrink-0"}),(0,r.jsx)("span",{className:"truncate",children:e.label})]},e.href)})}),(0,r.jsx)("div",{className:"p-4 border-t mt-auto",children:(0,r.jsxs)("button",{type:"button",onClick:A,className:"flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full transition-colors duration-200",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 flex-shrink-0"}),(0,r.jsx)("span",{children:"Logout"})]})})]})]})},C=({children:e})=>{let[t,s]=(0,i.useState)(!1),[n,o]=(0,i.useState)(!1);(0,i.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;o(e),e||s(!1)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let a=()=>{s(!t)};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,r.jsx)(A,{onToggle:a}),(0,r.jsx)(P,{isOpen:t,onToggle:a}),(0,r.jsx)("div",{className:`
        transition-all duration-300 ease-in-out
        ${n?"ml-0":"ml-64"}
      `,children:(0,r.jsx)("main",{className:"min-h-screen",children:e})})]})}},87021:(e,t,s)=>{"use strict";s.d(t,{$:()=>d});var r=s(45512),i=s(58009),n=s(12705),o=s(21643),a=s(59462);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef(({className:e,variant:t,size:s,asChild:i=!1,...o},d)=>{let c=i?n.DX:"button";return(0,r.jsx)(c,{className:(0,a.cn)(l({variant:t,size:s,className:e})),ref:d,...o})});d.displayName="Button"},25409:(e,t,s)=>{"use strict";s.d(t,{p:()=>o});var r=s(45512),i=s(58009),n=s(59462);let o=i.forwardRef(({className:e,type:t,...s},i)=>(0,r.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:i,...s}));o.displayName="Input"},48859:(e,t,s)=>{"use strict";s.d(t,{T:()=>o});var r=s(45512),i=s(58009),n=s(59462);let o=i.forwardRef(({className:e,...t},s)=>(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:s,...t}));o.displayName="Textarea"},59462:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var r=s(82281),i=s(94805);function n(...e){return(0,i.QP)((0,r.$)(e))}},71975:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>u});var r=s(62740),i=s(41583),n=s.n(i),o=s(76499),a=s.n(o);s(2012);var l=s(22234),d=s(60189),c=s(56814);let u={title:"Brissp Admin",description:"Brissp Academy Admin Dashboard"};function m({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsxs)("body",{className:`${n().variable} ${a().variable} antialiased`,children:[(0,r.jsx)(d.default,{children:(0,r.jsx)(l.default,{children:e})}),(0,r.jsx)(c.Toaster,{})]})})}},60189:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\AuthGuard.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\AuthGuard.tsx","default")},22234:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\components\\\\ResponsiveLayout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\components\\ResponsiveLayout.tsx","default")},46055:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(88077);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},2012:()=>{}};