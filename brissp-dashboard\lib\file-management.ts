/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { readdir, unlink, stat } from 'fs/promises';
import { join } from 'path';
import pool from './db';
import { fileStorage } from './file-storage';

export interface OrphanedFile {
  path: string;
  filename: string;
  folder: string;
  size: number;
  lastModified: Date;
}

export interface FileCleanupResult {
  deletedFiles: string[];
  errors: string[];
  totalSize: number;
}

export class FileManager {
  private uploadsDir: string;

  constructor(uploadsDir: string = 'public/uploads') {
    this.uploadsDir = uploadsDir;
  }

  /**
   * Find orphaned files that are not referenced in the database
   */
  async findOrphanedFiles(): Promise<OrphanedFile[]> {
    const orphanedFiles: OrphanedFile[] = [];
    
    try {
      // Get all file URLs from database tables
      const referencedUrls = await this.getReferencedFileUrls();
      const referencedFilenames = new Set(
        referencedUrls
          .map(url => this.extractFilenameFromUrl(url))
          .filter(Boolean)
      );

      // Scan upload directories
      const folders = await readdir(this.uploadsDir, { withFileTypes: true });
      
      for (const folder of folders) {
        if (folder.isDirectory()) {
          const folderPath = join(this.uploadsDir, folder.name);
          const files = await readdir(folderPath);
          
          for (const filename of files) {
            if (!referencedFilenames.has(filename)) {
              const filePath = join(folderPath, filename);
              const stats = await stat(filePath);
              
              orphanedFiles.push({
                path: filePath,
                filename,
                folder: folder.name,
                size: stats.size,
                lastModified: stats.mtime
              });
            }
          }
        }
      }
    } catch (error) {
      console.error('Error finding orphaned files:', error);
    }

    return orphanedFiles;
  }

  /**
   * Clean up orphaned files
   */
  async cleanupOrphanedFiles(olderThanDays: number = 7): Promise<FileCleanupResult> {
    const result: FileCleanupResult = {
      deletedFiles: [],
      errors: [],
      totalSize: 0
    };

    try {
      const orphanedFiles = await this.findOrphanedFiles();
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      for (const file of orphanedFiles) {
        if (file.lastModified < cutoffDate) {
          try {
            await unlink(file.path);
            result.deletedFiles.push(file.filename);
            result.totalSize += file.size;
          } catch (error) {
            result.errors.push(`Failed to delete ${file.filename}: ${error}`);
          }
        }
      }
    } catch (error) {
      result.errors.push(`Cleanup failed: ${error}`);
    }

    return result;
  }

  /**
   * Get all file URLs referenced in the database
   */
  private async getReferencedFileUrls(): Promise<string[]> {
    const urls: string[] = [];

    try {
      // Check courses table
      const [courseRows] = await pool.query('SELECT image_url FROM courses WHERE image_url IS NOT NULL');
      urls.push(...(courseRows as any[]).map(row => row.image_url));

      // Check downloadable_files table
      const [fileRows] = await pool.query('SELECT file_url FROM downloadable_files WHERE file_url IS NOT NULL');
      urls.push(...(fileRows as any[]).map(row => row.file_url));

      // Check graduates table
      const [graduateRows] = await pool.query('SELECT certificate_url, image_url FROM graduates WHERE certificate_url IS NOT NULL OR image_url IS NOT NULL');
      (graduateRows as any[]).forEach(row => {
        if (row.certificate_url) urls.push(row.certificate_url);
        if (row.image_url) urls.push(row.image_url);
      });

      // Add other tables as needed
      // You can extend this to check other tables that store file URLs

    } catch (error) {
      console.error('Error getting referenced file URLs:', error);
    }

    return urls.filter(Boolean);
  }

  /**
   * Extract filename from URL
   */
  private extractFilenameFromUrl(url: string): string | null {
    try {
      // Handle local URLs
      if (url.includes('/uploads/')) {
        const parts = url.split('/');
        return parts[parts.length - 1];
      }
      
      // Handle external URLs (for migration purposes)
      const urlObj = new URL(url);
      const pathname = urlObj.pathname;
      return pathname.substring(pathname.lastIndexOf('/') + 1);
    } catch {
      return null;
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats(): Promise<{
    totalFiles: number;
    totalSize: number;
    folderStats: Record<string, { files: number; size: number }>;
  }> {
    const stats = {
      totalFiles: 0,
      totalSize: 0,
      folderStats: {} as Record<string, { files: number; size: number }>
    };

    try {
      const folders = await readdir(this.uploadsDir, { withFileTypes: true });
      
      for (const folder of folders) {
        if (folder.isDirectory()) {
          const folderPath = join(this.uploadsDir, folder.name);
          const files = await readdir(folderPath);
          
          let folderSize = 0;
          for (const filename of files) {
            const filePath = join(folderPath, filename);
            const fileStat = await stat(filePath);
            folderSize += fileStat.size;
          }
          
          stats.folderStats[folder.name] = {
            files: files.length,
            size: folderSize
          };
          
          stats.totalFiles += files.length;
          stats.totalSize += folderSize;
        }
      }
    } catch (error) {
      console.error('Error getting storage stats:', error);
    }

    return stats;
  }

  /**
   * Validate and migrate old URLs to new format
   */
  async migrateFileUrls(): Promise<{
    migrated: number;
    errors: string[];
  }> {
    const result = {
      migrated: 0,
      errors: [] as string[]
    };

    try {
      // Migrate course images
      const [courses] = await pool.query('SELECT course_id, image_url FROM courses WHERE image_url IS NOT NULL');
      for (const course of courses as any[]) {
        if (this.isExternalUrl(course.image_url)) {
          try {
            const newUrl = this.convertToLocalUrl(course.image_url);
            await pool.query('UPDATE courses SET image_url = ? WHERE course_id = ?', [newUrl, course.course_id]);
            result.migrated++;
          } catch (error) {
            result.errors.push(`Failed to migrate course ${course.course_id}: ${error}`);
          }
        }
      }

      // Migrate downloadable files
      const [files] = await pool.query('SELECT file_id, file_url FROM downloadable_files WHERE file_url IS NOT NULL');
      for (const file of files as any[]) {
        if (this.isExternalUrl(file.file_url)) {
          try {
            const newUrl = this.convertToLocalUrl(file.file_url);
            await pool.query('UPDATE downloadable_files SET file_url = ? WHERE file_id = ?', [newUrl, file.file_id]);
            result.migrated++;
          } catch (error) {
            result.errors.push(`Failed to migrate file ${file.file_id}: ${error}`);
          }
        }
      }

      // Add other tables as needed

    } catch (error) {
      result.errors.push(`Migration failed: ${error}`);
    }

    return result;
  }

  /**
   * Check if URL is external
   */
  private isExternalUrl(url: string): boolean {
    return url.includes('imagekit.io') || 
           url.includes('cloudinary.com') || 
           url.includes('amazonaws.com') ||
           (url.startsWith('http') && !url.includes(process.env.NEXT_PUBLIC_DASHBOARD_URL || 'localhost'));
  }

  /**
   * Convert external URL to local URL format
   */
  private convertToLocalUrl(externalUrl: string): string {
    // Extract filename from external URL
    const filename = this.extractFilenameFromUrl(externalUrl);
    if (!filename) return externalUrl;

    // Determine folder based on URL pattern or default to 'migrated'
    let folder = 'migrated';
    if (externalUrl.includes('course')) folder = 'courses';
    if (externalUrl.includes('downloadable')) folder = 'downloadable_files';
    if (externalUrl.includes('graduate')) folder = 'graduates';

    return `/uploads/${folder}/${filename}`;
  }
}

// Default instance
export const fileManager = new FileManager();

// Utility functions
export async function cleanupOldFiles(olderThanDays: number = 7) {
  return fileManager.cleanupOrphanedFiles(olderThanDays);
}

export async function getStorageStatistics() {
  return fileManager.getStorageStats();
}

export async function findOrphanedFiles() {
  return fileManager.findOrphanedFiles();
}

export async function migrateExternalUrls() {
  return fileManager.migrateFileUrls();
}
