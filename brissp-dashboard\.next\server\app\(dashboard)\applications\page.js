(()=>{var e={};e.id=624,e.ids=[624],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},17618:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(70260),i=s(28203),r=s(25155),n=s.n(r),l=s(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o=["",{children:["(dashboard)",{children:["applications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,77724)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\applications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\applications\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/applications/page",pathname:"/applications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},50268:(e,t,s)=>{Promise.resolve().then(s.bind(s,77724))},8412:(e,t,s)=>{Promise.resolve().then(s.bind(s,41135))},41135:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var a=s(45512),i=s(58009),r=s(87021),n=s(13393),l=s(69193),d=s(77252),o=s(87664),c=s(82446),p=s(79041),x=s(71965);function m({application:e,isOpen:t,onClose:s,onApprove:i,onReject:n,onWaitlist:l,isLoading:c}){var p;return e?(0,a.jsx)(x.lG,{open:t,onOpenChange:s,children:(0,a.jsxs)(x.Cf,{className:"max-w-3xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(x.c7,{children:(0,a.jsx)(x.L3,{children:"Application Details"})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 py-4",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Course Information"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Course: ",e.course_title]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Student Type: ",e.student_type]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Study Mode: ",e.study_mode]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Academic Year: ",e.academic_year]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Intake: ",e.intake]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Personal Information"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Name: ",e.first_name," ",e.last_name," ",e.other_names||""]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Gender: ",e.gender]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Date of Birth: ",(0,o.A)(new Date(e.date_of_birth),"dd/MM/yyyy")]}),e.marital_status&&(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Marital Status: ",e.marital_status]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Contact Information"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Email: ",e.email]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Phone: ",e.phone_number]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Country: ",e.country]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Additional Information"}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Nationality: ",e.nationality]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["ID Number: ",e.id_number]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Application Status"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Current Status:"}),(p=e.status,(0,a.jsx)(d.E,{className:{pending:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",waitlisted:"bg-blue-100 text-blue-800"}[p],children:p.charAt(0).toUpperCase()+p.slice(1)}))]}),(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Application Date: ",(0,o.A)(new Date(e.application_date),"dd/MM/yyyy")]}),e.review_date&&(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Review Date: ",(0,o.A)(new Date(e.review_date),"dd/MM/yyyy")]}),e.review_notes&&(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Review Notes:"}),(0,a.jsx)("p",{className:"text-sm p-2 bg-gray-50 rounded",children:e.review_notes})]})]})]})]}),"pending"===e.status&&(0,a.jsxs)(x.Es,{className:"flex space-x-2",children:[(0,a.jsx)(r.$,{onClick:i,disabled:c,className:"bg-green-600 hover:bg-green-700",children:"Approve"}),(0,a.jsx)(r.$,{onClick:n,disabled:c,variant:"destructive",children:"Reject"}),(0,a.jsx)(r.$,{onClick:l,disabled:c,variant:"outline",children:"Waitlist"}),(0,a.jsx)(r.$,{onClick:s,disabled:c,variant:"outline",children:"Close"})]}),"pending"!==e.status&&(0,a.jsx)(x.Es,{children:(0,a.jsx)(r.$,{onClick:s,variant:"outline",children:"Close"})})]})}):null}let u=()=>{let[e,t]=(0,i.useState)([]),[s,x]=(0,i.useState)(!1),[u,h]=(0,i.useState)(null),[f,g]=(0,i.useState)(!1);(0,i.useEffect)(()=>{j()},[]);let j=async()=>{try{let e=await fetch("/api/applications"),s=await e.json();t(s)}catch(e){console.error("Error fetching applications:",e)}},y=async(e,t,s)=>{x(!0);try{let a=await fetch(`/api/applications/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:t,review_notes:s})});if(a.ok){if("approved"===t){let e=await a.json();e.userCreated&&alert(`Application approved! User account created with email: ${e.email}. A welcome email with login details has been sent.`)}j(),g(!1)}}catch(e){console.error("Error updating application:",e)}finally{x(!1)}},b=async e=>{if(confirm("Are you sure you want to delete this application?")){x(!0);try{(await fetch(`/api/applications/${e}`,{method:"DELETE"})).ok&&j()}catch(e){console.error("Error deleting application:",e)}finally{x(!1)}}},v=e=>{h(e),g(!0)},N=e=>(0,a.jsx)(d.E,{className:{pending:"bg-yellow-100 text-yellow-800",approved:"bg-green-100 text-green-800",rejected:"bg-red-100 text-red-800",waitlisted:"bg-blue-100 text-blue-800"}[e],children:e.charAt(0).toUpperCase()+e.slice(1)}),w=({applications:e})=>(0,a.jsxs)(n.XI,{children:[(0,a.jsx)(n.A0,{children:(0,a.jsxs)(n.Hj,{children:[(0,a.jsx)(n.nd,{children:"Date"}),(0,a.jsx)(n.nd,{children:"Name"}),(0,a.jsx)(n.nd,{children:"Course"}),(0,a.jsx)(n.nd,{children:"Type"}),(0,a.jsx)(n.nd,{children:"Mode"}),(0,a.jsx)(n.nd,{children:"Status"}),(0,a.jsx)(n.nd,{children:"Actions"})]})}),(0,a.jsx)(n.BF,{children:e.map(e=>(0,a.jsxs)(n.Hj,{children:[(0,a.jsx)(n.nA,{children:(0,o.A)(new Date(e.application_date),"dd/MM/yyyy")}),(0,a.jsxs)(n.nA,{children:[e.first_name," ",e.last_name,(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]}),(0,a.jsx)(n.nA,{children:e.course_title}),(0,a.jsx)(n.nA,{children:e.student_type}),(0,a.jsx)(n.nA,{children:e.study_mode}),(0,a.jsx)(n.nA,{children:N(e.status)}),(0,a.jsx)(n.nA,{children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(r.$,{size:"sm",variant:"outline",onClick:()=>v(e),className:"px-2",children:(0,a.jsx)(c.A,{className:"w-4 h-4"})}),"pending"===e.status&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.$,{size:"sm",onClick:()=>y(e.application_id,"approved"),disabled:s,className:"bg-green-600 hover:bg-green-700",children:"Approve"}),(0,a.jsx)(r.$,{size:"sm",onClick:()=>y(e.application_id,"rejected"),disabled:s,variant:"destructive",children:"Reject"}),(0,a.jsx)(r.$,{size:"sm",onClick:()=>y(e.application_id,"waitlisted"),disabled:s,variant:"outline",children:"Waitlist"})]}),"pending"!==e.status&&(0,a.jsx)(r.$,{size:"sm",variant:"destructive",onClick:()=>b(e.application_id),disabled:s,children:(0,a.jsx)(p.A,{className:"w-4 h-4"})})]})})]},e.application_id))})]}),_=e.filter(e=>"pending"===e.status),A=e.filter(e=>"pending"!==e.status);return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Applications Management"}),(0,a.jsxs)(l.tU,{defaultValue:"pending",className:"space-y-4",children:[(0,a.jsxs)(l.j7,{children:[(0,a.jsxs)(l.Xi,{value:"pending",children:["Pending Applications (",_.length,")"]}),(0,a.jsxs)(l.Xi,{value:"processed",children:["Processed Applications (",A.length,")"]})]}),(0,a.jsxs)(l.av,{value:"pending",className:"bg-white rounded-lg border p-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Pending Applications"}),(0,a.jsx)(w,{applications:_})]}),(0,a.jsxs)(l.av,{value:"processed",className:"bg-white rounded-lg border p-4",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"Processed Applications"}),(0,a.jsx)(w,{applications:A})]})]}),u&&(0,a.jsx)(m,{application:u,isOpen:f,onClose:()=>g(!1),onApprove:()=>y(u.application_id,"approved"),onReject:()=>y(u.application_id,"rejected"),onWaitlist:()=>y(u.application_id,"waitlisted"),isLoading:s})]})}},77252:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(45512);s(58009);var i=s(21643),r=s(59462);let n=(0,i.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return(0,a.jsx)("div",{className:(0,r.cn)(n({variant:t}),e),...s})}},71965:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>x,Es:()=>u,L3:()=>h,c7:()=>m,lG:()=>d,zM:()=>o});var a=s(45512),i=s(58009),r=s(32958),n=s(51255),l=s(59462);let d=r.bL,o=r.l9,c=r.ZL;r.bm;let p=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.hJ,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));p.displayName=r.hJ.displayName;let x=i.forwardRef(({className:e,children:t,...s},i)=>(0,a.jsxs)(c,{children:[(0,a.jsx)(p,{}),(0,a.jsxs)(r.UC,{ref:i,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,a.jsxs)(r.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));x.displayName=r.UC.displayName;let m=({className:e,...t})=>(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});m.displayName="DialogHeader";let u=({className:e,...t})=>(0,a.jsx)("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});u.displayName="DialogFooter";let h=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.hE,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=r.hE.displayName,i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.VY,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...t})).displayName=r.VY.displayName},69193:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>o,av:()=>c,j7:()=>d,tU:()=>l});var a=s(45512),i=s(58009),r=s(72366),n=s(59462);let l=r.bL,d=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=r.B8.displayName;let o=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));o.displayName=r.l9.displayName;let c=i.forwardRef(({className:e,...t},s)=>(0,a.jsx)(r.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));c.displayName=r.UC.displayName},77724:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\applications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\applications\\page.tsx","default")},82446:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(94825).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},79041:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[638,8403,7834,9267,4873,6562,7664,8449,2449],()=>s(17618));module.exports=a})();