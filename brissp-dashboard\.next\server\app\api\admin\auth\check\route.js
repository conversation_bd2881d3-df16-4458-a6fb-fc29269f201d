(()=>{var e={};e.id=1649,e.ids=[1649],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},5486:e=>{"use strict";e.exports=require("bcrypt")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},41865:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>c});var n=t(42706),i=t(28203),o=t(45994),u=t(39187),a=t(45369);async function c(e){try{console.log("Auth check request received");let r=e.cookies.get("admin_token")?.value;if(console.log("Token found:",!!r),!r)return console.log("No token found in cookies"),u.NextResponse.json({error:"Not authenticated"},{status:401});let t=(0,a.nr)(r);if(console.log("Token verification result:",!!t),!t)return console.log("Token verification failed"),u.NextResponse.json({error:"Invalid token"},{status:401});return console.log("Authentication successful"),u.NextResponse.json({authenticated:!0,user:{id:t.id,email:t.email,is_super_admin:t.is_super_admin}})}catch(e){return console.error("Auth check error:",e),u.NextResponse.json({error:"Authentication check failed"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/auth/check/route",pathname:"/api/admin/auth/check",filename:"route",bundlePath:"app/api/admin/auth/check/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\admin\\auth\\check\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},96487:()=>{},78335:()=>{},45369:(e,r,t)=>{"use strict";t.d(r,{B$:()=>h,Er:()=>d,HU:()=>l,dD:()=>v,nG:()=>x,nr:()=>m});var s=t(5486),n=t.n(s),i=t(43008),o=t.n(i),u=t(44512),a=t(39187),c=t(62545);let p=process.env.JWT_SECRET||"your-secret-key-change-this-in-production";async function d(e){return n().hash(e,10)}function l(e){return o().sign({id:e.admin_id,email:e.email,is_super_admin:e.is_super_admin},p,{expiresIn:"24h"})}function m(e){try{return o().verify(e,p)}catch{return null}}async function x(){let e=(0,u.UL)(),r=(await e).get("admin_token")?.value;if(!r)return null;let t=m(r);if(!t)return null;try{let[e]=await c.A.query("SELECT admin_id, email, full_name, is_super_admin FROM admin_users WHERE admin_id = ?",[t.id]);if(0===e.length)return null;return e[0]}catch(e){return console.error("Error fetching current admin:",e),null}}async function h(e){let r=e.cookies.get("admin_token")?.value;return r?m(r)?null:a.NextResponse.json({error:"Invalid token"},{status:401}):a.NextResponse.json({error:"Unauthorized"},{status:401})}async function v(e){let r=e.cookies.get("admin_token")?.value;if(!r)return a.NextResponse.json({error:"Unauthorized"},{status:401});let t=m(r);return t&&t.is_super_admin?null:a.NextResponse.json({error:"Forbidden: Requires super admin privileges"},{status:403})}},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});var s=t(60820),n=t(29021),i=t.n(n),o=t(33873),u=t.n(o);let a=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(u().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820,8935],()=>t(41865));module.exports=s})();