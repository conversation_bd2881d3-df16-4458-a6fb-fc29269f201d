(()=>{var e={};e.id=6563,e.ids=[6563],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},55511:e=>{"use strict";e.exports=require("crypto")},79748:e=>{"use strict";e.exports=require("fs/promises")},33873:e=>{"use strict";e.exports=require("path")},73917:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>c,serverHooks:()=>f,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>m});var i={};r.r(i),r.d(i,{POST:()=>p,config:()=>d});var s=r(42706),o=r(28203),a=r(45994),n=r(39187),l=r(77922);async function p(e){try{let t=await e.formData(),r=t.get("file"),i=t.get("folder")||"uploads";if(!r)return n.NextResponse.json({error:"No file provided"},{status:400});let s=await l.oB.uploadFile(r,i);return n.NextResponse.json({url:s.url,filename:s.filename,originalName:s.originalName,size:s.size,mimeType:s.mimeType})}catch(e){return console.error("Error uploading file:",e),n.NextResponse.json({error:e instanceof Error?e.message:"Failed to upload file"},{status:500})}}let d={api:{bodyParser:!1}},c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/upload/cloud/route",pathname:"/api/upload/cloud",filename:"route",bundlePath:"app/api/upload/cloud/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\upload\\cloud\\route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:u,workUnitAsyncStorage:m,serverHooks:f}=c;function g(){return(0,a.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:m})}},96487:()=>{},78335:()=>{},77922:(e,t,r)=>{"use strict";r.d(t,{oB:()=>f,cy:()=>x});var i=r(79748),s=r(33873),o=r(55511),a=r.n(o);let n=new Uint8Array(256),l=n.length,p=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i,d=[];for(let e=0;e<256;++e)d.push((e+256).toString(16).substr(1));let c=function(e,t=0){let r=(d[e[t+0]]+d[e[t+1]]+d[e[t+2]]+d[e[t+3]]+"-"+d[e[t+4]]+d[e[t+5]]+"-"+d[e[t+6]]+d[e[t+7]]+"-"+d[e[t+8]]+d[e[t+9]]+"-"+d[e[t+10]]+d[e[t+11]]+d[e[t+12]]+d[e[t+13]]+d[e[t+14]]+d[e[t+15]]).toLowerCase();if(!("string"==typeof r&&p.test(r)))throw TypeError("Stringified UUID is invalid");return r},u=function(e,t,r){let i=(e=e||{}).random||(e.rng||function(){return l>n.length-16&&(a().randomFillSync(n),l=0),n.slice(l,l+=16)})();if(i[6]=15&i[6]|64,i[8]=63&i[8]|128,t){r=r||0;for(let e=0;e<16;++e)t[r+e]=i[e];return t}return c(i)};class m{constructor(e="public/uploads",t="/uploads"){this.baseDir=e,this.baseUrl=t}validateFile(e,t={}){let{maxSize:r=0xa00000,allowedTypes:i=["image/jpeg","image/png","image/gif","image/webp","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/plain","text/csv"],allowedExtensions:s=[".jpg",".jpeg",".png",".gif",".webp",".pdf",".doc",".docx",".xls",".xlsx",".txt",".csv"]}=t;if(e.size>r)return{valid:!1,error:`File size exceeds maximum allowed size of ${Math.round(r/1048576)}MB`};if(!i.includes(e.type))return{valid:!1,error:`File type ${e.type} is not allowed`};let o=this.getFileExtension(e.name);return s.includes(o.toLowerCase())?{valid:!0}:{valid:!1,error:`File extension ${o} is not allowed`}}async uploadFile(e,t="general"){let r=this.validateFile(e);if(!r.valid)throw Error(r.error);let o=this.getFileExtension(e.name),a=`${u()}${o}`,n=(0,s.join)(this.baseDir,t),l=(0,s.join)(n,a);try{await (0,i.mkdir)(n,{recursive:!0});let r=await e.arrayBuffer(),s=Buffer.from(r);return await (0,i.writeFile)(l,s),{url:`${this.baseUrl}/${t}/${a}`,filename:a,originalName:e.name,size:e.size,mimeType:e.type,path:l}}catch(e){throw console.error("Error uploading file:",e),Error("Failed to upload file")}}async deleteFile(e){try{return await (0,i.unlink)(e),!0}catch(e){return console.error("Error deleting file:",e),!1}}async fileExists(e){try{return await (0,i.access)(e),!0}catch{return!1}}getFileExtension(e){return e.substring(e.lastIndexOf("."))}getFileUrl(e,t){return`${this.baseUrl}/${e}/${t}`}parseFileUrl(e){let t=e.replace(this.baseUrl+"/","").split("/");if(t.length>=2){let e=t.pop();return{folder:t.join("/"),filename:e}}return null}getFilePathFromUrl(e){let t=this.parseFileUrl(e);return t?(0,s.join)(this.baseDir,t.folder,t.filename):null}}let f=new m,g={images:{types:["image/jpeg","image/png","image/gif","image/webp"],extensions:[".jpg",".jpeg",".png",".gif",".webp"]},documents:{types:["application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/plain","text/csv"],extensions:[".pdf",".doc",".docx",".xls",".xlsx",".txt",".csv"]},all:{types:["image/jpeg","image/png","image/gif","image/webp","application/pdf","application/msword","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.ms-excel","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","text/plain","text/csv"],extensions:[".jpg",".jpeg",".png",".gif",".webp",".pdf",".doc",".docx",".xls",".xlsx",".txt",".csv"]}};function x(e,t){return{allowedTypes:g[e].types,allowedExtensions:g[e].extensions,maxSize:t}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[638,5452],()=>r(73917));module.exports=i})();