(()=>{var e={};e.id=2949,e.ids=[2949],e.modules={28303:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=28303,e.exports=t},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},74306:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>y,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>c,PUT:()=>l});var i=r(42706),o=r(28203),a=r(45994),n=r(39187),p=r(62545);async function u(e){try{let{searchParams:t}=new URL(e.url),r=t.get("status"),s=t.get("type"),i=t.get("university"),o=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),u=(o-1)*a,c=`
      SELECT * FROM final_year_project_applications
    `,l=[],d=[];r&&(d.push("status = ?"),l.push(r)),s&&(d.push("project_type = ?"),l.push(s)),i&&(d.push("university LIKE ?"),l.push(`%${i}%`)),d.length>0&&(c+=" WHERE "+d.join(" AND ")),c+=" ORDER BY application_date DESC LIMIT ? OFFSET ?",l.push(a,u);let[_]=await p.A.query(c,l),x="SELECT COUNT(*) as total FROM final_year_project_applications",y=[];d.length>0&&(x+=" WHERE "+d.join(" AND "),r&&y.push(r),s&&y.push(s),i&&y.push(`%${i}%`));let[f]=await p.A.query(x,y),h=f[0].total;return n.NextResponse.json({applications:_,pagination:{page:o,limit:a,total:h,totalPages:Math.ceil(h/a)}})}catch(e){return console.error("Error fetching FYP applications:",e),n.NextResponse.json({error:"Failed to fetch applications"},{status:500})}}async function c(e){try{let{student_name:t,email:r,phone:s,university:i,course_of_study:o,year_of_study:a,student_id:u,supervisor_name:c,supervisor_email:l,project_title:d,project_description:_,project_type:x,research_area:y,methodology:f,expected_outcomes:h,timeline_weeks:v,required_resources:j,technical_requirements:m,preferred_supervisor_expertise:q,project_deadline:g,defense_date:E,university_requirements:R,additional_notes:w}=await e.json();if(!t||!r||!s||!i||!o||!a||!x)return n.NextResponse.json({error:"Missing required fields"},{status:400});let[A]=await p.A.query(`INSERT INTO final_year_project_applications (
        student_name, email, phone, university, course_of_study, year_of_study,
        student_id, supervisor_name, supervisor_email, project_title,
        project_description, project_type, research_area, methodology,
        expected_outcomes, timeline_weeks, required_resources, technical_requirements,
        preferred_supervisor_expertise, project_deadline, defense_date,
        university_requirements, additional_notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,[t,r,s,i,o,a,u,c,l,d,_,x,y,f,h,v,j,m,q,g,E,R,w]);return n.NextResponse.json({message:"Application submitted successfully",application_id:A.insertId},{status:201})}catch(e){return console.error("Error creating FYP application:",e),n.NextResponse.json({error:"Failed to submit application"},{status:500})}}async function l(e){try{let{application_id:t,status:r,review_notes:s,assigned_supervisor:i}=await e.json();if(!t||!r)return n.NextResponse.json({error:"Application ID and status are required"},{status:400});let[o]=await p.A.query(`UPDATE final_year_project_applications 
       SET status = ?, review_notes = ?, assigned_supervisor = ?, review_date = NOW()
       WHERE application_id = ?`,[r,s,i,t]);if(0===o.affectedRows)return n.NextResponse.json({error:"Application not found"},{status:404});return n.NextResponse.json({message:"Application updated successfully"})}catch(e){return console.error("Error updating FYP application:",e),n.NextResponse.json({error:"Failed to update application"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/fyp-applications/route",pathname:"/api/fyp-applications",filename:"route",bundlePath:"app/api/fyp-applications/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\fyp-applications\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:x,serverHooks:y}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},62545:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(60820),i=r(29021),o=r.n(i),a=r(33873),n=r.n(a);let p=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452,820],()=>r(74306));module.exports=s})();