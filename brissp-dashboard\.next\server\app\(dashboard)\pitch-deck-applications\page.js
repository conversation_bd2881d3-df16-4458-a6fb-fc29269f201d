(()=>{var e={};e.id=7173,e.ids=[7173],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},27800:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>o});var s=a(70260),r=a(28203),i=a(25155),n=a.n(i),l=a(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(t,d);let o=["",{children:["(dashboard)",{children:["pitch-deck-applications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,86087)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\pitch-deck-applications\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(a.t.bind(a,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\pitch-deck-applications\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/pitch-deck-applications/page",pathname:"/pitch-deck-applications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},34021:(e,t,a)=>{Promise.resolve().then(a.bind(a,86087))},44293:(e,t,a)=>{Promise.resolve().then(a.bind(a,73479))},73479:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var s=a(45512),r=a(58009),i=a(87021),n=a(13393),l=a(77252),d=a(97643),o=a(54069),c=a(25409),p=a(57631),x=a(55469);let m=(0,a(94825).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);var u=a(25637),h=a(5899),f=a(82446),g=a(79041),j=a(28531),b=a.n(j);function y(){let[e,t]=(0,r.useState)([]),[a,j]=(0,r.useState)(!0),[y,v]=(0,r.useState)("all"),[w,N]=(0,r.useState)(""),[k,A]=(0,r.useState)({page:1,limit:10,total:0,totalPages:0}),[P,C]=(0,r.useState)({total:0,pending:0,inProgress:0,completed:0}),R=async()=>{try{j(!0);let e=new URLSearchParams({page:k.page.toString(),limit:k.limit.toString()});console.log("Fetching applications with statusFilter:",y),y&&"all"!==y?(e.append("status",y),console.log("Adding status filter to API call:",y)):console.log("Not adding status filter (showing all)");let a=`/api/pitch-deck-applications?${e}`;console.log("API URL:",a);let s=await fetch(a),r=await s.json();console.log("Pitch Deck Applications API Response:",r),console.log("Applications received:",r.applications?.length||0),r.applications&&r.applications.length>0&&console.log("Sample application data:",r.applications[0]),t(r.applications||[]),A(e=>({...e,total:r.pagination?.total||0,totalPages:r.pagination?.totalPages||0}))}catch(e){console.error("Error fetching applications:",e),t([]),alert("Error fetching applications. Please check the console for details.")}finally{j(!1)}},_=async()=>{try{let[e,t,a,s]=await Promise.all([fetch("/api/pitch-deck-applications"),fetch("/api/pitch-deck-applications?status=pending"),fetch("/api/pitch-deck-applications?status=in-progress"),fetch("/api/pitch-deck-applications?status=completed")]),[r,i,n,l]=await Promise.all([e.json(),t.json(),a.json(),s.json()]);C({total:r.pagination?.total||0,pending:i.pagination?.total||0,inProgress:n.pagination?.total||0,completed:l.pagination?.total||0})}catch(e){console.error("Error fetching stats:",e)}},B=async e=>{if(confirm("Are you sure you want to delete this application?"))try{(await fetch(`/api/pitch-deck-applications/${e}`,{method:"DELETE"})).ok&&(R(),_())}catch(e){console.error("Error deleting application:",e)}},L=e=>{let t={pending:{variant:"secondary",label:"Pending"},reviewed:{variant:"outline",label:"Reviewed"},accepted:{variant:"default",label:"Accepted"},rejected:{variant:"destructive",label:"Rejected"},"in-progress":{variant:"default",label:"In Progress"},completed:{variant:"default",label:"Completed"}},a=t[e]||t.pending;return(0,s.jsx)(l.E,{variant:a.variant,children:a.label})},S=e=>(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{"pre-seed":"bg-blue-100 text-blue-800",seed:"bg-green-100 text-green-800","series-a":"bg-purple-100 text-purple-800","series-b":"bg-orange-100 text-orange-800","later-stage":"bg-red-100 text-red-800"}[e]||"bg-gray-100 text-gray-800"}`,children:e.replace("-"," ").toUpperCase()}),q=e.filter(e=>{let t="all"===y||e.status===y,a=!w||e.applicant_name&&e.applicant_name.toLowerCase().includes(w.toLowerCase())||e.email&&e.email.toLowerCase().includes(w.toLowerCase())||e.company_name&&e.company_name.toLowerCase().includes(w.toLowerCase()),s=t&&a;return console.log("Filter result:",{statusMatch:t,searchMatch:a,finalResult:s}),s});return(0,s.jsxs)("div",{className:"p-6 space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Pitch Deck Applications"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Manage startup pitch deck development applications"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:"Total Applications"}),(0,s.jsx)(p.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:P.total})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:"Pending Review"}),(0,s.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:P.pending})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:"In Progress"}),(0,s.jsx)(m,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:P.inProgress})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(d.ZB,{className:"text-sm font-medium",children:"Completed"}),(0,s.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"text-2xl font-bold",children:P.completed})})]})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{children:"Filters"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(h.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,s.jsx)(c.p,{placeholder:"Search by name, email, or company...",value:w,onChange:e=>N(e.target.value),className:"pl-10"})]})}),(0,s.jsxs)(o.l6,{value:y,onValueChange:v,children:[(0,s.jsx)(o.bq,{className:"w-48",children:(0,s.jsx)(o.yv,{placeholder:"Filter by status"})}),(0,s.jsxs)(o.gC,{children:[(0,s.jsx)(o.eb,{value:"all",children:"All Statuses"}),(0,s.jsx)(o.eb,{value:"pending",children:"Pending"}),(0,s.jsx)(o.eb,{value:"reviewed",children:"Reviewed"}),(0,s.jsx)(o.eb,{value:"accepted",children:"Accepted"}),(0,s.jsx)(o.eb,{value:"rejected",children:"Rejected"}),(0,s.jsx)(o.eb,{value:"in-progress",children:"In Progress"}),(0,s.jsx)(o.eb,{value:"completed",children:"Completed"})]})]})]})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsxs)(d.ZB,{children:["Applications (",q.length,")"]})}),(0,s.jsx)(d.Wu,{children:a?(0,s.jsx)("div",{className:"text-center py-8",children:"Loading applications..."}):0===e.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{children:"No pitch deck applications found."}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Make sure the database table exists and contains data."})]}):0===q.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("p",{children:"No applications match your current filters."}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Try adjusting your search terms or filters."})]}):(0,s.jsxs)(n.XI,{children:[(0,s.jsx)(n.A0,{children:(0,s.jsxs)(n.Hj,{children:[(0,s.jsx)(n.nd,{children:"Applicant"}),(0,s.jsx)(n.nd,{children:"Company"}),(0,s.jsx)(n.nd,{children:"Funding Stage"}),(0,s.jsx)(n.nd,{children:"Budget Range"}),(0,s.jsx)(n.nd,{children:"Status"}),(0,s.jsx)(n.nd,{children:"Applied Date"}),(0,s.jsx)(n.nd,{children:"Actions"})]})}),(0,s.jsx)(n.BF,{children:q.map(e=>(0,s.jsxs)(n.Hj,{children:[(0,s.jsx)(n.nA,{children:(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-medium",children:e.applicant_name}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,s.jsx)(n.nA,{children:e.company_name||"N/A"}),(0,s.jsx)(n.nA,{children:S(e.funding_stage)}),(0,s.jsx)(n.nA,{children:(0,s.jsx)("span",{className:"capitalize",children:e.budget_range})}),(0,s.jsx)(n.nA,{children:L(e.status)}),(0,s.jsx)(n.nA,{children:new Date(e.application_date).toLocaleDateString()}),(0,s.jsx)(n.nA,{children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(b(),{href:`/pitch-deck-applications/${e.application_id}`,children:(0,s.jsx)(i.$,{variant:"outline",size:"sm",children:(0,s.jsx)(f.A,{className:"w-4 h-4"})})}),(0,s.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>B(e.application_id),children:(0,s.jsx)(g.A,{className:"w-4 h-4"})})]})})]},e.application_id))})]})})]}),k.totalPages>1&&(0,s.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,s.jsx)(i.$,{variant:"outline",onClick:()=>A(e=>({...e,page:Math.max(1,e.page-1)})),disabled:1===k.page,children:"Previous"}),(0,s.jsxs)("span",{className:"flex items-center px-4",children:["Page ",k.page," of ",k.totalPages]}),(0,s.jsx)(i.$,{variant:"outline",onClick:()=>A(e=>({...e,page:Math.min(e.totalPages,e.page+1)})),disabled:k.page===k.totalPages,children:"Next"})]})]})}},77252:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var s=a(45512);a(58009);var r=a(21643),i=a(59462);let n=(0,r.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...a}){return(0,s.jsx)("div",{className:(0,i.cn)(n({variant:t}),e),...a})}},97643:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>l,wL:()=>p});var s=a(45512),r=a(58009),i=a(59462);let n=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let l=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let o=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent";let p=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));p.displayName="CardFooter"},25409:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(45512),r=a(58009),i=a(59462);let n=r.forwardRef(({className:e,type:t,...a},r)=>(0,s.jsx)("input",{type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...a}));n.displayName="Input"},54069:(e,t,a)=>{"use strict";a.d(t,{bq:()=>x,eb:()=>f,gC:()=>h,l6:()=>c,yv:()=>p});var s=a(45512),r=a(58009),i=a(96096),n=a(7833),l=a(36624),d=a(24999),o=a(59462);let c=i.bL;i.YJ;let p=i.WT,x=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(i.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));x.displayName=i.l9.displayName;let m=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.PP,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}));m.displayName=i.PP.displayName;let u=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.wn,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})}));u.displayName=i.wn.displayName;let h=r.forwardRef(({className:e,children:t,position:a="popper",...r},n)=>(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{ref:n,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,s.jsx)(m,{}),(0,s.jsx)(i.LM,{className:(0,o.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,s.jsx)(u,{})]})}));h.displayName=i.UC.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.JU,{ref:a,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=i.JU.displayName;let f=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(i.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),(0,s.jsx)(i.p4,{children:t})]}));f.displayName=i.q7.displayName,r.forwardRef(({className:e,...t},a)=>(0,s.jsx)(i.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=i.wv.displayName},86087:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\pitch-deck-applications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\pitch-deck-applications\\page.tsx","default")},25637:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(94825).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},55469:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(94825).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},82446:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(94825).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},5899:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(94825).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},79041:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[638,8403,7834,9267,4873,6562,3015,2449],()=>a(27800));module.exports=s})();