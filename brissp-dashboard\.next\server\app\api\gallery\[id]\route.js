(()=>{var e={};e.id=9078,e.ids=[9078],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},14734:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{DELETE:()=>c,GET:()=>p,PUT:()=>l});var o=t(42706),i=t(28203),n=t(45994),a=t(39187),u=t(62545);async function p(e){try{let r=e.url.split("/").pop();if(!r)return a.NextResponse.json({error:"Missing gallery ID"},{status:400});let[t]=await u.A.query("SELECT * FROM gallery WHERE gallery_id = ? AND is_active = true",[r]);if(!t[0])return a.NextResponse.json({error:"Gallery not found"},{status:404});return a.NextResponse.json(t[0])}catch(e){return console.error(e),a.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function l(e){try{let r=e.url.split("/").pop();if(!r)return a.NextResponse.json({error:"Missing gallery ID"},{status:400});let{image_title:t,image_description:s,image_type:o,display_order:i,is_active:n}=await e.json(),[p]=await u.A.query(`UPDATE gallery
             SET image_title = COALESCE(?, image_title),
                 image_description = COALESCE(?, image_description),
                 image_type = COALESCE(?, image_type),
                 display_order = COALESCE(?, display_order),
                 is_active = COALESCE(?, is_active)
             WHERE gallery_id = ?`,[t,s,o,i,n,r]);if(0===p.affectedRows)return a.NextResponse.json({error:"Gallery not found"},{status:404});return a.NextResponse.json({message:"Updated successfully"})}catch(e){return console.error(e),a.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function c(e){try{let r=e.url.split("/").pop();if(!r)return a.NextResponse.json({error:"Missing gallery ID"},{status:400});let[t]=await u.A.query("UPDATE gallery SET is_active = false WHERE gallery_id = ?",[r]);if(0===t.affectedRows)return a.NextResponse.json({error:"Gallery not found"},{status:404});return a.NextResponse.json({message:"Deleted successfully"})}catch(e){return console.error(e),a.NextResponse.json({error:"Internal Server Error"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/gallery/[id]/route",pathname:"/api/gallery/[id]",filename:"route",bundlePath:"app/api/gallery/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\gallery\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:g}=d;function E(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(60820),o=t(29021),i=t.n(o),n=t(33873),a=t.n(n);let u=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(a().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(14734));module.exports=s})();