(()=>{var e={};e.id=673,e.ids=[673],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},28642:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var i=r(70260),s=r(28203),o=r(25155),a=r.n(o),n=r(67292),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);r.d(t,d);let l=["",{children:["(dashboard)",{children:["notice-board",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,57781)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\[id]\\edit\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/notice-board/[id]/edit/page",pathname:"/notice-board/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},87789:(e,t,r)=>{Promise.resolve().then(r.bind(r,57781))},51405:(e,t,r)=>{Promise.resolve().then(r.bind(r,46441))},46441:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var i=r(45512),s=r(58009),o=r(35564),a=r(79334);let n=()=>{let e=(0,a.useParams)(),t=e?.id,[r,n]=(0,s.useState)(null),[d,l]=(0,s.useState)(!0),[p,c]=(0,s.useState)(null);(0,s.useEffect)(()=>{t&&(async()=>{try{let e=await fetch(`/api/notices/${t}`);if(!e.ok)throw Error(`Error: ${e.status}`);let r=await e.json();r.publish_date&&(r.publish_date=r.publish_date.split("T")[0]),r.expiry_date&&(r.expiry_date=r.expiry_date.split("T")[0]),n(r)}catch(e){console.error("Failed to fetch notice:",e),c("Failed to load notice data. Please try again.")}finally{l(!1)}})()},[t]);let u=async e=>{l(!0),c(null);try{let r=await fetch(`/api/notices/${t}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!r.ok)throw Error(`Error: ${r.status}`);window.location.href="/notice-board"}catch(e){console.error("Failed to update notice:",e),c("Failed to update notice. Please try again."),l(!1)}};return d?(0,i.jsx)("div",{className:"p-6",children:"Loading..."}):p?(0,i.jsx)("div",{className:"p-6 text-red-500",children:p}):(0,i.jsxs)("div",{className:"p-6 ",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Edit Notice"}),(0,i.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[r&&(0,i.jsx)(o.A,{initialData:r,onSubmit:u,isLoading:d}),!r&&!d&&(0,i.jsx)("div",{className:"text-red-500",children:"Notice not found"})]})]})}},57781:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\notice-board\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\notice-board\\[id]\\edit\\page.tsx","default")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[638,8403,7834,9267,4873,1066],()=>r(28642));module.exports=i})();