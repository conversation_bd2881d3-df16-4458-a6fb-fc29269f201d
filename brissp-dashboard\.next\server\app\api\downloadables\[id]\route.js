(()=>{var e={};e.id=9277,e.ids=[9277],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},13738:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>R,routeModule:()=>d,serverHooks:()=>E,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>x});var t={};s.r(t),s.d(t,{DELETE:()=>c,GET:()=>l,PUT:()=>p});var o=s(42706),i=s(28203),n=s(45994),u=s(39187),a=s(62545);async function l(e){try{let r=e.url.split("/").pop();if(!r)return u.NextResponse.json({error:"Missing file ID"},{status:400});let[s]=await a.A.query(`SELECT f.*, c.title as course_title
       FROM downloadable_files f
       LEFT JOIN courses c ON f.course_id = c.course_id
       WHERE f.file_id = ?`,[r]);if(!s[0])return u.NextResponse.json({error:"File not found"},{status:404});return u.NextResponse.json(s[0])}catch(e){return console.error("Error fetching file:",e),u.NextResponse.json({error:"Failed to fetch file"},{status:500})}}async function p(e){try{let r=e.url.split("/").pop();if(!r)return u.NextResponse.json({error:"Missing file ID"},{status:400});let s=await e.json(),{file_name:t,file_url:o,description:i,course_id:n,file_type:l,file_size:p}=s;if(!t||!o)return u.NextResponse.json({error:"File name and URL are required"},{status:400});let[c]=await a.A.query(`UPDATE downloadable_files SET
       file_name = ?,
       file_url = ?,
       description = ?,
       course_id = ?,
       file_type = COALESCE(?, file_type),
       file_size = COALESCE(?, file_size)
       WHERE file_id = ?`,[t,o,i||null,n||null,l,p,r]);if(0===c.affectedRows)return u.NextResponse.json({error:"File not found"},{status:404});return u.NextResponse.json({id:r,...s})}catch(e){return console.error("Error updating file:",e),u.NextResponse.json({error:"Failed to update file"},{status:500})}}async function c(e){try{let r=e.url.split("/").pop();if(!r)return u.NextResponse.json({error:"Missing file ID"},{status:400});let[s]=await a.A.query("DELETE FROM downloadable_files WHERE file_id = ?",[r]);if(0===s.affectedRows)return u.NextResponse.json({error:"File not found"},{status:404});return u.NextResponse.json({message:"File deleted successfully"})}catch(e){return console.error("Error deleting file:",e),u.NextResponse.json({error:"Failed to delete file"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/downloadables/[id]/route",pathname:"/api/downloadables/[id]",filename:"route",bundlePath:"app/api/downloadables/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\downloadables\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:f,workUnitAsyncStorage:x,serverHooks:E}=d;function R(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},62545:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60820),o=s(29021),i=s.n(o),n=s(33873),u=s.n(n);let a=t.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:i().readFileSync(u().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,5452,820],()=>s(13738));module.exports=t})();