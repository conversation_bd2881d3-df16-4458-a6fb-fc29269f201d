"use strict";(()=>{var e={};e.id=39,e.ids=[39],e.modules={75600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},96762:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},16990:(e,t,n)=>{n.r(t),n.d(t,{config:()=>S,default:()=>A,routeModule:()=>_});var r={};n.r(r),n.d(r,{default:()=>P});var i=n(89947),o=n(2706),s=n(96762);let a=require("mysql2/promise");var l=n.n(a);let u=require("fs");var d=n.n(u);let c=require("path");var f=n.n(c);let p=l().createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:d().readFileSync(f().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0});async function P(e,t){if("GET"===e.method){let e=await p.query("SELECT * FROM downloadable_files");t.status(200).json(e)}else if("POST"===e.method){let{title:n,file_url:r,file_type:i,file_size:o}=e.body;await p.query("INSERT INTO downloadable_files (title, file_url, file_type, file_size) VALUES (?, ?, ?, ?)",[n,r,i,o]),t.status(201).json({message:"File added successfully"})}}let A=(0,s.M)(r,"default"),S=(0,s.M)(r,"config"),_=new i.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/downloadable_files",pathname:"/api/downloadable_files",bundlePath:"",filename:""},userland:r})},2706:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},89947:(e,t,n)=>{e.exports=n(75600)}};var t=require("../../webpack-api-runtime.js");t.C(e);var n=t(t.s=16990);module.exports=n})();