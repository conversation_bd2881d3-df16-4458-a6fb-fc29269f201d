(()=>{var e={};e.id=7908,e.ids=[7908],e.modules={28303:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=28303,e.exports=t},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},95005:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>_});var s={};r.r(s),r.d(s,{DELETE:()=>d,GET:()=>c,PUT:()=>u});var i=r(42706),o=r(28203),a=r(45994),n=r(39187),p=r(62545);async function c(e,{params:t}){try{let e=(await t).id;if(!e)return n.NextResponse.json({error:"Missing application ID"},{status:400});let[r]=await p.A.query("SELECT * FROM pitch_deck_applications WHERE application_id = ?",[e]);if(!r[0])return n.NextResponse.json({error:"Application not found"},{status:404});let[s]=await p.A.query(`SELECT * FROM pitch_deck_progress 
       WHERE application_id = ? 
       ORDER BY milestone`,[e]);return n.NextResponse.json({application:r[0],progress:s})}catch(e){return console.error("Error fetching pitch deck application:",e),n.NextResponse.json({error:"Failed to fetch application"},{status:500})}}async function u(e,{params:t}){try{let r=(await t).id,s=await e.json();if(!r)return n.NextResponse.json({error:"Missing application ID"},{status:400});let{status:i,review_notes:o,assigned_consultant:a,project_start_date:c,project_completion_date:u,final_pitch_deck_url:d,success_metrics:l}=s,[x]=await p.A.query(`UPDATE pitch_deck_applications 
       SET status = ?, review_notes = ?, assigned_consultant = ?, 
           project_start_date = ?, project_completion_date = ?, 
           final_pitch_deck_url = ?, success_metrics = ?, 
           review_date = NOW()
       WHERE application_id = ?`,[i,o,a,c,u,d,l,r]);if(0===x.affectedRows)return n.NextResponse.json({error:"Application not found"},{status:404});return n.NextResponse.json({message:"Application updated successfully"})}catch(e){return console.error("Error updating pitch deck application:",e),n.NextResponse.json({error:"Failed to update application"},{status:500})}}async function d(e,{params:t}){try{let e=(await t).id;if(!e)return n.NextResponse.json({error:"Missing application ID"},{status:400});let[r]=await p.A.query("DELETE FROM pitch_deck_applications WHERE application_id = ?",[e]);if(0===r.affectedRows)return n.NextResponse.json({error:"Application not found"},{status:404});return n.NextResponse.json({message:"Application deleted successfully"})}catch(e){return console.error("Error deleting pitch deck application:",e),n.NextResponse.json({error:"Failed to delete application"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/pitch-deck-applications/[id]/route",pathname:"/api/pitch-deck-applications/[id]",filename:"route",bundlePath:"app/api/pitch-deck-applications/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\pitch-deck-applications\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:_,serverHooks:f}=l;function E(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:_})}},96487:()=>{},78335:()=>{},62545:(e,t,r)=>{"use strict";r.d(t,{A:()=>p});var s=r(60820),i=r(29021),o=r.n(i),a=r(33873),n=r.n(a);let p=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,5452,820],()=>r(95005));module.exports=s})();