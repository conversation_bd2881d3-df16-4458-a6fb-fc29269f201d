"use strict";exports.id=8449,exports.ids=[8449],exports.modules={32958:(e,t,n)=>{n.d(t,{bm:()=>eh,UC:()=>eg,VY:()=>eE,hJ:()=>eb,ZL:()=>ey,bL:()=>em,hE:()=>ew,l9:()=>ev});var r,o=n(58009),i=n(31412),a=n(29952),s=n(6004),u=n(30096),l=n(13024),d=n(55740),c=n(12705),f=n(45512),p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=o.forwardRef((e,n)=>{let{asChild:r,...o}=e,i=r?c.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,f.jsx)(i,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),m=n(92828),v=n(99850),y="dismissableLayer.update",b=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),g=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:s,onPointerDownOutside:u,onFocusOutside:l,onInteractOutside:d,onDismiss:c,...g}=e,h=o.useContext(b),[D,x]=o.useState(null),R=D?.ownerDocument??globalThis?.document,[,C]=o.useState({}),N=(0,a.s)(t,e=>x(e)),I=Array.from(h.layers),[T]=[...h.layersWithOutsidePointerEventsDisabled].slice(-1),j=I.indexOf(T),F=D?I.indexOf(D):-1,O=h.layersWithOutsidePointerEventsDisabled.size>0,P=F>=j,L=function(e,t=globalThis?.document){let n=(0,m.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){E("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...h.branches].some(e=>e.contains(t));!P||n||(u?.(e),d?.(e),e.defaultPrevented||c?.())},R),A=function(e,t=globalThis?.document){let n=(0,m.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&E("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...h.branches].some(e=>e.contains(t))||(l?.(e),d?.(e),e.defaultPrevented||c?.())},R);return(0,v.U)(e=>{F!==h.layers.size-1||(s?.(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))},R),o.useEffect(()=>{if(D)return n&&(0===h.layersWithOutsidePointerEventsDisabled.size&&(r=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),h.layersWithOutsidePointerEventsDisabled.add(D)),h.layers.add(D),w(),()=>{n&&1===h.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=r)}},[D,R,n,h]),o.useEffect(()=>()=>{D&&(h.layers.delete(D),h.layersWithOutsidePointerEventsDisabled.delete(D),w())},[D,h]),o.useEffect(()=>{let e=()=>C({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,f.jsx)(p.div,{...g,ref:N,style:{pointerEvents:O?P?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,A.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,A.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,L.onPointerDownCapture)})});function w(){let e=new CustomEvent(y);document.dispatchEvent(e)}function E(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&o.addEventListener(e,t,{once:!0}),r)?o&&d.flushSync(()=>o.dispatchEvent(i)):o.dispatchEvent(i)}g.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(b),r=o.useRef(null),i=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,f.jsx)(p.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var h="focusScope.autoFocusOnMount",D="focusScope.autoFocusOnUnmount",x={bubbles:!1,cancelable:!0},R=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:s,...u}=e,[l,d]=o.useState(null),c=(0,m.c)(i),v=(0,m.c)(s),y=o.useRef(null),b=(0,a.s)(t,e=>d(e)),g=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(g.paused||!l)return;let t=e.target;l.contains(t)?y.current=t:I(y.current,{select:!0})},t=function(e){if(g.paused||!l)return;let t=e.relatedTarget;null===t||l.contains(t)||I(y.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&I(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,g.paused]),o.useEffect(()=>{if(l){T.add(g);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(h,x);l.addEventListener(h,c),l.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(I(r,{select:t}),document.activeElement!==n)return}(C(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&I(l))}return()=>{l.removeEventListener(h,c),setTimeout(()=>{let t=new CustomEvent(D,x);l.addEventListener(D,v),l.dispatchEvent(t),t.defaultPrevented||I(e??document.body,{select:!0}),l.removeEventListener(D,v),T.remove(g)},0)}}},[l,c,v,g]);let w=o.useCallback(e=>{if(!n&&!r||g.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=C(e);return[N(t,e),N(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&I(i,{select:!0})):(e.preventDefault(),n&&I(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,g.paused]);return(0,f.jsx)(p.div,{tabIndex:-1,...u,ref:b,onKeyDown:w})});function C(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function N(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function I(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}R.displayName="FocusScope";var T=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=j(e,t)).unshift(t)},remove(t){e=j(e,t),e[0]?.resume()}}}();function j(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var F=n(49397),O=o.forwardRef((e,t)=>{let{container:n,...r}=e,[i,a]=o.useState(!1);(0,F.N)(()=>a(!0),[]);let s=n||i&&globalThis?.document?.body;return s?d.createPortal((0,f.jsx)(p.div,{...r,ref:t}),s):null});O.displayName="Portal";var P=n(98060),L=n(19632),A=n(67783),S=n(72421),M="Dialog",[_,k]=(0,s.A)(M),[U,K]=_(M),B=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:i,onOpenChange:a,modal:s=!0}=e,d=o.useRef(null),c=o.useRef(null),[p=!1,m]=(0,l.i)({prop:r,defaultProp:i,onChange:a});return(0,f.jsx)(U,{scope:t,triggerRef:d,contentRef:c,contentId:(0,u.B)(),titleId:(0,u.B)(),descriptionId:(0,u.B)(),open:p,onOpenChange:m,onOpenToggle:o.useCallback(()=>m(e=>!e),[m]),modal:s,children:n})};B.displayName=M;var W="DialogTrigger",$=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(W,n),s=(0,a.s)(t,o.triggerRef);return(0,f.jsx)(p.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eu(o.open),...r,ref:s,onClick:(0,i.m)(e.onClick,o.onOpenToggle)})});$.displayName=W;var V="DialogPortal",[z,H]=_(V,{forceMount:void 0}),X=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:i}=e,a=K(V,t);return(0,f.jsx)(z,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,f.jsx)(P.C,{present:n||a.open,children:(0,f.jsx)(O,{asChild:!0,container:i,children:e})}))})};X.displayName=V;var q="DialogOverlay",G=o.forwardRef((e,t)=>{let n=H(q,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=K(q,e.__scopeDialog);return i.modal?(0,f.jsx)(P.C,{present:r||i.open,children:(0,f.jsx)(Z,{...o,ref:t})}):null});G.displayName=q;var Z=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(q,n);return(0,f.jsx)(A.A,{as:c.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,f.jsx)(p.div,{"data-state":eu(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),J="DialogContent",Y=o.forwardRef((e,t)=>{let n=H(J,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=K(J,e.__scopeDialog);return(0,f.jsx)(P.C,{present:r||i.open,children:i.modal?(0,f.jsx)(Q,{...o,ref:t}):(0,f.jsx)(ee,{...o,ref:t})})});Y.displayName=J;var Q=o.forwardRef((e,t)=>{let n=K(J,e.__scopeDialog),r=o.useRef(null),s=(0,a.s)(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,S.Eq)(e)},[]),(0,f.jsx)(et,{...e,ref:s,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,i.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault())})}),ee=o.forwardRef((e,t)=>{let n=K(J,e.__scopeDialog),r=o.useRef(!1),i=o.useRef(!1);return(0,f.jsx)(et,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||n.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let o=t.target;n.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),et=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:i,onCloseAutoFocus:s,...u}=e,l=K(J,n),d=o.useRef(null),c=(0,a.s)(t,d);return(0,L.Oh)(),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(R,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:i,onUnmountAutoFocus:s,children:(0,f.jsx)(g,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":eu(l.open),...u,ref:c,onDismiss:()=>l.onOpenChange(!1)})}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(ef,{titleId:l.titleId}),(0,f.jsx)(ep,{contentRef:d,descriptionId:l.descriptionId})]})]})}),en="DialogTitle",er=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(en,n);return(0,f.jsx)(p.h2,{id:o.titleId,...r,ref:t})});er.displayName=en;var eo="DialogDescription",ei=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(eo,n);return(0,f.jsx)(p.p,{id:o.descriptionId,...r,ref:t})});ei.displayName=eo;var ea="DialogClose",es=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=K(ea,n);return(0,f.jsx)(p.button,{type:"button",...r,ref:t,onClick:(0,i.m)(e.onClick,()=>o.onOpenChange(!1))})});function eu(e){return e?"open":"closed"}es.displayName=ea;var el="DialogTitleWarning",[ed,ec]=(0,s.q)(el,{contentName:J,titleName:en,docsSlug:"dialog"}),ef=({titleId:e})=>{let t=ec(el),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},ep=({contentRef:e,descriptionId:t})=>{let n=ec("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return o.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(r)},[r,e,t]),null},em=B,ev=$,ey=X,eb=G,eg=Y,ew=er,eE=ei,eh=es},98060:(e,t,n)=>{n.d(t,{C:()=>a});var r=n(58009),o=n(29952),i=n(49397),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef({}),l=r.useRef(e),d=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>n[e][t]??e,t));return r.useEffect(()=>{let e=s(u.current);d.current="mounted"===c?e:"none"},[c]),(0,i.N)(()=>{let t=u.current,n=l.current;if(n!==e){let r=d.current,o=s(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),l.current=e}},[e,f]),(0,i.N)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,n=n=>{let r=s(u.current).includes(n.animationName);if(n.target===o&&r&&(f("ANIMATION_END"),!l.current)){let n=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=n)})}},r=e=>{e.target===o&&(d.current=s(u.current))};return o.addEventListener("animationstart",r),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",r),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),l=(0,o.s)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:l}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},72366:(e,t,n)=>{n.d(t,{UC:()=>Z,B8:()=>q,bL:()=>X,l9:()=>G});var r=n(58009),o=n(31412),i=n(6004),a=n(29952),s=n(12705),u=n(45512),l=n(30096);n(55740);var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,i=r?s.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),c=n(92828),f=n(13024),p=n(59018),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},y="RovingFocusGroup",[b,g,w]=function(e){let t=e+"CollectionProvider",[n,o]=(0,i.A)(t),[l,d]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,u.jsx)(l,{scope:t,itemMap:i,collectionRef:o,children:n})};c.displayName=t;let f=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=d(f,n),i=(0,a.s)(t,o.collectionRef);return(0,u.jsx)(s.DX,{ref:i,children:r})});p.displayName=f;let m=e+"CollectionItemSlot",v="data-radix-collection-item",y=r.forwardRef((e,t)=>{let{scope:n,children:o,...i}=e,l=r.useRef(null),c=(0,a.s)(t,l),f=d(m,n);return r.useEffect(()=>(f.itemMap.set(l,{ref:l,...i}),()=>void f.itemMap.delete(l))),(0,u.jsx)(s.DX,{[v]:"",ref:c,children:o})});return y.displayName=m,[{Provider:c,Slot:p,ItemSlot:y},function(t){let n=d(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${v}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},o]}(y),[E,h]=(0,i.A)(y,[w]),[D,x]=E(y),R=r.forwardRef((e,t)=>(0,u.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(C,{...e,ref:t})})}));R.displayName=y;var C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:s=!1,dir:l,currentTabStopId:y,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:w,onEntryFocus:E,preventScrollOnEntryFocus:h=!1,...x}=e,R=r.useRef(null),C=(0,a.s)(t,R),N=(0,p.jH)(l),[I=null,T]=(0,f.i)({prop:y,defaultProp:b,onChange:w}),[F,O]=r.useState(!1),P=(0,c.c)(E),L=g(n),A=r.useRef(!1),[S,M]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(m,P),()=>e.removeEventListener(m,P)},[P]),(0,u.jsx)(D,{scope:n,orientation:i,dir:N,loop:s,currentTabStopId:I,onItemFocus:r.useCallback(e=>T(e),[T]),onItemShiftTab:r.useCallback(()=>O(!0),[]),onFocusableItemAdd:r.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>M(e=>e-1),[]),children:(0,u.jsx)(d.div,{tabIndex:F||0===S?-1:0,"data-orientation":i,...x,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{A.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!A.current;if(e.target===e.currentTarget&&t&&!F){let t=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===I),...e].filter(Boolean).map(e=>e.ref.current),h)}}A.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>O(!1))})})}),N="RovingFocusGroupItem",I=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:s,...c}=e,f=(0,l.B)(),p=s||f,m=x(N,n),v=m.currentTabStopId===p,y=g(n),{onFocusableItemAdd:w,onFocusableItemRemove:E}=m;return r.useEffect(()=>{if(i)return w(),()=>E()},[i,w,E]),(0,u.jsx)(b.ItemSlot,{scope:n,id:p,focusable:i,active:a,children:(0,u.jsx)(d.span,{tabIndex:v?0:-1,"data-orientation":m.orientation,...c,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?m.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=m.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>j(n))}})})})});I.displayName=N;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var F=n(98060),O=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,i=r?s.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),P="Tabs",[L,A]=(0,i.A)(P,[h]),S=h(),[M,_]=L(P),k=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:a="horizontal",dir:s,activationMode:d="automatic",...c}=e,m=(0,p.jH)(s),[v,y]=(0,f.i)({prop:r,onChange:o,defaultProp:i});return(0,u.jsx)(M,{scope:n,baseId:(0,l.B)(),value:v,onValueChange:y,orientation:a,dir:m,activationMode:d,children:(0,u.jsx)(O.div,{dir:m,"data-orientation":a,...c,ref:t})})});k.displayName=P;var U="TabsList",K=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=_(U,n),a=S(n);return(0,u.jsx)(R,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:(0,u.jsx)(O.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});K.displayName=U;var B="TabsTrigger",W=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...a}=e,s=_(B,n),l=S(n),d=z(s.baseId,r),c=H(s.baseId,r),f=r===s.value;return(0,u.jsx)(I,{asChild:!0,...l,focusable:!i,active:f,children:(0,u.jsx)(O.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":c,"data-state":f?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:d,...a,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;f||i||!e||s.onValueChange(r)})})})});W.displayName=B;var $="TabsContent",V=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:a,...s}=e,l=_($,n),d=z(l.baseId,o),c=H(l.baseId,o),f=o===l.value,p=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(F.C,{present:i||f,children:({present:n})=>(0,u.jsx)(O.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":d,hidden:!n,id:c,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&a})})});function z(e,t){return`${e}-trigger-${t}`}function H(e,t){return`${e}-content-${t}`}V.displayName=$;var X=k,q=K,G=W,Z=V}};