(()=>{var e={};e.id=8195,e.ids=[8195],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},3296:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>l,GET:()=>p,PUT:()=>a});var u=t(42706),o=t(28203),i=t(45994),n=t(39187),c=t(62545);async function p(e){try{let r=e.url.split("/").pop();if(!r)return n.NextResponse.json({error:"Missing curriculum ID"},{status:400});let[t]=await c.A.query("SELECT * FROM curriculum WHERE curriculum_id = ?",[r]);if(!t[0])return n.NextResponse.json({error:"Curriculum not found"},{status:404});return n.NextResponse.json(t[0])}catch(e){return console.error(e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function a(e){try{let r=e.url.split("/").pop();if(!r)return n.NextResponse.json({error:"Missing curriculum ID"},{status:400});let{course_id:t,week_number:s,topic:u,content:o,learning_objectives:i}=await e.json(),[p]=await c.A.query(`UPDATE curriculum 
       SET course_id = ?, week_number = ?, topic = ?, content = ?, learning_objectives = ?
       WHERE curriculum_id = ?`,[t,s,u,o,i,r]);if(0===p.affectedRows)return n.NextResponse.json({error:"Curriculum not found"},{status:404});return n.NextResponse.json({success:!0})}catch(e){return console.error(e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function l(e){try{let r=e.url.split("/").pop();if(!r)return n.NextResponse.json({error:"Missing curriculum ID"},{status:400});let[t]=await c.A.query("DELETE FROM curriculum WHERE curriculum_id = ?",[r]);if(0===t.affectedRows)return n.NextResponse.json({error:"Curriculum not found"},{status:404});return n.NextResponse.json({success:!0})}catch(e){return console.error(e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}let d=new u.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/curriculum/[id]/route",pathname:"/api/curriculum/[id]",filename:"route",bundlePath:"app/api/curriculum/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\curriculum\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:m,serverHooks:v}=d;function f(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:m})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var s=t(60820),u=t(29021),o=t.n(u),i=t(33873),n=t.n(i);let c=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(3296));module.exports=s})();