(()=>{var e={};e.id=6834,e.ids=[6834],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},82495:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>R,routeModule:()=>l,serverHooks:()=>v,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var t={};s.r(t),s.d(t,{DELETE:()=>d,GET:()=>c,PUT:()=>p});var o=s(42706),u=s(28203),n=s(45994),i=s(39187),a=s(62545);async function c(e){try{let r=e.url.split("/").pop();if(!r)return i.NextResponse.json({error:"Missing course ID"},{status:400});let[s]=await a.A.query("SELECT * FROM courses WHERE course_id = ?",[r]),t=s[0];if(!t)return i.NextResponse.json({error:"Course not found"},{status:404});return i.NextResponse.json(t)}catch(e){return console.error(e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function p(e){try{let r=e.url.split("/").pop();if(!r)return i.NextResponse.json({error:"Missing course ID"},{status:400});let{title:s,description:t,duration_months:o,price:u,department:n,category:c,image_url:p,program_type:d,num_lectures:l,skill_level:x,languages:f,class_days:v,course_code:R}=await e.json(),[E]=await a.A.query(`UPDATE courses SET
        title = ?, description = ?, duration_months = ?, 
        price = ?, department = ?, category = ?, 
        image_url = ?, program_type = ?, num_lectures = ?,
        skill_level = ?, languages = ?, class_days = ?,
        course_code = ?
      WHERE course_id = ?`,[s,t,o,u,n,c,p,d,l,x,f,v,R,r]);if(0===E.affectedRows)return i.NextResponse.json({error:"Course not found"},{status:404});return i.NextResponse.json({message:"Course updated successfully"})}catch(e){return console.error(e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function d(e){try{let r=e.url.split("/").pop();if(!r)return i.NextResponse.json({error:"Missing course ID"},{status:400});let[s]=await a.A.query("DELETE FROM courses WHERE course_id = ?",[r]);if(0===s.affectedRows)return i.NextResponse.json({error:"Course not found"},{status:404});return i.NextResponse.json({message:"Course deleted successfully"},{status:200})}catch(e){return console.error(e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}let l=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/courses/[id]/route",pathname:"/api/courses/[id]",filename:"route",bundlePath:"app/api/courses/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\courses\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:v}=l;function R(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},96487:()=>{},78335:()=>{},62545:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60820),o=s(29021),u=s.n(o),n=s(33873),i=s.n(n);let a=t.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:u().readFileSync(i().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,5452,820],()=>s(82495));module.exports=t})();