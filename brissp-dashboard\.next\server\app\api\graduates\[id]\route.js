(()=>{var e={};e.id=4242,e.ids=[4242],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11723:e=>{"use strict";e.exports=require("querystring")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},61527:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>E,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{DELETE:()=>l,GET:()=>p,PUT:()=>d});var a=t(42706),o=t(28203),i=t(45994),u=t(39187),n=t(62545),c=t(49647);async function p(e){try{let r=e.url.split("/").pop();if(!r)return u.NextResponse.json({error:"Missing graduate ID"},{status:400});let[t]=await n.A.query("SELECT * FROM graduates WHERE graduate_id = ?",[r]);if(!t[0])return u.NextResponse.json({error:"Graduate not found"},{status:404});let s=t[0],[a]=await n.A.query("SELECT * FROM projects WHERE graduate_id = ?",[r]),[o]=await n.A.query("SELECT * FROM social_links WHERE graduate_id = ?",[r]);return u.NextResponse.json({...s,projects:a,social_links:o})}catch(e){return console.error(e),u.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function d(e){let r=await n.A.getConnection();await r.beginTransaction();try{let t;let s=e.url.split("/").pop();if(!s)return u.NextResponse.json({error:"Missing graduate ID"},{status:400});let a=await e.formData(),o=a.get("certificate_file");o&&(t=await (0,c.R)(o,"certificates"));let i=JSON.parse(a.get("graduateData")),n=JSON.parse(a.get("projects")),p=JSON.parse(a.get("social_links"));for(let e of(await r.query("UPDATE graduates SET ? WHERE graduate_id = ?",[{...i,...t&&{certificate_file_url:t}},s]),await r.query("DELETE FROM projects WHERE graduate_id = ?",[s]),await r.query("DELETE FROM social_links WHERE graduate_id = ?",[s]),n))await r.query("INSERT INTO projects SET ?",{...e,graduate_id:s});for(let e of p)await r.query("INSERT INTO social_links SET ?",{...e,graduate_id:s});return await r.commit(),u.NextResponse.json({success:!0})}catch(e){return await r.rollback(),console.error(e),u.NextResponse.json({error:"Internal Server Error"},{status:500})}finally{r.release()}}async function l(e){try{let r=e.url.split("/").pop();if(!r)return u.NextResponse.json({error:"Missing graduate ID"},{status:400});let[t]=await n.A.query("DELETE FROM graduates WHERE graduate_id = ?",[r]);if(0===t.affectedRows)return u.NextResponse.json({error:"Graduate not found"},{status:404});return u.NextResponse.json({success:!0})}catch(e){return console.error(e),u.NextResponse.json({error:"Internal Server Error"},{status:500})}}let E=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/graduates/[id]/route",pathname:"/api/graduates/[id]",filename:"route",bundlePath:"app/api/graduates/[id]/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\graduates\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:f}=E;function R(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},96487:()=>{},78335:()=>{},49647:(e,r,t)=>{"use strict";t.d(r,{R:()=>a});var s=t(97873);async function a(e,r){try{let t=await e.arrayBuffer(),a=Buffer.from(t);return(await new Promise((e,t)=>{s.v2.uploader.upload_stream({folder:r,resource_type:"auto"},(r,s)=>{r?t(r):s?e(s):t(Error("No result from Cloudinary"))}).end(a)})).secure_url}catch(e){throw console.error("Error uploading to Cloudinary:",e),e}}s.v2.config({cloud_name:process.env.CLOUDINARY_CLOUD_NAME,api_key:process.env.CLOUDINARY_API_KEY,api_secret:process.env.CLOUDINARY_API_SECRET})},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var s=t(60820),a=t(29021),o=t.n(a),i=t(33873),u=t.n(i);let n=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(u().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820,7873],()=>t(61527));module.exports=s})();