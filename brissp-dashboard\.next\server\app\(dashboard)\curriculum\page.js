(()=>{var e={};e.id=3952,e.ids=[3952],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},53950:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var t=s(70260),i=s(28203),n=s(25155),a=s.n(n),o=s(67292),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(r,d);let c=["",{children:["(dashboard)",{children:["curriculum",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,64088)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/curriculum/page",pathname:"/curriculum",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},16616:(e,r,s)=>{Promise.resolve().then(s.bind(s,64088))},3464:(e,r,s)=>{Promise.resolve().then(s.bind(s,68436))},68436:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>p});var t=s(45512),i=s(58009),n=s(87021),a=s(13393),o=s(46335),d=s(95758),c=s(79041),l=s(28531),u=s.n(l);let p=()=>{let[e,r]=(0,i.useState)([]);(0,i.useEffect)(()=>{s()},[]);let s=async()=>{try{let e=await fetch("/api/curriculum"),s=await e.json();r(s)}catch(e){console.error("Error fetching curriculum:",e)}},l=async e=>{if(confirm("Are you sure you want to delete this curriculum item?"))try{(await fetch(`/api/curriculum/${e}`,{method:"DELETE"})).ok&&s()}catch(e){console.error("Error deleting curriculum:",e)}};return(0,t.jsxs)("div",{className:"p-6 ",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold",children:"Curriculum Management"}),(0,t.jsx)(u(),{href:"/curriculum/new",children:(0,t.jsxs)(n.$,{children:[(0,t.jsx)(o.A,{className:"w-4 h-4 mr-2"}),"Add New Curriculum"]})})]}),(0,t.jsx)("div",{className:"bg-white rounded-lg border",children:(0,t.jsxs)(a.XI,{children:[(0,t.jsx)(a.A0,{children:(0,t.jsxs)(a.Hj,{children:[(0,t.jsx)(a.nd,{children:"Course ID"}),(0,t.jsx)(a.nd,{children:"Week"}),(0,t.jsx)(a.nd,{children:"Topic"}),(0,t.jsx)(a.nd,{children:"Learning Objectives"}),(0,t.jsx)(a.nd,{children:"Actions"})]})}),(0,t.jsx)(a.BF,{children:e.map(e=>(0,t.jsxs)(a.Hj,{children:[(0,t.jsx)(a.nA,{children:e.course_id}),(0,t.jsx)(a.nA,{children:e.week_number}),(0,t.jsx)(a.nA,{children:e.topic}),(0,t.jsx)(a.nA,{children:e.learning_objectives}),(0,t.jsxs)(a.nA,{className:"flex space-x-2",children:[(0,t.jsx)(u(),{href:`/curriculum/${e.curriculum_id}/edit`,children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(d.A,{className:"w-4 h-4"})})}),(0,t.jsx)(n.$,{variant:"destructive",size:"sm",onClick:()=>l(e.curriculum_id),children:(0,t.jsx)(c.A,{className:"w-4 h-4"})})]})]},e.curriculum_id))})]})})]})}},64088:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\curriculum\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\page.tsx","default")},95758:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(94825).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},46335:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(94825).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},79041:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(94825).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,8403,7834,9267,4873,2449],()=>s(53950));module.exports=t})();