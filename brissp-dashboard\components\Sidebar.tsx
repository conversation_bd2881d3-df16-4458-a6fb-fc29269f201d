"use client";

import Link from "next/link"
import { useState, useEffect } from "react"
import { BarChart2, BellDot, GraduationCap, LogOut, ClipboardList, Award, Download, BookOpen, Layers, Key, Presentation, Briefcase, FileText, Menu, X, Cpu } from "lucide-react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

interface SidebarProps {
  isOpen?: boolean;
  onToggle?: () => void;
}

const Sidebar = ({ isOpen = false, onToggle }: SidebarProps) => {
  const router = useRouter();
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Close sidebar when clicking outside on mobile
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMobile && isOpen && onToggle) {
        const sidebar = document.getElementById('mobile-sidebar');
        if (sidebar && !sidebar.contains(event.target as Node)) {
          onToggle();
        }
      }
    };

    if (isMobile && isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isMobile, isOpen, onToggle]);

  // Prevent body scroll when mobile sidebar is open
  useEffect(() => {
    if (isMobile && isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobile, isOpen]);

  const handleLogout = async () => {
    try {
      const response = await fetch("/api/admin/auth/logout", {
        method: "POST",
        credentials: "include",
      });

      if (response.ok) {
        toast.success("Logged out successfully");
        router.push("/");
      } else {
        toast.error("Failed to log out");
      }
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("An error occurred during logout");
    }
  };

  const handleLinkClick = () => {
    if (isMobile && onToggle) {
      onToggle();
    }
  };

  const navigationItems = [
    { href: "/panel", icon: BarChart2, label: "Dashboard" },
    { href: "/applications", icon: ClipboardList, label: "Course Applications" },
    { href: "/pitch-deck-applications", icon: Presentation, label: "Pitch Deck Applications" },
    { href: "/internship-applications", icon: Briefcase, label: "Internship Applications" },
    { href: "/fyp-applications", icon: FileText, label: "FYP Applications" },
    { href: "/innovation-lab-applications", icon: Cpu, label: "Innovation Lab Applications" },
    { href: "/results", icon: Award, label: "Results" },
    { href: "/downloadables", icon: Download, label: "Resources" },
    { href: "/courses", icon: BookOpen, label: "Courses" },
    { href: "/curriculum", icon: Layers, label: "Curriculum" },
    { href: "/notice-board", icon: BellDot, label: "Notice Board" },
    { href: "/graduates", icon: GraduationCap, label: "Graduates" },
    { href: "/password-management", icon: Key, label: "Password Management" },
  ];

  return (
    <>
      {/* Mobile Overlay */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onToggle}
        />
      )}

      {/* Sidebar */}
      <div
        id="mobile-sidebar"
        className={`
          ${isMobile
            ? `fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${
                isOpen ? 'translate-x-0' : '-translate-x-full'
              }`
            : 'fixed inset-y-0 left-0 w-64'
          }
          border-r bg-white h-screen flex flex-col
        `}
      >
        {/* Header */}
        <div className="p-4 border-b flex items-center justify-between">
          <Link href="/panel" className="flex items-center space-x-2" onClick={handleLinkClick}>
            <span className="font-semibold text-lg">Brissp Dash</span>
          </Link>
          {isMobile && (
            <button
              type="button"
              onClick={onToggle}
              className="p-2 rounded-lg hover:bg-gray-100 md:hidden"
              aria-label="Close navigation menu"
              title="Close navigation menu"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Navigation */}
        <nav className="p-4 space-y-2 flex-grow overflow-y-auto">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.href}
                href={item.href}
                onClick={handleLinkClick}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200"
              >
                <Icon className="w-5 h-5 flex-shrink-0" />
                <span className="truncate">{item.label}</span>
              </Link>
            );
          })}
        </nav>

        {/* Logout Button */}
        <div className="p-4 border-t mt-auto">
          <button
            type="button"
            onClick={handleLogout}
            className="flex items-center space-x-2 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 w-full transition-colors duration-200"
          >
            <LogOut className="w-5 h-5 flex-shrink-0" />
            <span>Logout</span>
          </button>
        </div>
      </div>
    </>
  )
}

// Mobile Header Component with Hamburger Menu
export const MobileHeader = ({ onToggle }: { onToggle: () => void }) => {
  return (
    <div className="md:hidden bg-white border-b px-4 py-3 flex items-center justify-between sticky top-0 z-30">
      <button
        type="button"
        onClick={onToggle}
        className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
        aria-label="Toggle navigation menu"
        title="Toggle navigation menu"
      >
        <Menu className="w-6 h-6" />
      </button>
      <h1 className="font-semibold text-lg">Brissp Dashboard</h1>
      <div className="w-10" /> {/* Spacer for centering */}
    </div>
  );
};

export default Sidebar