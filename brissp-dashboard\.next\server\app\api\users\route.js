(()=>{var e={};e.id=318,e.ids=[318],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},35146:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>v,routeModule:()=>x,serverHooks:()=>R,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>f});var t={};s.r(t),s.d(t,{DELETE:()=>l,GET:()=>p,POST:()=>c,PUT:()=>d});var o=s(42706),n=s(28203),u=s(45994),i=s(39187),a=s(62545);async function p(){try{let e=`
      SELECT user_id, first_name, last_name, email, is_active, created_at, updated_at
      FROM users
      WHERE is_active = true
      ORDER BY first_name, last_name
    `,[r]=await a.A.query(e);return i.NextResponse.json(r)}catch(e){return console.error("Error fetching users:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function c(e){try{let{first_name:r,last_name:s,email:t,password:o}=await e.json();if(!r||!s||!t||!o)return i.NextResponse.json({error:"Missing required fields"},{status:400});let[n]=await a.A.query("SELECT user_id FROM users WHERE email = ?",[t]);if(Array.isArray(n)&&n.length>0)return i.NextResponse.json({error:"Email already exists"},{status:409});let[u]=await a.A.query(`INSERT INTO users (first_name, last_name, email, password)
       VALUES (?, ?, ?, ?)`,[r,s,t,o]);return i.NextResponse.json({id:u.insertId},{status:201})}catch(e){return console.error("Error creating user:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function d(e){try{let{searchParams:r}=new URL(e.url),s=r.get("userId");if(!s)return i.NextResponse.json({error:"User ID is required"},{status:400});let{first_name:t,last_name:o,email:n,is_active:u}=await e.json(),p=[],c=[];if(t&&(p.push("first_name = ?"),c.push(t)),o&&(p.push("last_name = ?"),c.push(o)),n&&(p.push("email = ?"),c.push(n)),"boolean"==typeof u&&(p.push("is_active = ?"),c.push(u)),0===p.length)return i.NextResponse.json({error:"No fields to update"},{status:400});c.push(s);let[d]=await a.A.query(`UPDATE users SET ${p.join(", ")} WHERE user_id = ?`,c);if(0===d.affectedRows)return i.NextResponse.json({error:"User not found"},{status:404});return i.NextResponse.json({success:!0})}catch(e){return console.error("Error updating user:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function l(e){try{let{searchParams:r}=new URL(e.url),s=r.get("userId");if(!s)return i.NextResponse.json({error:"User ID is required"},{status:400});let[t]=await a.A.query("UPDATE users SET is_active = false WHERE user_id = ?",[s]);if(0===t.affectedRows)return i.NextResponse.json({error:"User not found"},{status:404});return i.NextResponse.json({success:!0})}catch(e){return console.error("Error deleting user:",e),i.NextResponse.json({error:"Internal Server Error"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:E,workUnitAsyncStorage:f,serverHooks:R}=x;function v(){return(0,u.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:f})}},96487:()=>{},78335:()=>{},62545:(e,r,s)=>{"use strict";s.d(r,{A:()=>a});var t=s(60820),o=s(29021),n=s.n(o),u=s(33873),i=s.n(u);let a=t.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:n().readFileSync(i().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,5452,820],()=>s(35146));module.exports=t})();