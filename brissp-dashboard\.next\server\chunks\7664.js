"use strict";exports.id=7664,exports.ids=[7664],exports.modules={87664:(t,e,n)=>{function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(t,e){if(e.length<t)throw TypeError(t+" argument"+(t>1?"s":"")+" required, but only "+e.length+" present")}function i(t){a(1,arguments);var e=Object.prototype.toString.call(t);return t instanceof Date||"object"===r(t)&&"[object Date]"===e?new Date(t.getTime()):"number"==typeof t||"[object Number]"===e?new Date(t):(("string"==typeof t||"[object String]"===e)&&"undefined"!=typeof console&&(console.warn("Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments"),console.warn(Error().stack)),new Date(NaN))}function o(t){if(null===t||!0===t||!1===t)return NaN;var e=Number(t);return isNaN(e)?e:e<0?Math.ceil(e):Math.floor(e)}function u(t){a(1,arguments);var e=i(t),n=e.getUTCDay();return e.setUTCDate(e.getUTCDate()-((n<1?7:0)+n-1)),e.setUTCHours(0,0,0,0),e}function d(t){a(1,arguments);var e=i(t),n=e.getUTCFullYear(),r=new Date(0);r.setUTCFullYear(n+1,0,4),r.setUTCHours(0,0,0,0);var o=u(r),d=new Date(0);d.setUTCFullYear(n,0,4),d.setUTCHours(0,0,0,0);var s=u(d);return e.getTime()>=o.getTime()?n+1:e.getTime()>=s.getTime()?n:n-1}n.d(e,{A:()=>F});var s={};function l(t,e){a(1,arguments);var n,r,u,d,l,c,h,f,m=o(null!==(n=null!==(r=null!==(u=null!==(d=null==e?void 0:e.weekStartsOn)&&void 0!==d?d:null==e?void 0:null===(l=e.locale)||void 0===l?void 0:null===(c=l.options)||void 0===c?void 0:c.weekStartsOn)&&void 0!==u?u:s.weekStartsOn)&&void 0!==r?r:null===(h=s.locale)||void 0===h?void 0:null===(f=h.options)||void 0===f?void 0:f.weekStartsOn)&&void 0!==n?n:0);if(!(m>=0&&m<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");var g=i(t),v=g.getUTCDay();return g.setUTCDate(g.getUTCDate()-((v<m?7:0)+v-m)),g.setUTCHours(0,0,0,0),g}function c(t,e){a(1,arguments);var n,r,u,d,c,h,f,m,g=i(t),v=g.getUTCFullYear(),w=o(null!==(n=null!==(r=null!==(u=null!==(d=null==e?void 0:e.firstWeekContainsDate)&&void 0!==d?d:null==e?void 0:null===(c=e.locale)||void 0===c?void 0:null===(h=c.options)||void 0===h?void 0:h.firstWeekContainsDate)&&void 0!==u?u:s.firstWeekContainsDate)&&void 0!==r?r:null===(f=s.locale)||void 0===f?void 0:null===(m=f.options)||void 0===m?void 0:m.firstWeekContainsDate)&&void 0!==n?n:1);if(!(w>=1&&w<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var b=new Date(0);b.setUTCFullYear(v+1,0,w),b.setUTCHours(0,0,0,0);var y=l(b,e),p=new Date(0);p.setUTCFullYear(v,0,w),p.setUTCHours(0,0,0,0);var T=l(p,e);return g.getTime()>=y.getTime()?v+1:g.getTime()>=T.getTime()?v:v-1}function h(t,e){for(var n=Math.abs(t).toString();n.length<e;)n="0"+n;return(t<0?"-":"")+n}let f={y:function(t,e){var n=t.getUTCFullYear(),r=n>0?n:1-n;return h("yy"===e?r%100:r,e.length)},M:function(t,e){var n=t.getUTCMonth();return"M"===e?String(n+1):h(n+1,2)},d:function(t,e){return h(t.getUTCDate(),e.length)},h:function(t,e){return h(t.getUTCHours()%12||12,e.length)},H:function(t,e){return h(t.getUTCHours(),e.length)},m:function(t,e){return h(t.getUTCMinutes(),e.length)},s:function(t,e){return h(t.getUTCSeconds(),e.length)},S:function(t,e){var n=e.length;return h(Math.floor(t.getUTCMilliseconds()*Math.pow(10,n-3)),e.length)}};var m={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"};function g(t,e){var n=t>0?"-":"+",r=Math.abs(t),a=Math.floor(r/60),i=r%60;return 0===i?n+String(a):n+String(a)+(e||"")+h(i,2)}function v(t,e){return t%60==0?(t>0?"-":"+")+h(Math.abs(t)/60,2):w(t,e)}function w(t,e){var n=Math.abs(t);return(t>0?"-":"+")+h(Math.floor(n/60),2)+(e||"")+h(n%60,2)}let b={G:function(t,e,n){var r=t.getUTCFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});default:return n.era(r,{width:"wide"})}},y:function(t,e,n){if("yo"===e){var r=t.getUTCFullYear();return n.ordinalNumber(r>0?r:1-r,{unit:"year"})}return f.y(t,e)},Y:function(t,e,n,r){var a=c(t,r),i=a>0?a:1-a;return"YY"===e?h(i%100,2):"Yo"===e?n.ordinalNumber(i,{unit:"year"}):h(i,e.length)},R:function(t,e){return h(d(t),e.length)},u:function(t,e){return h(t.getUTCFullYear(),e.length)},Q:function(t,e,n){var r=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"Q":return String(r);case"QQ":return h(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(t,e,n){var r=Math.ceil((t.getUTCMonth()+1)/3);switch(e){case"q":return String(r);case"qq":return h(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(t,e,n){var r=t.getUTCMonth();switch(e){case"M":case"MM":return f.M(t,e);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(t,e,n){var r=t.getUTCMonth();switch(e){case"L":return String(r+1);case"LL":return h(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(t,e,n,r){var u=function(t,e){a(1,arguments);var n=i(t);return Math.round((l(n,e).getTime()-(function(t,e){a(1,arguments);var n,r,i,u,d,h,f,m,g=o(null!==(n=null!==(r=null!==(i=null!==(u=null==e?void 0:e.firstWeekContainsDate)&&void 0!==u?u:null==e?void 0:null===(d=e.locale)||void 0===d?void 0:null===(h=d.options)||void 0===h?void 0:h.firstWeekContainsDate)&&void 0!==i?i:s.firstWeekContainsDate)&&void 0!==r?r:null===(f=s.locale)||void 0===f?void 0:null===(m=f.options)||void 0===m?void 0:m.firstWeekContainsDate)&&void 0!==n?n:1),v=c(t,e),w=new Date(0);return w.setUTCFullYear(v,0,g),w.setUTCHours(0,0,0,0),l(w,e)})(n,e).getTime())/6048e5)+1}(t,r);return"wo"===e?n.ordinalNumber(u,{unit:"week"}):h(u,e.length)},I:function(t,e,n){var r=function(t){a(1,arguments);var e=i(t);return Math.round((u(e).getTime()-(function(t){a(1,arguments);var e=d(t),n=new Date(0);return n.setUTCFullYear(e,0,4),n.setUTCHours(0,0,0,0),u(n)})(e).getTime())/6048e5)+1}(t);return"Io"===e?n.ordinalNumber(r,{unit:"week"}):h(r,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getUTCDate(),{unit:"date"}):f.d(t,e)},D:function(t,e,n){var r=function(t){a(1,arguments);var e=i(t),n=e.getTime();return e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0),Math.floor((n-e.getTime())/864e5)+1}(t);return"Do"===e?n.ordinalNumber(r,{unit:"dayOfYear"}):h(r,e.length)},E:function(t,e,n){var r=t.getUTCDay();switch(e){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(t,e,n,r){var a=t.getUTCDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"e":return String(i);case"ee":return h(i,2);case"eo":return n.ordinalNumber(i,{unit:"day"});case"eee":return n.day(a,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(a,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(a,{width:"short",context:"formatting"});default:return n.day(a,{width:"wide",context:"formatting"})}},c:function(t,e,n,r){var a=t.getUTCDay(),i=(a-r.weekStartsOn+8)%7||7;switch(e){case"c":return String(i);case"cc":return h(i,e.length);case"co":return n.ordinalNumber(i,{unit:"day"});case"ccc":return n.day(a,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(a,{width:"narrow",context:"standalone"});case"cccccc":return n.day(a,{width:"short",context:"standalone"});default:return n.day(a,{width:"wide",context:"standalone"})}},i:function(t,e,n){var r=t.getUTCDay(),a=0===r?7:r;switch(e){case"i":return String(a);case"ii":return h(a,e.length);case"io":return n.ordinalNumber(a,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(t,e,n){var r=t.getUTCHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(t,e,n){var r,a=t.getUTCHours();switch(r=12===a?m.noon:0===a?m.midnight:a/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(t,e,n){var r,a=t.getUTCHours();switch(r=a>=17?m.evening:a>=12?m.afternoon:a>=4?m.morning:m.night,e){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){var r=t.getUTCHours()%12;return 0===r&&(r=12),n.ordinalNumber(r,{unit:"hour"})}return f.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getUTCHours(),{unit:"hour"}):f.H(t,e)},K:function(t,e,n){var r=t.getUTCHours()%12;return"Ko"===e?n.ordinalNumber(r,{unit:"hour"}):h(r,e.length)},k:function(t,e,n){var r=t.getUTCHours();return(0===r&&(r=24),"ko"===e)?n.ordinalNumber(r,{unit:"hour"}):h(r,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getUTCMinutes(),{unit:"minute"}):f.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getUTCSeconds(),{unit:"second"}):f.s(t,e)},S:function(t,e){return f.S(t,e)},X:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();if(0===a)return"Z";switch(e){case"X":return v(a);case"XXXX":case"XX":return w(a);default:return w(a,":")}},x:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"x":return v(a);case"xxxx":case"xx":return w(a);default:return w(a,":")}},O:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+g(a,":");default:return"GMT"+w(a,":")}},z:function(t,e,n,r){var a=(r._originalDate||t).getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+g(a,":");default:return"GMT"+w(a,":")}},t:function(t,e,n,r){return h(Math.floor((r._originalDate||t).getTime()/1e3),e.length)},T:function(t,e,n,r){return h((r._originalDate||t).getTime(),e.length)}};var y=function(t,e){switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},p=function(t,e){switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}};let T={p:p,P:function(t,e){var n,r=t.match(/(P+)(p+)?/)||[],a=r[1],i=r[2];if(!i)return y(t,e);switch(a){case"P":n=e.dateTime({width:"short"});break;case"PP":n=e.dateTime({width:"medium"});break;case"PPP":n=e.dateTime({width:"long"});break;default:n=e.dateTime({width:"full"})}return n.replace("{{date}}",y(a,e)).replace("{{time}}",p(i,e))}};var C=["D","DD"],M=["YY","YYYY"];function D(t,e,n){if("YYYY"===t)throw RangeError("Use `yyyy` instead of `YYYY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("YY"===t)throw RangeError("Use `yy` instead of `YY` (in `".concat(e,"`) for formatting years to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("D"===t)throw RangeError("Use `d` instead of `D` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"));if("DD"===t)throw RangeError("Use `dd` instead of `DD` (in `".concat(e,"`) for formatting days of the month to the input `").concat(n,"`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md"))}var k={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function S(t){return function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}var x={date:S({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:S({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:S({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},U={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function P(t){return function(e,n){var r;if("formatting"===(null!=n&&n.context?String(n.context):"standalone")&&t.formattingValues){var a=t.defaultFormattingWidth||t.defaultWidth,i=null!=n&&n.width?String(n.width):a;r=t.formattingValues[i]||t.formattingValues[a]}else{var o=t.defaultWidth,u=null!=n&&n.width?String(n.width):t.defaultWidth;r=t.values[u]||t.values[o]}return r[t.argumentCallback?t.argumentCallback(e):e]}}function W(t){return function(e){var n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=r.width,i=a&&t.matchPatterns[a]||t.matchPatterns[t.defaultMatchWidth],o=e.match(i);if(!o)return null;var u=o[0],d=a&&t.parsePatterns[a]||t.parsePatterns[t.defaultParseWidth],s=Array.isArray(d)?function(t,e){for(var n=0;n<t.length;n++)if(e(t[n]))return n}(d,function(t){return t.test(u)}):function(t,e){for(var n in t)if(t.hasOwnProperty(n)&&e(t[n]))return n}(d,function(t){return t.test(u)});return n=t.valueCallback?t.valueCallback(s):s,{value:n=r.valueCallback?r.valueCallback(n):n,rest:e.slice(u.length)}}}let Y={code:"en-US",formatDistance:function(t,e,n){var r,a=k[t];return(r="string"==typeof a?a:1===e?a.one:a.other.replace("{{count}}",e.toString()),null!=n&&n.addSuffix)?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:x,formatRelative:function(t,e,n,r){return U[t]},localize:{ordinalNumber:function(t,e){var n=Number(t),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:P({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:P({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:P({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:P({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:P({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(t){return function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.match(t.matchPattern);if(!r)return null;var a=r[0],i=e.match(t.parsePattern);if(!i)return null;var o=t.valueCallback?t.valueCallback(i[0]):i[0];return{value:o=n.valueCallback?n.valueCallback(o):o,rest:e.slice(a.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:function(t){return parseInt(t,10)}}),era:W({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:W({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:W({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:W({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:W({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};var E=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,N=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,O=/^'([^]*?)'?$/,q=/''/g,H=/[a-zA-Z]/;function F(t,e,n){a(2,arguments);var u,d,l,c,h,f,m,g,v,w,y,p,k,S,x,U,P,W,F,j=String(e),z=null!==(d=null!==(l=null==n?void 0:n.locale)&&void 0!==l?l:s.locale)&&void 0!==d?d:Y,L=o(null!==(c=null!==(h=null!==(f=null!==(m=null==n?void 0:n.firstWeekContainsDate)&&void 0!==m?m:null==n?void 0:null===(g=n.locale)||void 0===g?void 0:null===(v=g.options)||void 0===v?void 0:v.firstWeekContainsDate)&&void 0!==f?f:s.firstWeekContainsDate)&&void 0!==h?h:null===(w=s.locale)||void 0===w?void 0:null===(y=w.options)||void 0===y?void 0:y.firstWeekContainsDate)&&void 0!==c?c:1);if(!(L>=1&&L<=7))throw RangeError("firstWeekContainsDate must be between 1 and 7 inclusively");var A=o(null!==(p=null!==(k=null!==(S=null!==(x=null==n?void 0:n.weekStartsOn)&&void 0!==x?x:null==n?void 0:null===(U=n.locale)||void 0===U?void 0:null===(P=U.options)||void 0===P?void 0:P.weekStartsOn)&&void 0!==S?S:s.weekStartsOn)&&void 0!==k?k:null===(W=s.locale)||void 0===W?void 0:null===(F=W.options)||void 0===F?void 0:F.weekStartsOn)&&void 0!==p?p:0);if(!(A>=0&&A<=6))throw RangeError("weekStartsOn must be between 0 and 6 inclusively");if(!z.localize)throw RangeError("locale must contain localize property");if(!z.formatLong)throw RangeError("locale must contain formatLong property");var Q=i(t);if(!function(t){return a(1,arguments),(!!function(t){return a(1,arguments),t instanceof Date||"object"===r(t)&&"[object Date]"===Object.prototype.toString.call(t)}(t)||"number"==typeof t)&&!isNaN(Number(i(t)))}(Q))throw RangeError("Invalid time value");var G=((u=new Date(Date.UTC(Q.getFullYear(),Q.getMonth(),Q.getDate(),Q.getHours(),Q.getMinutes(),Q.getSeconds(),Q.getMilliseconds()))).setUTCFullYear(Q.getFullYear()),Q.getTime()-u.getTime()),X=function(t,e){return a(2,arguments),function(t,e){return a(2,arguments),new Date(i(t).getTime()+o(e))}(t,-o(e))}(Q,G),B={firstWeekContainsDate:L,weekStartsOn:A,locale:z,_originalDate:Q};return j.match(N).map(function(t){var e=t[0];return"p"===e||"P"===e?(0,T[e])(t,z.formatLong):t}).join("").match(E).map(function(r){if("''"===r)return"'";var a,i=r[0];if("'"===i)return(a=r.match(O))?a[1].replace(q,"'"):r;var o=b[i];if(o)return null!=n&&n.useAdditionalWeekYearTokens||-1===M.indexOf(r)||D(r,e,String(t)),null!=n&&n.useAdditionalDayOfYearTokens||-1===C.indexOf(r)||D(r,e,String(t)),o(X,r,z.localize,B);if(i.match(H))throw RangeError("Format string contains an unescaped latin alphabet character `"+i+"`");return r}).join("")}}};