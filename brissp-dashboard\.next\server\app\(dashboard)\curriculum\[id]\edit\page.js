(()=>{var e={};e.id=8427,e.ids=[8427],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},42830:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>u,routeModule:()=>p,tree:()=>l});var s=t(70260),i=t(28203),a=t(25155),o=t.n(a),n=t(67292),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(r,d);let l=["",{children:["(dashboard)",{children:["curriculum",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4823)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\[id]\\edit\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/curriculum/[id]/edit/page",pathname:"/curriculum/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},86663:(e,r,t)=>{Promise.resolve().then(t.bind(t,4823))},16927:(e,r,t)=>{Promise.resolve().then(t.bind(t,97875))},97875:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(45512),i=t(58009),a=t(36929),o=t(79334);let n=()=>{let e=(0,o.useParams)(),r=e?.id,[t,n]=(0,i.useState)(null),[d,l]=(0,i.useState)(!0),[u,c]=(0,i.useState)(null);(0,i.useEffect)(()=>{r&&(async()=>{try{let e=await fetch(`/api/curriculum/${r}`);if(!e.ok)throw Error(`Error: ${e.status}`);let t=await e.json();n(t)}catch(e){console.error("Failed to fetch curriculum:",e),c("Failed to load curriculum data. Please try again.")}finally{l(!1)}})()},[r]);let p=async e=>{l(!0),c(null);try{let t=await fetch(`/api/curriculum/${r}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok)throw Error(`Error: ${t.status}`);window.location.href="/curriculum"}catch(e){console.error("Failed to update curriculum:",e),c("Failed to update curriculum. Please try again."),l(!1)}};return d?(0,s.jsx)("div",{className:"p-6",children:"Loading..."}):u?(0,s.jsx)("div",{className:"p-6 text-red-500",children:u}):(0,s.jsxs)("div",{className:"p-6 ",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Edit Curriculum"}),(0,s.jsxs)("div",{className:"bg-white rounded-lg border p-6",children:[t&&(0,s.jsx)(a.A,{initialData:t,onSubmit:p,isLoading:d}),!t&&!d&&(0,s.jsx)("div",{className:"text-red-500",children:"Curriculum not found"})]})]})}},4823:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\curriculum\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\curriculum\\[id]\\edit\\page.tsx","default")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,8403,7834,9267,4873,6562,3015,3373],()=>t(42830));module.exports=s})();