(()=>{var e={};e.id=179,e.ids=[179],e.modules={28303:e=>{function r(e){var r=Error("Cannot find module '"+e+"'");throw r.code="MODULE_NOT_FOUND",r}r.keys=()=>[],r.resolve=r,r.id=28303,e.exports=r},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},91645:e=>{"use strict";e.exports=require("net")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},27910:e=>{"use strict";e.exports=require("stream")},41204:e=>{"use strict";e.exports=require("string_decoder")},66136:e=>{"use strict";e.exports=require("timers")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},73168:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>_,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>p});var i=t(42706),o=t(28203),a=t(45994),n=t(39187),u=t(62545);async function p(e){try{let{courseId:r,studentType:t,studyMode:s,firstName:i,lastName:o,otherNames:a,gender:p,maritalStatus:c,dateOfBirth:l,nationality:d,idNumber:x,academicYear:m,intake:_,email:v,phoneNumber:q,country:y}=await e.json();if(!r||!t||!s||!i||!o||!p||!l||!d||!x||!m||!_||!v||!q||!y)return n.NextResponse.json({error:"Missing required fields"},{status:400});let[h]=await u.A.query(`INSERT INTO applications (
        course_id,
        student_type,
        study_mode,
        first_name,
        last_name,
        other_names,
        gender,
        marital_status,
        date_of_birth,
        nationality,
        id_number,
        academic_year,
        intake,
        email,
        phone_number,
        country
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,[r,t,s,i,o,a||null,p,c||null,l,d,x,m,_,v,q,y]);return n.NextResponse.json({success:!0,applicationId:h.insertId,message:"Application submitted successfully"},{status:201})}catch(e){return console.error("Error submitting application:",e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}async function c(e){try{let{searchParams:r}=new URL(e.url),t=r.get("status"),s=r.get("email"),i=`
      SELECT 
        a.*,
        c.title as course_title,
        c.course_code
      FROM applications a
      JOIN courses c ON a.course_id = c.course_id
      WHERE 1=1
    `,o=[];t&&(i+=" AND a.status = ?",o.push(t)),s&&(i+=" AND a.email = ?",o.push(s)),i+=" ORDER BY a.application_date DESC";let[a]=await u.A.query(i,o);return n.NextResponse.json(a)}catch(e){return console.error("Error fetching applications:",e),n.NextResponse.json({error:"Internal Server Error"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/applications/route",pathname:"/api/applications",filename:"route",bundlePath:"app/api/applications/route"},resolvedPagePath:"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\api\\applications\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:m}=l;function _(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},96487:()=>{},78335:()=>{},62545:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var s=t(60820),i=t(29021),o=t.n(i),a=t(33873),n=t.n(a);let u=s.createPool({host:process.env.MYSQL_HOST,port:parseInt(process.env.MYSQL_PORT||"20423"),database:process.env.MYSQL_DATABASE,user:process.env.MYSQL_USER,password:process.env.MYSQL_PASSWORD,ssl:{ca:o().readFileSync(n().resolve(process.cwd(),"aiven.pem")),rejectUnauthorized:!0},waitForConnections:!0,connectionLimit:10,queueLimit:0})}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,5452,820],()=>t(73168));module.exports=s})();