(()=>{var e={};e.id=3730,e.ids=[3730],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},25192:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var t=r(70260),n=r(28203),a=r(25155),i=r.n(a),o=r(67292),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d=["",{children:["(dashboard)",{children:["courses",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1520)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71975)),"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\new\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/courses/new/page",pathname:"/courses/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},98595:(e,s,r)=>{Promise.resolve().then(r.bind(r,1520))},12667:(e,s,r)=>{Promise.resolve().then(r.bind(r,46580))},46580:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(45512),n=r(58009),a=r(79334),i=r(77243);function o(){let e=(0,a.useRouter)(),[s,r]=(0,n.useState)(!1),o=async(s,t)=>{r(!0);try{let r=s.image_url;if(t){let e=new FormData;e.append("file",t);let s=await fetch("/api/upload",{method:"POST",body:e});if(s.ok){let{url:e}=await s.json();r=e}}(await fetch("/api/courses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...s,image_url:r})})).ok&&e.push("/courses")}catch(e){console.error("Error creating course:",e)}finally{r(!1)}};return(0,t.jsxs)("div",{className:"p-6 ",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Create New Course"}),(0,t.jsx)("div",{className:"bg-white rounded-lg border p-6",children:(0,t.jsx)(i.A,{onSubmit:o,isLoading:s})})]})}},77243:(e,s,r)=>{"use strict";r.d(s,{A:()=>d});var t=r(45512),n=r(58009),a=r(87021),i=r(25409),o=r(47920),l=r(27042);function d({initialData:e,onSubmit:s,isLoading:r}){let[d,c]=(0,n.useState)(e||{title:"",course_code:"",description:"",duration_months:0,price:0,department:"",category:"adults",program_type:"",num_lectures:0,skill_level:"beginner",languages:"",class_days:""}),[u,m]=(0,n.useState)(null),p=(0,o.hG)({extensions:[l.A],content:d.description,onUpdate:({editor:e})=>{c(s=>({...s,description:e.getHTML()}))}}),h=e=>{let{name:s,value:r}=e.target;c(e=>({...e,[s]:r}))};return(0,t.jsxs)("form",{onSubmit:e=>{e.preventDefault(),s(d,u||void 0)},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Course Title"}),(0,t.jsx)(i.p,{name:"title",value:d.title,onChange:h,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Course Code"}),(0,t.jsx)(i.p,{name:"course_code",value:d.course_code,onChange:h,required:!0})]}),(0,t.jsxs)("div",{className:"col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Description"}),(0,t.jsxs)("div",{className:"border rounded-md p-2",children:[(0,t.jsxs)("div",{className:"border-b p-2 mb-2 flex gap-2",children:[(0,t.jsx)("button",{type:"button",onClick:()=>p?.chain().focus().toggleBold().run(),className:`p-2 ${p?.isActive("bold")?"bg-gray-200":""}`,children:"Bold"}),(0,t.jsx)("button",{type:"button",onClick:()=>p?.chain().focus().toggleItalic().run(),className:`p-2 ${p?.isActive("italic")?"bg-gray-200":""}`,children:"Italic"}),(0,t.jsx)("button",{type:"button",onClick:()=>p?.chain().focus().toggleBulletList().run(),className:`p-2 ${p?.isActive("bulletList")?"bg-gray-200":""}`,children:"Bullet List"})]}),(0,t.jsx)(o.$Z,{editor:p,className:"min-h-[200px] prose max-w-none"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Duration (months)"}),(0,t.jsx)(i.p,{type:"number",name:"duration_months",value:d.duration_months,onChange:h,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Price"}),(0,t.jsx)(i.p,{type:"number",name:"price",value:d.price,onChange:h,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Department"}),(0,t.jsxs)("select",{title:"department",name:"department",value:d.department,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,t.jsx)("option",{value:"beginner",children:"Auto Engineering"}),(0,t.jsx)("option",{value:"intermediate",children:"Computer Science"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Category"}),(0,t.jsxs)("select",{title:"category",name:"category",value:d.category,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,t.jsx)("option",{value:"adults",children:"Adults"}),(0,t.jsx)("option",{value:"kids",children:"Kids"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Program Type"}),(0,t.jsxs)("select",{title:"program_type",name:"program_type",value:d.program_type,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,t.jsx)("option",{value:"",children:"Select program type"}),(0,t.jsx)("option",{value:"fulltime",children:"Fulltime"}),(0,t.jsx)("option",{value:"adults-online",children:"Adults online"}),(0,t.jsx)("option",{value:"kids-online",children:"Kids online"}),(0,t.jsx)("option",{value:"distance",children:"Distance"}),(0,t.jsx)("option",{value:"part-time",children:"Part time"}),(0,t.jsx)("option",{value:"weekend",children:"Weekend"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:'For refresher courses, use the dedicated "Add Refresher Course" button'})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Number of Lectures"}),(0,t.jsx)(i.p,{type:"number",name:"num_lectures",value:d.num_lectures,onChange:h,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Skill Level"}),(0,t.jsxs)("select",{title:"skill_level",name:"skill_level",value:d.skill_level,onChange:h,className:"w-full border rounded-md p-2",required:!0,children:[(0,t.jsx)("option",{value:"all",children:"All"}),(0,t.jsx)("option",{value:"beginner",children:"Beginner"}),(0,t.jsx)("option",{value:"intermediate",children:"Intermediate"}),(0,t.jsx)("option",{value:"advanced",children:"Advanced"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Languages"}),(0,t.jsx)(i.p,{name:"languages",value:d.languages,onChange:h,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Class Days"}),(0,t.jsx)(i.p,{name:"class_days",value:d.class_days,onChange:h,required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Upload Image"}),(0,t.jsx)(i.p,{type:"file",accept:"image/*",onChange:e=>{m(e.target.files?.[0]||null)}})]})]}),(0,t.jsx)("div",{className:"flex justify-end space-x-4",children:(0,t.jsx)(a.$,{type:"submit",disabled:r,children:r?"Saving...":"Save Course"})})]})}},1520:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\projects\\\\work\\\\Brissp\\\\briisp-academy\\\\brissp-dashboard\\\\app\\\\(dashboard)\\\\courses\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\projects\\work\\Brissp\\briisp-academy\\brissp-dashboard\\app\\(dashboard)\\courses\\new\\page.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[638,8403,7834,9267,4873,9847,8273],()=>r(25192));module.exports=t})();