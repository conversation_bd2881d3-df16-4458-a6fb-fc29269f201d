"use strict";exports.id=3015,exports.ids=[3015],exports.modules={18055:(e,t,n)=>{n.d(t,{sG:()=>d,hO:()=>f});var r=n(58009),o=n(55740),i=n(29952),l=n(45512),a=r.forwardRef((e,t)=>{let{children:n,...o}=e,i=r.Children.toArray(n),a=i.find(c);if(a){let e=a.props.children,n=i.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(s,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,l.jsx)(s,{...o,ref:t,children:n})});a.displayName="Slot";var s=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,i.t)(t,e):e})}return r.Children.count(n)>1?r.Children.only(null):null});s.displayName="SlotClone";var u=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function c(e){return r.isValidElement(e)&&e.type===u}var d=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=r.forwardRef((e,n)=>{let{asChild:r,...o}=e,i=r?a:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(i,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function f(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},96096:(e,t,n)=>{n.d(t,{UC:()=>ng,YJ:()=>nw,In:()=>nh,q7:()=>nb,VF:()=>nC,p4:()=>nE,JU:()=>nx,ZL:()=>nv,bL:()=>nf,wn:()=>nR,PP:()=>nS,wv:()=>nP,l9:()=>np,WT:()=>nm,LM:()=>ny});var r,o=n(58009),i=n(55740);function l(e,[t,n]){return Math.min(n,Math.max(t,e))}var a=n(31412),s=n(6004),u=n(29952),c=n(45512),d=o.forwardRef((e,t)=>{let{children:n,...r}=e,i=o.Children.toArray(n),l=i.find(m);if(l){let e=l.props.children,n=i.map(t=>t!==l?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,c.jsx)(f,{...r,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,c.jsx)(f,{...r,ref:t,children:n})});d.displayName="Slot";var f=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return o.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props),ref:t?(0,u.t)(t,e):e})}return o.Children.count(n)>1?o.Children.only(null):null});f.displayName="SlotClone";var p=({children:e})=>(0,c.jsx)(c.Fragment,{children:e});function m(e){return o.isValidElement(e)&&e.type===p}var h=n(59018),v=n(18055),g=n(92828),y=n(99850),w="dismissableLayer.update",x=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),b=o.forwardRef((e,t)=>{let{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:i,onPointerDownOutside:l,onFocusOutside:s,onInteractOutside:d,onDismiss:f,...p}=e,m=o.useContext(x),[h,b]=o.useState(null),S=h?.ownerDocument??globalThis?.document,[,R]=o.useState({}),P=(0,u.s)(t,e=>b(e)),T=Array.from(m.layers),[A]=[...m.layersWithOutsidePointerEventsDisabled].slice(-1),L=T.indexOf(A),j=h?T.indexOf(h):-1,N=m.layersWithOutsidePointerEventsDisabled.size>0,D=j>=L,O=function(e,t=globalThis?.document){let n=(0,g.c)(e),r=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!r.current){let r=function(){C("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=r,t.addEventListener("click",i.current,{once:!0})):r()}else t.removeEventListener("click",i.current);r.current=!1},o=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,n=[...m.branches].some(e=>e.contains(t));!D||n||(l?.(e),d?.(e),e.defaultPrevented||f?.())},S),k=function(e,t=globalThis?.document){let n=(0,g.c)(e),r=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!r.current&&C("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;[...m.branches].some(e=>e.contains(t))||(s?.(e),d?.(e),e.defaultPrevented||f?.())},S);return(0,y.U)(e=>{j!==m.layers.size-1||(i?.(e),!e.defaultPrevented&&f&&(e.preventDefault(),f()))},S),o.useEffect(()=>{if(h)return n&&(0===m.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),m.layersWithOutsidePointerEventsDisabled.add(h)),m.layers.add(h),E(),()=>{n&&1===m.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[h,S,n,m]),o.useEffect(()=>()=>{h&&(m.layers.delete(h),m.layersWithOutsidePointerEventsDisabled.delete(h),E())},[h,m]),o.useEffect(()=>{let e=()=>R({});return document.addEventListener(w,e),()=>document.removeEventListener(w,e)},[]),(0,c.jsx)(v.sG.div,{...p,ref:P,style:{pointerEvents:N?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,k.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});function E(){let e=new CustomEvent(w);document.dispatchEvent(e)}function C(e,t,n,{discrete:r}){let o=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?(0,v.hO)(o,i):o.dispatchEvent(i)}b.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(x),r=o.useRef(null),i=(0,u.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(v.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var S=n(19632),R="focusScope.autoFocusOnMount",P="focusScope.autoFocusOnUnmount",T={bubbles:!1,cancelable:!0},A=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:i,onUnmountAutoFocus:l,...a}=e,[s,d]=o.useState(null),f=(0,g.c)(i),p=(0,g.c)(l),m=o.useRef(null),h=(0,u.s)(t,e=>d(e)),y=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(y.paused||!s)return;let t=e.target;s.contains(t)?m.current=t:N(m.current,{select:!0})},t=function(e){if(y.paused||!s)return;let t=e.relatedTarget;null===t||s.contains(t)||N(m.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&N(s)});return s&&n.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,s,y.paused]),o.useEffect(()=>{if(s){D.add(y);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(R,T);s.addEventListener(R,f),s.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(N(r,{select:t}),document.activeElement!==n)return}(L(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&N(s))}return()=>{s.removeEventListener(R,f),setTimeout(()=>{let t=new CustomEvent(P,T);s.addEventListener(P,p),s.dispatchEvent(t),t.defaultPrevented||N(e??document.body,{select:!0}),s.removeEventListener(P,p),D.remove(y)},0)}}},[s,f,p,y]);let w=o.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=L(e);return[j(t,e),j(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&N(i,{select:!0})):(e.preventDefault(),n&&N(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,c.jsx)(v.sG.div,{tabIndex:-1,...a,ref:h,onKeyDown:w})});function L(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function j(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function N(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}A.displayName="FocusScope";var D=function(){let e=[];return{add(t){let n=e[0];t!==n&&n?.pause(),(e=O(e,t)).unshift(t)},remove(t){e=O(e,t),e[0]?.resume()}}}();function O(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var k=n(30096);let M=["top","right","bottom","left"],I=Math.min,F=Math.max,H=Math.round,W=Math.floor,B=e=>({x:e,y:e}),V={left:"right",right:"left",bottom:"top",top:"bottom"},_={start:"end",end:"start"};function z(e,t){return"function"==typeof e?e(t):e}function G(e){return e.split("-")[0]}function K(e){return e.split("-")[1]}function $(e){return"x"===e?"y":"x"}function U(e){return"y"===e?"height":"width"}function q(e){return["top","bottom"].includes(G(e))?"y":"x"}function X(e){return e.replace(/start|end/g,e=>_[e])}function Y(e){return e.replace(/left|right|bottom|top/g,e=>V[e])}function Z(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function J(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Q(e,t,n){let r,{reference:o,floating:i}=e,l=q(t),a=$(q(t)),s=U(a),u=G(t),c="y"===l,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,p=o[s]/2-i[s]/2;switch(u){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(K(t)){case"start":r[a]-=p*(n&&c?-1:1);break;case"end":r[a]+=p*(n&&c?-1:1)}return r}let ee=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:l}=n,a=i.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:d}=Q(u,r,s),f=r,p={},m=0;for(let n=0;n<a.length;n++){let{name:i,fn:h}=a[n],{x:v,y:g,data:y,reset:w}=await h({x:c,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=v?v:c,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:d}=Q(u,f,s)),n=-1)}return{x:c,y:d,placement:f,strategy:o,middlewareData:p}};async function et(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=z(t,e),m=Z(p),h=a[f?"floating"===d?"reference":"floating":d],v=J(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:s})),g="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(a.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},x=J(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:y,strategy:s}):g);return{top:(v.top-x.top+m.top)/w.y,bottom:(x.bottom-v.bottom+m.bottom)/w.y,left:(v.left-x.left+m.left)/w.x,right:(x.right-v.right+m.right)/w.x}}function en(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function er(e){return M.some(t=>e[t]>=0)}async function eo(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),l=G(n),a=K(n),s="y"===q(n),u=["left","top"].includes(l)?-1:1,c=i&&s?-1:1,d=z(t,e),{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof m&&(p="end"===a?-1*m:m),s?{x:p*c,y:f*u}:{x:f*u,y:p*c}}function ei(){return"undefined"!=typeof window}function el(e){return eu(e)?(e.nodeName||"").toLowerCase():"#document"}function ea(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function es(e){var t;return null==(t=(eu(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function eu(e){return!!ei()&&(e instanceof Node||e instanceof ea(e).Node)}function ec(e){return!!ei()&&(e instanceof Element||e instanceof ea(e).Element)}function ed(e){return!!ei()&&(e instanceof HTMLElement||e instanceof ea(e).HTMLElement)}function ef(e){return!!ei()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof ea(e).ShadowRoot)}function ep(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ey(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function em(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function eh(e){let t=ev(),n=ec(e)?ey(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function ev(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function eg(e){return["html","body","#document"].includes(el(e))}function ey(e){return ea(e).getComputedStyle(e)}function ew(e){return ec(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ex(e){if("html"===el(e))return e;let t=e.assignedSlot||e.parentNode||ef(e)&&e.host||es(e);return ef(t)?t.host:t}function eb(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ex(t);return eg(n)?t.ownerDocument?t.ownerDocument.body:t.body:ed(n)&&ep(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),l=ea(o);if(i){let e=eE(l);return t.concat(l,l.visualViewport||[],ep(o)?o:[],e&&n?eb(e):[])}return t.concat(o,eb(o,[],n))}function eE(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eC(e){let t=ey(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=ed(e),i=o?e.offsetWidth:n,l=o?e.offsetHeight:r,a=H(n)!==i||H(r)!==l;return a&&(n=i,r=l),{width:n,height:r,$:a}}function eS(e){return ec(e)?e:e.contextElement}function eR(e){let t=eS(e);if(!ed(t))return B(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eC(t),l=(i?H(n.width):n.width)/r,a=(i?H(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),a&&Number.isFinite(a)||(a=1),{x:l,y:a}}let eP=B(0);function eT(e){let t=ea(e);return ev()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eP}function eA(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),l=eS(e),a=B(1);t&&(r?ec(r)&&(a=eR(r)):a=eR(e));let s=(void 0===(o=n)&&(o=!1),r&&(!o||r===ea(l))&&o)?eT(l):B(0),u=(i.left+s.x)/a.x,c=(i.top+s.y)/a.y,d=i.width/a.x,f=i.height/a.y;if(l){let e=ea(l),t=r&&ec(r)?ea(r):r,n=e,o=eE(n);for(;o&&r&&t!==n;){let e=eR(o),t=o.getBoundingClientRect(),r=ey(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=i,c+=l,o=eE(n=ea(o))}}return J({width:d,height:f,x:u,y:c})}function eL(e,t){let n=ew(e).scrollLeft;return t?t.left+n:eA(es(e)).left+n}function ej(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:eL(e,r)),y:r.top+t.scrollTop}}function eN(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=ea(e),r=es(e),o=n.visualViewport,i=r.clientWidth,l=r.clientHeight,a=0,s=0;if(o){i=o.width,l=o.height;let e=ev();(!e||e&&"fixed"===t)&&(a=o.offsetLeft,s=o.offsetTop)}return{width:i,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=es(e),n=ew(e),r=e.ownerDocument.body,o=F(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=F(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+eL(e),a=-n.scrollTop;return"rtl"===ey(r).direction&&(l+=F(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:a}}(es(e));else if(ec(t))r=function(e,t){let n=eA(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=ed(e)?eR(e):B(1),l=e.clientWidth*i.x;return{width:l,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=eT(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return J(r)}function eD(e){return"static"===ey(e).position}function eO(e,t){if(!ed(e)||"fixed"===ey(e).position)return null;if(t)return t(e);let n=e.offsetParent;return es(e)===n&&(n=n.ownerDocument.body),n}function ek(e,t){let n=ea(e);if(em(e))return n;if(!ed(e)){let t=ex(e);for(;t&&!eg(t);){if(ec(t)&&!eD(t))return t;t=ex(t)}return n}let r=eO(e,t);for(;r&&["table","td","th"].includes(el(r))&&eD(r);)r=eO(r,t);return r&&eg(r)&&eD(r)&&!eh(r)?n:r||function(e){let t=ex(e);for(;ed(t)&&!eg(t);){if(eh(t))return t;if(em(t))break;t=ex(t)}return null}(e)||n}let eM=async function(e){let t=this.getOffsetParent||ek,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=ed(t),o=es(t),i="fixed"===n,l=eA(e,!0,i,t),a={scrollLeft:0,scrollTop:0},s=B(0);if(r||!r&&!i){if(("body"!==el(t)||ep(o))&&(a=ew(t)),r){let e=eA(t,!0,i,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&(s.x=eL(o))}let u=!o||r||i?B(0):ej(o,a);return{x:l.left+a.scrollLeft-s.x-u.x,y:l.top+a.scrollTop-s.y-u.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eI={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,l=es(r),a=!!t&&em(t.floating);if(r===l||a&&i)return n;let s={scrollLeft:0,scrollTop:0},u=B(1),c=B(0),d=ed(r);if((d||!d&&!i)&&(("body"!==el(r)||ep(l))&&(s=ew(r)),ed(r))){let e=eA(r);u=eR(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!l||d||i?B(0):ej(l,s,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-s.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-s.scrollTop*u.y+c.y+f.y}},getDocumentElement:es,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?em(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eb(e,[],!1).filter(e=>ec(e)&&"body"!==el(e)),o=null,i="fixed"===ey(e).position,l=i?ex(e):e;for(;ec(l)&&!eg(l);){let t=ey(l),n=eh(l);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||ep(l)&&!n&&function e(t,n){let r=ex(t);return!(r===n||!ec(r)||eg(r))&&("fixed"===ey(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):o=t,l=ex(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((e,n)=>{let r=eN(t,n,o);return e.top=F(r.top,e.top),e.right=I(r.right,e.right),e.bottom=I(r.bottom,e.bottom),e.left=F(r.left,e.left),e},eN(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:ek,getElementRects:eM,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eC(e);return{width:t,height:n}},getScale:eR,isElement:ec,isRTL:function(e){return"rtl"===ey(e).direction}};function eF(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eH=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:l,elements:a,middlewareData:s}=t,{element:u,padding:c=0}=z(e,t)||{};if(null==u)return{};let d=Z(c),f={x:n,y:r},p=$(q(o)),m=U(p),h=await l.getDimensions(u),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[m]+i.reference[p]-f[p]-i.floating[m],w=f[p]-i.reference[p],x=await (null==l.getOffsetParent?void 0:l.getOffsetParent(u)),b=x?x[g]:0;b&&await (null==l.isElement?void 0:l.isElement(x))||(b=a.floating[g]||i.floating[m]);let E=b/2-h[m]/2-1,C=I(d[v?"top":"left"],E),S=I(d[v?"bottom":"right"],E),R=b-h[m]-S,P=b/2-h[m]/2+(y/2-w/2),T=F(C,I(P,R)),A=!s.arrow&&null!=K(o)&&P!==T&&i.reference[m]/2-(P<C?C:S)-h[m]/2<0,L=A?P<C?P-C:P-R:0;return{[p]:f[p]+L,data:{[p]:T,centerOffset:P-T-L,...A&&{alignmentOffset:L}},reset:A}}}),eW=(e,t,n)=>{let r=new Map,o={platform:eI,...n},i={...o.platform,_c:r};return ee(e,t,{...o,platform:i})};var eB="undefined"!=typeof document?o.useLayoutEffect:o.useEffect;function eV(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eV(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eV(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function e_(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ez(e,t){let n=e_(e);return Math.round(t*n)/n}function eG(e){let t=o.useRef(e);return eB(()=>{t.current=e}),t}let eK=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eH({element:n.current,padding:r}).fn(t):{}:n?eH({element:n,padding:r}).fn(t):{}}}),e$=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:l,middlewareData:a}=t,s=await eo(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:i+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}),eU=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:l=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=z(e,t),u={x:n,y:r},c=await et(t,s),d=q(G(o)),f=$(d),p=u[f],m=u[d];if(i){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=p+c[e],r=p-c[t];p=F(n,I(p,r))}if(l){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=m+c[e],r=m-c[t];m=F(n,I(m,r))}let h=a.fn({...t,[f]:p,[d]:m});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:i,[d]:l}}}}}}(e),options:[e,t]}),eq=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=z(e,t),c={x:n,y:r},d=q(o),f=$(d),p=c[f],m=c[d],h=z(a,t),v="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(s){let e="y"===f?"height":"width",t=i.reference[f]-i.floating[e]+v.mainAxis,n=i.reference[f]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(u){var g,y;let e="y"===f?"width":"height",t=["top","left"].includes(G(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(g=l.offset)?void 0:g[d])||0)+(t?0:v.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(y=l.offset)?void 0:y[d])||0)-(t?v.crossAxis:0);m<n?m=n:m>r&&(m=r)}return{[f]:p,[d]:m}}}}(e),options:[e,t]}),eX=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:c,platform:d,elements:f}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:h,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=z(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let x=G(a),b=q(c),E=G(c)===c,C=await (null==d.isRTL?void 0:d.isRTL(f.floating)),S=h||(E||!y?[Y(c)]:function(e){let t=Y(e);return[X(e),t,X(t)]}(c)),R="none"!==g;!h&&R&&S.push(...function(e,t,n,r){let o=K(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(G(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(X)))),i}(c,y,g,C));let P=[c,...S],T=await et(t,w),A=[],L=(null==(r=s.flip)?void 0:r.overflows)||[];if(p&&A.push(T[x]),m){let e=function(e,t,n){void 0===n&&(n=!1);let r=K(e),o=$(q(e)),i=U(o),l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(l=Y(l)),[l,Y(l)]}(a,u,C);A.push(T[e[0]],T[e[1]])}if(L=[...L,{placement:a,overflows:A}],!A.every(e=>e<=0)){let e=((null==(o=s.flip)?void 0:o.index)||0)+1,t=P[e];if(t)return{data:{index:e,overflows:L},reset:{placement:t}};let n=null==(i=L.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(l=L.filter(e=>{if(R){let t=q(e.placement);return t===b||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),eY=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i;let{placement:l,rects:a,platform:s,elements:u}=t,{apply:c=()=>{},...d}=z(e,t),f=await et(t,d),p=G(l),m=K(l),h="y"===q(l),{width:v,height:g}=a.floating;"top"===p||"bottom"===p?(o=p,i=m===(await (null==s.isRTL?void 0:s.isRTL(u.floating))?"start":"end")?"left":"right"):(i=p,o="end"===m?"top":"bottom");let y=g-f.top-f.bottom,w=v-f.left-f.right,x=I(g-f[o],y),b=I(v-f[i],w),E=!t.middlewareData.shift,C=x,S=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(S=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=y),E&&!m){let e=F(f.left,0),t=F(f.right,0),n=F(f.top,0),r=F(f.bottom,0);h?S=v-2*(0!==e||0!==t?e+t:F(f.left,f.right)):C=g-2*(0!==n||0!==r?n+r:F(f.top,f.bottom))}await c({...t,availableWidth:S,availableHeight:C});let R=await s.getDimensions(u.floating);return v!==R.width||g!==R.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eZ=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=z(e,t);switch(r){case"referenceHidden":{let e=en(await et(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:er(e)}}}case"escaped":{let e=en(await et(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:er(e)}}}default:return{}}}}}(e),options:[e,t]}),eJ=(e,t)=>({...eK(e),options:[e,t]});var eQ=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,c.jsx)(v.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,c.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eQ.displayName="Arrow";var e0=n(49397),e1=n(38762),e2="Popper",[e5,e9]=(0,s.A)(e2),[e6,e8]=e5(e2),e3=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,c.jsx)(e6,{scope:t,anchor:r,onAnchorChange:i,children:n})};e3.displayName=e2;var e4="PopperAnchor",e7=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=e8(e4,n),a=o.useRef(null),s=(0,u.s)(t,a);return o.useEffect(()=>{l.onAnchorChange(r?.current||a.current)}),r?null:(0,c.jsx)(v.sG.div,{...i,ref:s})});e7.displayName=e4;var te="PopperContent",[tt,tn]=e5(te),tr=o.forwardRef((e,t)=>{let{__scopePopper:n,side:r="bottom",sideOffset:l=0,align:a="center",alignOffset:s=0,arrowPadding:d=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:h="partial",hideWhenDetached:y=!1,updatePositionStrategy:w="optimized",onPlaced:x,...b}=e,E=e8(te,n),[C,S]=o.useState(null),R=(0,u.s)(t,e=>S(e)),[P,T]=o.useState(null),A=(0,e1.X)(P),L=A?.width??0,j=A?.height??0,N="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},D=Array.isArray(p)?p:[p],O=D.length>0,k={padding:N,boundary:D.filter(ta),altBoundary:O},{refs:M,floatingStyles:H,placement:B,isPositioned:V,middlewareData:_}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:l,elements:{reference:a,floating:s}={},transform:u=!0,whileElementsMounted:c,open:d}=e,[f,p]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=o.useState(r);eV(m,r)||h(r);let[v,g]=o.useState(null),[y,w]=o.useState(null),x=o.useCallback(e=>{e!==S.current&&(S.current=e,g(e))},[]),b=o.useCallback(e=>{e!==R.current&&(R.current=e,w(e))},[]),E=a||v,C=s||y,S=o.useRef(null),R=o.useRef(null),P=o.useRef(f),T=null!=c,A=eG(c),L=eG(l),j=eG(d),N=o.useCallback(()=>{if(!S.current||!R.current)return;let e={placement:t,strategy:n,middleware:m};L.current&&(e.platform=L.current),eW(S.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==j.current};D.current&&!eV(P.current,t)&&(P.current=t,i.flushSync(()=>{p(t)}))})},[m,t,n,L,j]);eB(()=>{!1===d&&P.current.isPositioned&&(P.current.isPositioned=!1,p(e=>({...e,isPositioned:!1})))},[d]);let D=o.useRef(!1);eB(()=>(D.current=!0,()=>{D.current=!1}),[]),eB(()=>{if(E&&(S.current=E),C&&(R.current=C),E&&C){if(A.current)return A.current(E,C,N);N()}},[E,C,N,A,T]);let O=o.useMemo(()=>({reference:S,floating:R,setReference:x,setFloating:b}),[x,b]),k=o.useMemo(()=>({reference:E,floating:C}),[E,C]),M=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!k.floating)return e;let t=ez(k.floating,f.x),r=ez(k.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...e_(k.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,k.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:N,refs:O,elements:k,floatingStyles:M}),[f,N,O,k,M])}({strategy:"fixed",placement:r+("center"!==a?"-"+a:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,c=eS(e),d=i||l?[...c?eb(c):[],...eb(t)]:[];d.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),l&&e.addEventListener("resize",n)});let f=c&&s?function(e,t){let n,r=null,o=es(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function l(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),i();let u=e.getBoundingClientRect(),{left:c,top:d,width:f,height:p}=u;if(a||t(),!f||!p)return;let m=W(d),h=W(o.clientWidth-(c+f)),v={rootMargin:-m+"px "+-h+"px "+-W(o.clientHeight-(d+p))+"px "+-W(c)+"px",threshold:F(0,I(1,s))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==s){if(!g)return l();r?l(!1,r):n=setTimeout(()=>{l(!1,1e-7)},1e3)}1!==r||eF(u,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(c,n):null,p=-1,m=null;a&&(m=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&m&&(m.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=m)||e.observe(t)})),n()}),c&&!u&&m.observe(c),m.observe(t));let h=u?eA(e):null;return u&&function t(){let r=eA(e);h&&!eF(h,r)&&n(),h=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach(e=>{i&&e.removeEventListener("scroll",n),l&&e.removeEventListener("resize",n)}),null==f||f(),null==(e=m)||e.disconnect(),m=null,u&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===w}),elements:{reference:E.anchor},middleware:[e$({mainAxis:l+j,alignmentAxis:s}),f&&eU({mainAxis:!0,crossAxis:!1,limiter:"partial"===h?eq():void 0,...k}),f&&eX({...k}),eY({...k,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,l=e.floating.style;l.setProperty("--radix-popper-available-width",`${n}px`),l.setProperty("--radix-popper-available-height",`${r}px`),l.setProperty("--radix-popper-anchor-width",`${o}px`),l.setProperty("--radix-popper-anchor-height",`${i}px`)}}),P&&eJ({element:P,padding:d}),ts({arrowWidth:L,arrowHeight:j}),y&&eZ({strategy:"referenceHidden",...k})]}),[z,G]=tu(B),K=(0,g.c)(x);(0,e0.N)(()=>{V&&K?.()},[V,K]);let $=_.arrow?.x,U=_.arrow?.y,q=_.arrow?.centerOffset!==0,[X,Y]=o.useState();return(0,e0.N)(()=>{C&&Y(window.getComputedStyle(C).zIndex)},[C]),(0,c.jsx)("div",{ref:M.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:V?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[_.transformOrigin?.x,_.transformOrigin?.y].join(" "),..._.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,c.jsx)(tt,{scope:n,placedSide:z,onArrowChange:T,arrowX:$,arrowY:U,shouldHideArrow:q,children:(0,c.jsx)(v.sG.div,{"data-side":z,"data-align":G,...b,ref:R,style:{...b.style,animation:V?void 0:"none"}})})})});tr.displayName=te;var to="PopperArrow",ti={top:"bottom",right:"left",bottom:"top",left:"right"},tl=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=tn(to,n),i=ti[o.placedSide];return(0,c.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,c.jsx)(eQ,{...r,ref:t,style:{...r.style,display:"block"}})})});function ta(e){return null!==e}tl.displayName=to;var ts=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[s,u]=tu(n),c={start:"0%",center:"50%",end:"100%"}[u],d=(o.arrow?.x??0)+l/2,f=(o.arrow?.y??0)+a/2,p="",m="";return"bottom"===s?(p=i?c:`${d}px`,m=`${-a}px`):"top"===s?(p=i?c:`${d}px`,m=`${r.floating.height+a}px`):"right"===s?(p=`${-a}px`,m=i?c:`${f}px`):"left"===s&&(p=`${r.floating.width+a}px`,m=i?c:`${f}px`),{data:{x:p,y:m}}}});function tu(e){let[t,n="center"]=e.split("-");return[t,n]}var tc=o.forwardRef((e,t)=>{let{container:n,...r}=e,[l,a]=o.useState(!1);(0,e0.N)(()=>a(!0),[]);let s=n||l&&globalThis?.document?.body;return s?i.createPortal((0,c.jsx)(v.sG.div,{...r,ref:t}),s):null});tc.displayName="Portal";var td=o.forwardRef((e,t)=>{let{children:n,...r}=e,i=o.Children.toArray(n),l=i.find(tm);if(l){let e=l.props.children,n=i.map(t=>t!==l?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,c.jsx)(tf,{...r,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,c.jsx)(tf,{...r,ref:t,children:n})});td.displayName="Slot";var tf=o.forwardRef((e,t)=>{let{children:n,...r}=e;if(o.isValidElement(n)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(n=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(n);return o.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(r,n.props),ref:t?(0,u.t)(t,e):e})}return o.Children.count(n)>1?o.Children.only(null):null});tf.displayName="SlotClone";var tp=({children:e})=>(0,c.jsx)(c.Fragment,{children:e});function tm(e){return o.isValidElement(e)&&e.type===tp}var th=n(13024),tv=n(66582),tg=o.forwardRef((e,t)=>(0,c.jsx)(v.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));tg.displayName="VisuallyHidden";var ty=n(72421),tw=n(67783),tx=[" ","Enter","ArrowUp","ArrowDown"],tb=[" ","Enter"],tE="Select",[tC,tS,tR]=function(e){let t=e+"CollectionProvider",[n,r]=(0,s.A)(t),[i,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=o.useRef(null),l=o.useRef(new Map).current;return(0,c.jsx)(i,{scope:t,itemMap:l,collectionRef:r,children:n})};a.displayName=t;let f=e+"CollectionSlot",p=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=l(f,n),i=(0,u.s)(t,o.collectionRef);return(0,c.jsx)(d,{ref:i,children:r})});p.displayName=f;let m=e+"CollectionItemSlot",h="data-radix-collection-item",v=o.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,a=o.useRef(null),s=(0,u.s)(t,a),f=l(m,n);return o.useEffect(()=>(f.itemMap.set(a,{ref:a,...i}),()=>void f.itemMap.delete(a))),(0,c.jsx)(d,{[h]:"",ref:s,children:r})});return v.displayName=m,[{Provider:a,Slot:p,ItemSlot:v},function(t){let n=l(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}(tE),[tP,tT]=(0,s.A)(tE,[tR,e9]),tA=e9(),[tL,tj]=tP(tE),[tN,tD]=tP(tE),tO=e=>{let{__scopeSelect:t,children:n,open:r,defaultOpen:i,onOpenChange:l,value:a,defaultValue:s,onValueChange:u,dir:d,name:f,autoComplete:p,disabled:m,required:v,form:g}=e,y=tA(t),[w,x]=o.useState(null),[b,E]=o.useState(null),[C,S]=o.useState(!1),R=(0,h.jH)(d),[P=!1,T]=(0,th.i)({prop:r,defaultProp:i,onChange:l}),[A,L]=(0,th.i)({prop:a,defaultProp:s,onChange:u}),j=o.useRef(null),N=!w||g||!!w.closest("form"),[D,O]=o.useState(new Set),M=Array.from(D).map(e=>e.props.value).join(";");return(0,c.jsx)(e3,{...y,children:(0,c.jsxs)(tL,{required:v,scope:t,trigger:w,onTriggerChange:x,valueNode:b,onValueNodeChange:E,valueNodeHasChildren:C,onValueNodeHasChildrenChange:S,contentId:(0,k.B)(),value:A,onValueChange:L,open:P,onOpenChange:T,dir:R,triggerPointerDownPosRef:j,disabled:m,children:[(0,c.jsx)(tC.Provider,{scope:t,children:(0,c.jsx)(tN,{scope:e.__scopeSelect,onNativeOptionAdd:o.useCallback(e=>{O(t=>new Set(t).add(e))},[]),onNativeOptionRemove:o.useCallback(e=>{O(t=>{let n=new Set(t);return n.delete(e),n})},[]),children:n})}),N?(0,c.jsxs)(nu,{"aria-hidden":!0,required:v,tabIndex:-1,name:f,autoComplete:p,value:A,onChange:e=>L(e.target.value),disabled:m,form:g,children:[void 0===A?(0,c.jsx)("option",{value:""}):null,Array.from(D)]},M):null]})})};tO.displayName=tE;var tk="SelectTrigger",tM=o.forwardRef((e,t)=>{let{__scopeSelect:n,disabled:r=!1,...i}=e,l=tA(n),s=tj(tk,n),d=s.disabled||r,f=(0,u.s)(t,s.onTriggerChange),p=tS(n),m=o.useRef("touch"),[h,g,y]=nc(e=>{let t=p().filter(e=>!e.disabled),n=t.find(e=>e.value===s.value),r=nd(t,e,n);void 0!==r&&s.onValueChange(r.value)}),w=e=>{d||(s.onOpenChange(!0),y()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,c.jsx)(e7,{asChild:!0,...l,children:(0,c.jsx)(v.sG.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ns(s.value)?"":void 0,...i,ref:f,onClick:(0,a.m)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==m.current&&w(e)}),onPointerDown:(0,a.m)(i.onPointerDown,e=>{m.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())}),onKeyDown:(0,a.m)(i.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&tx.includes(e.key)&&(w(),e.preventDefault())})})})});tM.displayName=tk;var tI="SelectValue",tF=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:o,children:i,placeholder:l="",...a}=e,s=tj(tI,n),{onValueNodeHasChildrenChange:d}=s,f=void 0!==i,p=(0,u.s)(t,s.onValueNodeChange);return(0,e0.N)(()=>{d(f)},[d,f]),(0,c.jsx)(v.sG.span,{...a,ref:p,style:{pointerEvents:"none"},children:ns(s.value)?(0,c.jsx)(c.Fragment,{children:l}):i})});tF.displayName=tI;var tH=o.forwardRef((e,t)=>{let{__scopeSelect:n,children:r,...o}=e;return(0,c.jsx)(v.sG.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});tH.displayName="SelectIcon";var tW=e=>(0,c.jsx)(tc,{asChild:!0,...e});tW.displayName="SelectPortal";var tB="SelectContent",tV=o.forwardRef((e,t)=>{let n=tj(tB,e.__scopeSelect),[r,l]=o.useState();return((0,e0.N)(()=>{l(new DocumentFragment)},[]),n.open)?(0,c.jsx)(tG,{...e,ref:t}):r?i.createPortal((0,c.jsx)(t_,{scope:e.__scopeSelect,children:(0,c.jsx)(tC.Slot,{scope:e.__scopeSelect,children:(0,c.jsx)("div",{children:e.children})})}),r):null});tV.displayName=tB;var[t_,tz]=tP(tB),tG=o.forwardRef((e,t)=>{let{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:s,side:d,sideOffset:f,align:p,alignOffset:m,arrowPadding:h,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:w,avoidCollisions:x,...E}=e,C=tj(tB,n),[R,P]=o.useState(null),[T,L]=o.useState(null),j=(0,u.s)(t,e=>P(e)),[N,D]=o.useState(null),[O,k]=o.useState(null),M=tS(n),[I,F]=o.useState(!1),H=o.useRef(!1);o.useEffect(()=>{if(R)return(0,ty.Eq)(R)},[R]),(0,S.Oh)();let W=o.useCallback(e=>{let[t,...n]=M().map(e=>e.ref.current),[r]=n.slice(-1),o=document.activeElement;for(let n of e)if(n===o||(n?.scrollIntoView({block:"nearest"}),n===t&&T&&(T.scrollTop=0),n===r&&T&&(T.scrollTop=T.scrollHeight),n?.focus(),document.activeElement!==o))return},[M,T]),B=o.useCallback(()=>W([N,R]),[W,N,R]);o.useEffect(()=>{I&&B()},[I,B]);let{onOpenChange:V,triggerPointerDownPosRef:_}=C;o.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(_.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(_.current?.y??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||V(!1),document.removeEventListener("pointermove",t),_.current=null};return null!==_.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}},[R,V,_]),o.useEffect(()=>{let e=()=>V(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[V]);let[z,G]=nc(e=>{let t=M().filter(e=>!e.disabled),n=t.find(e=>e.ref.current===document.activeElement),r=nd(t,e,n);r&&setTimeout(()=>r.ref.current.focus())}),K=o.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==C.value&&C.value===t||r)&&(D(e),r&&(H.current=!0))},[C.value]),$=o.useCallback(()=>R?.focus(),[R]),U=o.useCallback((e,t,n)=>{let r=!H.current&&!n;(void 0!==C.value&&C.value===t||r)&&k(e)},[C.value]),q="popper"===r?t$:tK,X=q===t$?{side:d,sideOffset:f,align:p,alignOffset:m,arrowPadding:h,collisionBoundary:v,collisionPadding:g,sticky:y,hideWhenDetached:w,avoidCollisions:x}:{};return(0,c.jsx)(t_,{scope:n,content:R,viewport:T,onViewportChange:L,itemRefCallback:K,selectedItem:N,onItemLeave:$,itemTextRefCallback:U,focusSelectedItem:B,selectedItemText:O,position:r,isPositioned:I,searchRef:z,children:(0,c.jsx)(tw.A,{as:td,allowPinchZoom:!0,children:(0,c.jsx)(A,{asChild:!0,trapped:C.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(i,e=>{C.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,c.jsx)(b,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>C.onOpenChange(!1),children:(0,c.jsx)(q,{role:"listbox",id:C.contentId,"data-state":C.open?"open":"closed",dir:C.dir,onContextMenu:e=>e.preventDefault(),...E,...X,onPlaced:()=>F(!0),ref:j,style:{display:"flex",flexDirection:"column",outline:"none",...E.style},onKeyDown:(0,a.m)(E.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=M().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout(()=>W(t)),e.preventDefault()}})})})})})})});tG.displayName="SelectContentImpl";var tK=o.forwardRef((e,t)=>{let{__scopeSelect:n,onPlaced:r,...i}=e,a=tj(tB,n),s=tz(tB,n),[d,f]=o.useState(null),[p,m]=o.useState(null),h=(0,u.s)(t,e=>m(e)),g=tS(n),y=o.useRef(!1),w=o.useRef(!0),{viewport:x,selectedItem:b,selectedItemText:E,focusSelectedItem:C}=s,S=o.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&p&&x&&b&&E){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),n=a.valueNode.getBoundingClientRect(),o=E.getBoundingClientRect();if("rtl"!==a.dir){let r=o.left-t.left,i=n.left-r,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),c=l(i,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let r=t.right-o.right,i=window.innerWidth-n.right-r,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),c=l(i,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let i=g(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),m=parseInt(c.paddingTop,10),h=parseInt(c.borderBottomWidth,10),v=f+m+u+parseInt(c.paddingBottom,10)+h,w=Math.min(5*b.offsetHeight,v),C=window.getComputedStyle(x),S=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),P=e.top+e.height/2-10,T=b.offsetHeight/2,A=f+m+(b.offsetTop+T);if(A<=P){let e=i.length>0&&b===i[i.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-P,T+(e?R:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+h);d.style.height=A+t+"px"}else{let e=i.length>0&&b===i[0].ref.current;d.style.top="0px";let t=Math.max(P,f+x.offsetTop+(e?S:0)+T);d.style.height=t+(v-A)+"px",x.scrollTop=A-P+x.offsetTop}d.style.margin="10px 0",d.style.minHeight=w+"px",d.style.maxHeight=s+"px",r?.(),requestAnimationFrame(()=>y.current=!0)}},[g,a.trigger,a.valueNode,d,p,x,b,E,a.dir,r]);(0,e0.N)(()=>S(),[S]);let[R,P]=o.useState();(0,e0.N)(()=>{p&&P(window.getComputedStyle(p).zIndex)},[p]);let T=o.useCallback(e=>{e&&!0===w.current&&(S(),C?.(),w.current=!1)},[S,C]);return(0,c.jsx)(tU,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:y,onScrollButtonChange:T,children:(0,c.jsx)("div",{ref:f,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:R},children:(0,c.jsx)(v.sG.div,{...i,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});tK.displayName="SelectItemAlignedPosition";var t$=o.forwardRef((e,t)=>{let{__scopeSelect:n,align:r="start",collisionPadding:o=10,...i}=e,l=tA(n);return(0,c.jsx)(tr,{...l,...i,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});t$.displayName="SelectPopperPosition";var[tU,tq]=tP(tB,{}),tX="SelectViewport",tY=o.forwardRef((e,t)=>{let{__scopeSelect:n,nonce:r,...i}=e,l=tz(tX,n),s=tq(tX,n),d=(0,u.s)(t,l.onViewportChange),f=o.useRef(0);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),(0,c.jsx)(tC.Slot,{scope:n,children:(0,c.jsx)(v.sG.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,a.m)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if(r?.current&&n){let e=Math.abs(f.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,o=Math.max(parseFloat(n.style.minHeight),parseFloat(n.style.height));if(o<r){let i=o+e,l=Math.min(r,i),a=i-l;n.style.height=l+"px","0px"===n.style.bottom&&(t.scrollTop=a>0?a:0,n.style.justifyContent="flex-end")}}}f.current=t.scrollTop})})})]})});tY.displayName=tX;var tZ="SelectGroup",[tJ,tQ]=tP(tZ),t0=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=(0,k.B)();return(0,c.jsx)(tJ,{scope:n,id:o,children:(0,c.jsx)(v.sG.div,{role:"group","aria-labelledby":o,...r,ref:t})})});t0.displayName=tZ;var t1="SelectLabel",t2=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tQ(t1,n);return(0,c.jsx)(v.sG.div,{id:o.id,...r,ref:t})});t2.displayName=t1;var t5="SelectItem",[t9,t6]=tP(t5),t8=o.forwardRef((e,t)=>{let{__scopeSelect:n,value:r,disabled:i=!1,textValue:l,...s}=e,d=tj(t5,n),f=tz(t5,n),p=d.value===r,[m,h]=o.useState(l??""),[g,y]=o.useState(!1),w=(0,u.s)(t,e=>f.itemRefCallback?.(e,r,i)),x=(0,k.B)(),b=o.useRef("touch"),E=()=>{i||(d.onValueChange(r),d.onOpenChange(!1))};if(""===r)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,c.jsx)(t9,{scope:n,value:r,disabled:i,textId:x,isSelected:p,onItemTextChange:o.useCallback(e=>{h(t=>t||(e?.textContent??"").trim())},[]),children:(0,c.jsx)(tC.ItemSlot,{scope:n,value:r,disabled:i,textValue:m,children:(0,c.jsx)(v.sG.div,{role:"option","aria-labelledby":x,"data-highlighted":g?"":void 0,"aria-selected":p&&g,"data-state":p?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...s,ref:w,onFocus:(0,a.m)(s.onFocus,()=>y(!0)),onBlur:(0,a.m)(s.onBlur,()=>y(!1)),onClick:(0,a.m)(s.onClick,()=>{"mouse"!==b.current&&E()}),onPointerUp:(0,a.m)(s.onPointerUp,()=>{"mouse"===b.current&&E()}),onPointerDown:(0,a.m)(s.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.m)(s.onPointerMove,e=>{b.current=e.pointerType,i?f.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(s.onPointerLeave,e=>{e.currentTarget===document.activeElement&&f.onItemLeave?.()}),onKeyDown:(0,a.m)(s.onKeyDown,e=>{f.searchRef?.current!==""&&" "===e.key||(tb.includes(e.key)&&E()," "===e.key&&e.preventDefault())})})})})});t8.displayName=t5;var t3="SelectItemText",t4=o.forwardRef((e,t)=>{let{__scopeSelect:n,className:r,style:l,...a}=e,s=tj(t3,n),d=tz(t3,n),f=t6(t3,n),p=tD(t3,n),[m,h]=o.useState(null),g=(0,u.s)(t,e=>h(e),f.onItemTextChange,e=>d.itemTextRefCallback?.(e,f.value,f.disabled)),y=m?.textContent,w=o.useMemo(()=>(0,c.jsx)("option",{value:f.value,disabled:f.disabled,children:y},f.value),[f.disabled,f.value,y]),{onNativeOptionAdd:x,onNativeOptionRemove:b}=p;return(0,e0.N)(()=>(x(w),()=>b(w)),[x,b,w]),(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(v.sG.span,{id:f.textId,...a,ref:g}),f.isSelected&&s.valueNode&&!s.valueNodeHasChildren?i.createPortal(a.children,s.valueNode):null]})});t4.displayName=t3;var t7="SelectItemIndicator",ne=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return t6(t7,n).isSelected?(0,c.jsx)(v.sG.span,{"aria-hidden":!0,...r,ref:t}):null});ne.displayName=t7;var nt="SelectScrollUpButton",nn=o.forwardRef((e,t)=>{let n=tz(nt,e.__scopeSelect),r=tq(nt,e.__scopeSelect),[i,l]=o.useState(!1),a=(0,u.s)(t,r.onScrollButtonChange);return(0,e0.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,c.jsx)(ni,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});nn.displayName=nt;var nr="SelectScrollDownButton",no=o.forwardRef((e,t)=>{let n=tz(nr,e.__scopeSelect),r=tq(nr,e.__scopeSelect),[i,l]=o.useState(!1),a=(0,u.s)(t,r.onScrollButtonChange);return(0,e0.N)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,c.jsx)(ni,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});no.displayName=nr;var ni=o.forwardRef((e,t)=>{let{__scopeSelect:n,onAutoScroll:r,...i}=e,l=tz("SelectScrollButton",n),s=o.useRef(null),u=tS(n),d=o.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return o.useEffect(()=>()=>d(),[d]),(0,e0.N)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,c.jsx)(v.sG.div,{"aria-hidden":!0,...i,ref:t,style:{flexShrink:0,...i.style},onPointerDown:(0,a.m)(i.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(r,50))}),onPointerMove:(0,a.m)(i.onPointerMove,()=>{l.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(r,50))}),onPointerLeave:(0,a.m)(i.onPointerLeave,()=>{d()})})}),nl=o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e;return(0,c.jsx)(v.sG.div,{"aria-hidden":!0,...r,ref:t})});nl.displayName="SelectSeparator";var na="SelectArrow";function ns(e){return""===e||void 0===e}o.forwardRef((e,t)=>{let{__scopeSelect:n,...r}=e,o=tA(n),i=tj(na,n),l=tz(na,n);return i.open&&"popper"===l.position?(0,c.jsx)(tl,{...o,...r,ref:t}):null}).displayName=na;var nu=o.forwardRef((e,t)=>{let{value:n,...r}=e,i=o.useRef(null),l=(0,u.s)(t,i),a=(0,tv.Z)(n);return o.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==n&&t){let r=new Event("change",{bubbles:!0});t.call(e,n),e.dispatchEvent(r)}},[a,n]),(0,c.jsx)(tg,{asChild:!0,children:(0,c.jsx)("select",{...r,ref:l,defaultValue:n})})});function nc(e){let t=(0,g.c)(e),n=o.useRef(""),r=o.useRef(0),i=o.useCallback(e=>{let o=n.current+e;t(o),function e(t){n.current=t,window.clearTimeout(r.current),""!==t&&(r.current=window.setTimeout(()=>e(""),1e3))}(o)},[t]),l=o.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return o.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,i,l]}function nd(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let l=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}nu.displayName="BubbleSelect";var nf=tO,np=tM,nm=tF,nh=tH,nv=tW,ng=tV,ny=tY,nw=t0,nx=t2,nb=t8,nE=t4,nC=ne,nS=nn,nR=no,nP=nl},66582:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(58009);function o(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},38762:(e,t,n)=>{n.d(t,{X:()=>i});var r=n(58009),o=n(49397);function i(e){let[t,n]=r.useState(void 0);return(0,o.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},24999:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(94825).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},7833:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(94825).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},36624:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(94825).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}};